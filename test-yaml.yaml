# Meta Studio複雑なYAML構造テスト用ファイル（完全版）
meta_studio:
  project:
    name: "MetaStudio Platform"
    version: "2.1.3"
    type: "full-stack-platform"
    status: "production"
    repository:
      url: "https://github.com/meta-studio/platform"
      branch: "main"
      commit: "a1b2c3d4"
      tags:
        - "v2.1.3"
        - "stable"
        - "production"
    
  environment:
    development:
      database:
        host: "localhost"
        port: 5432
        name: "metastudio_dev"
        credentials:
          username: "dev_user"
          password: "dev_pass"
          ssl: false
      api:
        base_url: "http://localhost:8080"
        timeout: 30000
        retry_attempts: 3
        allowed_origins:
          - "http://localhost:3000"
          - "http://localhost:8080"
          - "http://127.0.0.1:3000"
      cache:
        redis:
          host: "127.0.0.1"
          port: 6379
          db: 0
    
    production:
      database:
        host: "prod-db.example.com"
        port: 5432
        name: "metastudio_prod"
        credentials:
          username: "prod_user"
          password: "ENCRYPTED"
          ssl: true
      api:
        base_url: "https://api.metastudio.com"
        timeout: 15000
        retry_attempts: 5
        allowed_origins:
          - "https://metastudio.com"
          - "https://www.metastudio.com"
          - "https://app.metastudio.com"

agents:
  hierarchy:
    god:
      name: "グランドデザイナー"
      level: 0
      permissions:
        - "*"
      capabilities:
        - "strategic_planning"
        - "resource_allocation"
        - "vision_setting"
        - "architecture_design"
      models:
        primary: "claude-3-opus"
        fallback: "gpt-4"
        settings:
          temperature: 0.7
          max_tokens: 4096
          top_p: 0.9
    
    king:
      name: "プロジェクトマネージャー"
      level: 1
      permissions:
        - "project:*"
        - "team:*"
      capabilities:
        - "project_management"
        - "team_coordination"
        - "quality_assurance"
        - "timeline_management"
      models:
        primary: "claude-3-sonnet"
        fallback: "gpt-3.5-turbo"
        settings:
          temperature: 0.5
          max_tokens: 2048
    
    general:
      name: "開発リーダー"
      level: 2
      permissions:
        - "code:*"
        - "review:*"
        - "deploy:staging"
      capabilities:
        - "code_generation"
        - "code_review"
        - "architecture_design"
        - "debugging"
      models:
        primary: "claude-3-haiku"
        settings:
          temperature: 0.3
          max_tokens: 1024
    
    soldier:
      name: "実装エージェント"
      level: 3
      permissions:
        - "task:execute"
        - "test:run"
      capabilities:
        - "task_execution"
        - "testing"
        - "documentation"
        - "bug_fixing"

development:
  stack:
    frontend:
      framework: "Next.js"
      version: "15.0.0"
      language: "TypeScript"
      ui_library: "DaisyUI"
      state_management: "React Context"
      bundler: "Turbopack"
      dependencies:
        - "react@18.2.0"
        - "next@15.0.0"
        - "typescript@5.0.0"
        - "tailwindcss@3.4.0"
        - "daisyui@4.0.0"
    
    backend:
      runtime: "Node.js"
      version: "22.0.0"
      framework: "Express"
      database: "PostgreSQL"
      orm: "Prisma"
      cache: "Redis"
      dependencies:
        - "express@4.18.0"
        - "prisma@5.7.0"
        - "redis@4.6.0"
        - "jsonwebtoken@9.0.0"
    
    tools:
      package_manager: "bun"
      monorepo: "Turborepo"
      testing: "Vitest"
      e2e: "Playwright"
      linting: "ESLint"
      formatting: "Prettier"
      ci_cd: "GitHub Actions"
      monitoring:
        - "Sentry"
        - "DataDog"
        - "New Relic"

features:
  core_systems:
    tab_management:
      max_tabs: 18
      split_panes: true
      drag_drop: true
      persistence: true
      status: "implemented"
      supported_formats:
        - "yaml"
        - "json"
        - "markdown"
        - "typescript"
        - "javascript"
    
    file_management:
      explorer: true
      yaml_viewer: true
      markdown_editor: true
      real_time_editing: true
      status: "implemented"
      supported_operations:
        - "create"
        - "read"
        - "update"
        - "delete"
        - "rename"
        - "copy"
        - "move"
    
    vrm_integration:
      model_upload: true
      animation: true
      lip_sync: true
      expressions: true
      status: "beta"
      supported_formats:
        - ".vrm"
        - ".glb"
        - ".gltf"
    
    ai_chat:
      models:
        - "claude-3-opus"
        - "claude-3-sonnet"
        - "claude-3-haiku"
        - "gpt-4"
        - "gpt-3.5-turbo"
      context_memory: true
      multi_character: true
      voice_input: true
      status: "development"
      features:
        - "context_retention"
        - "multi_turn_conversation"
        - "code_generation"
        - "document_analysis"

  advanced_features:
    browser_automation:
      engine: "playwright"
      visual_control: true
      llm_integration: true
      status: "planned"
      supported_browsers:
        - "chromium"
        - "firefox"
        - "webkit"
    
    voice_streaming:
      real_time: true
      tiktok_integration: true
      obs_integration: true
      status: "planned"
      supported_codecs:
        - "opus"
        - "aac"
        - "mp3"

budget:
  development:
    salaries: 2400000
    infrastructure: 150000
    tools: 80000
    licenses: 50000
    total: 2680000
  
  marketing:
    advertising: 300000
    content_creation: 120000
    influencer: 200000
    seo: 80000
    total: 700000
  
  operations:
    server_costs: 50000
    third_party_apis: 30000
    maintenance: 40000
    support: 20000
    total: 140000
  
  grand_total: 3520000

infrastructure:
  deployment:
    platform: "Vercel"
    domains:
      - "metastudio.com"
      - "api.metastudio.com"
      - "cdn.metastudio.com"
    ssl: true
    cdn: "Cloudflare"
    regions:
      - "us-east-1"
      - "eu-west-1"
      - "ap-northeast-1"
  
  monitoring:
    analytics: "Google Analytics"
    error_tracking: "Sentry"
    performance: "Web Vitals"
    uptime: "Pingdom"
    custom_metrics:
      - "user_engagement"
      - "api_response_time"
      - "conversion_rate"
  
  security:
    authentication: "NextAuth.js"
    authorization: "RBAC"
    encryption: "AES-256"
    rate_limiting: true
    security_headers:
      - "CSP"
      - "HSTS"
      - "X-Frame-Options"
      - "X-Content-Type-Options"

metadata:
  created_at: "2024-01-15T10:30:00Z"
  updated_at: "2024-12-17T15:45:30Z"
  created_by: "god-agent"
  updated_by: "king-agent"
  schema_version: "2.1"
  validation:
    required_fields:
      - "project.name"
      - "project.version"
      - "agents.hierarchy"
    optional_fields:
      - "budget.marketing"
      - "infrastructure.monitoring"
    constraints:
      version_format: "semantic"
      status_values:
        - "development"
        - "beta"
        - "production"
        - "deprecated"
  contributors:
    - name: "Claude Code"
      role: "AI Assistant"
      contributions: 150
    - name: "Human Developer"
      role: "Lead Developer"
      contributions: 89