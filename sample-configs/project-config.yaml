# メタスタジオプロジェクト設定ファイル

project:
  name: "瞑想アプリ"
  version: "1.0.0"
  type: "mobile"
  status: "development"
  description: "心を落ち着かせる瞑想タイマーアプリ"

team:
  lead: "王子-meditation"
  developers:
    - name: "将軍-meditation"
      role: "frontend"
      skills: ["React Native", "Audio API", "UI実装"]
    - name: "兵士-UI-001"
      role: "ui_ux"
      skills: ["React", "CSS", "デザイン"]
    - name: "兵士-Test-001"
      role: "qa"
      skills: ["Testing", "QA", "Automation"]

features:
  core:
    - name: "タイマー機能"
      status: "development"
      priority: "high"
      components:
        - "タイマー画面"
        - "音声ガイド"
        - "プリセット時間"
    - name: "プログレス管理"
      status: "planning"
      priority: "medium"
      components:
        - "履歴記録"
        - "統計表示"
        - "目標設定"

technology:
  frontend:
    framework: "React Native"
    ui_library: "DaisyUI"
    navigation: "React Navigation"
  backend:
    database: "SQLite"
    audio: "Audio API"
  deployment:
    ios: "App Store"
    android: "Google Play"

timeline:
  milestones:
    - name: "MVP完成"
      date: "2025-07-01"
      deliverables:
        - "基本タイマー機能"
        - "5分・10分・15分プリセット"
    - name: "ベータ版リリース"
      date: "2025-08-01"
      deliverables:
        - "音声ガイド追加"
        - "プログレス記録"
    - name: "正式リリース"
      date: "2025-09-01"
      deliverables:
        - "全機能完成"
        - "ストア公開"

budget:
  development: 500000
  marketing: 100000
  deployment: 50000
  total: 650000
  currency: "JPY"

requirements:
  functional:
    - "5分・10分・15分のタイマープリセット"
    - "カスタムタイマー時間設定"
    - "音声ガイダンス機能"
    - "バックグラウンド動作"
    - "プログレス履歴記録"
  non_functional:
    - "起動時間3秒以内"
    - "バッテリー消費最小化"
    - "オフライン動作対応"
    - "アクセシビリティ対応"

risks:
  - name: "音声API制限"
    probability: "medium"
    impact: "high"
    mitigation: "代替音声ライブラリ準備"
  - name: "バックグラウンド制限"
    probability: "high"
    impact: "medium"
    mitigation: "プッシュ通知で代替"