ーーー

（未完了）順番は任せる。重要度、簡単度で優先を決めてバレットポイントやタスクマークで管理して、完了したものは確認に移動、それを僕がチェックしたら完了に移動。

下記で完了しているものは無視していい。ちゃんと調査して複雑にしないように慎重に確認してからお願い。
一旦下記をみたらこのmdファイルも要素は絶対に消さず整理していいよ。似たタスクはまとめてもいいし。

ディレクトリツリーはシステムの上だし、vscodeのようにファイルツリーにしてよ。

システム
    エディター
    インボックス
    AIキャラクター
    ブラウザ自動化
    対談スタジオ
    backups
ーー
アプリドック
エージェント管理
プラグイン
統計
Expo QR
設定

設定はもう少し高い位置。順序はOK。バックアップはカタカナに。他は最適な順にソートして。システムカテゴリとーー以下のの違いがいまいちわからない。ディレクトリツリー（フォルダやノート）の欄も作って。そこはシステムの上

エージェントペルソナ例の定義を各世界観で選択して、自認できるようにする。カスタムも可能。デフォは一番左のやつ。ユニーク命名も可能（⚪︎くんですなど）
神様　神　ユーザー　Owner グランドデザイナー　父　CEO
王子　王　皇帝　ボス　王様 COO president　/　女王　女帝　母　CTO　
将軍　将　マネージャ　子 CXO
兵士　兵　ワーカー　孫 employer

☑️ダッシュボードのタブは左上のデフォルト位置に最優先固定。ピンマークなどの概念はこの要素だけ不要。
☑️サイドバーのアプリドックを開くと、クイックランチャーというタイトルになっているから、それをアプリドックに変更。クイックアクション要素、最近のコマンドは、右下のロケットの要素に移動してマージ。アプリドック自体はアイコンが羅列されてる、メタスタジオ内でのツールボックス集にしたい。あるいみOSのデスクチップ的なイメージ？
・サイドバー左下のボタンは設定が一番した。さらに一番下の要素がnextのマークに被ってるからもう少し高い位置が最下部になるようにして。
・ブラウザ自動化ツールは、ブラウザで実際にgoogleなどをデフォルトで表示させておいて欲しい。UIが微妙なので洗練させて。
・タブを増やすと、右のサイドバーがずれる。九個以上は二段にして。いまは六個で二段になってる。
☑️タイムブロックは伸ばしても伸ばしたサイズで固定されない。時間幅の大きさにして。またブロックの選択はできても並び替えができない。時間も入力じゃなくてトグルみたいにモダンにして。スマートに。色の変更もできない。プラスとマイナスは何？
・所々、ボタンがあるだけでプレースホルダーになっている要素のピックアップと解消。
・右クリックによる拡張ダイアログが必要な要素があればお願い。
☑️開発中や完成してるアプリアイコンが集まる場所の名称はドックに変えよう。アイコンサイズ少し小さくしてね。いまあるランチャーは別の意味になってるので分けます。定義を明確にして説明して。
・チャットとキャラクターが同期して喋るようにしてほしい。
・キャラクター設定の中にモデル一覧を内包して。
・メタスタジオアイコンを押して表示されるパーソナルダッシュボードはタブの一番左のホームにして、いまホームに表示されてるプロジェクト管理は、サイドバーのプロジェクト管理の管理を押したら表示させるようにできる？リロードすると旧ホームがでちゃう。
☑️タブの固定ピン機能。ドクロでも消えないやつにして。セッションも記憶。
・claude codeの作業完了の通知音を設定したい。どのファイルの音源？変更できる？そして今は通知音ならない。安定させて
・claude codeにてパーミッション許可の設定。まだ度々確認される。
・wiki　FAQ ナレッジベース　知識　セカンドブレイン　notebookLM。中央集権的に作ったプロジェクトファイル群を参照してLLMが開発を進める機構。
・キャラ、RAG 記憶メモリ設定、配信obs連携、tiktok,instalive youtubeliveなどサイマル配信
・RTD中央情報とのリアルタイム参照機構を整理したい。ユーザーとaiはそれぞれその中央管理システムプロジェクストにアクセスして、齟齬がないように開発を進めるイメージ。
・モバイルからの独自ビューで音声入力のテストとデータの閲覧快適度のチェック。claude code github actionをつかい、
  ジャストアイデアを音声で入れ、要件定義を対話で拡充し、送信したらバックグラウンドで並行して、リポジトリを作り、git worktreesなどをつかって、複数のブランチを立てて、プロトタイプMVPを作れる仕組みが理想。
・デザインに関しての依頼を刹那的な瞬間の解決に頼らず、根本で一元的に管理しているものを編集して、
  いろんなところからcssを指定することのないようにきちんと確認してから実装するようにして
・メインパネルでプロジェクト名ダブルクリックで編集
・WYSWYGエディタの行並び替え機能、  実装したwyswigエディタがcraftのように直感的に行を選択する機能がない。あとプロジェクトファイルを編集する際もプレビューと編集で分かれてるからデフォルトはリアルタイム編集。crafteditorって名称やめない？editorで。
・変更履歴機能（v0.0.1刻み）実装はされてるけど、0.0.1刻みで更新時にきちんと記入されてないかも？自動で更新はできない？
・ディレクトリ、フォルダ、シンプルに新規で作ったノートの管理と自動ふりわけ機能について。インボックスの仕組み
・右下のランチャーをひらいて新規アプリ作成をしても、一度閉じてもう一度おさないとそのウインドウが表示されない
謎のジャーン音は、cursorで、tab補完をすると音が鳴る。いらない。
・yamlエディタの際にセルのように構造が視認性高く表示される専用ビューは実装したんだろうけどどこで確認できるの？
  project-config.yamlとかを開いてもなにもかわってないよ？yaml2tableの指示覚えてる？
・資料のテンプレートにプレゼン、marp、manimの文言追加。あとpodcastも。士業系の項目も足して。ライター業務も何処かに足して。
・キャラは読み込み完了と表示されるだけでモデルが出現しない。読み込んでも表示されない。アクティブと出るだけ。
・また、キャラ設定はトンカチマークをクリックしたら設定要素を展開するように。デフォは折り畳まれてる。モデル一覧やポーズリセット
・新案で追記して欲しいのはブラウザ機能（puppeteerかscrapybaraかplayweightなど壁打ちして選定）でllmがディスプレイを見ながら発言や操作ができる機能。
・あとキャラを複数追加できるように。そして片方は僕がVTUBERとなり中でAITUBERと話せるイメージ。その対談などを録画できる機能。
・それぞれのビューでビュータイプをいくつか選べるようにして欲しい。リスト表示はどれも必須。
新規プロジェクトつくってもサイドバーに表示されない。
☑️タイムブロックで、時間幅の設定も直感的にクリックして伸ばすみたいな感じできる？
・アプリアイコンにボーダー外枠で視認性高める。アプリカラーと同期。外枠がいまは白になってる。あとアプリカラーが設定されていないのもある。進捗ゲージがアプリに内包されてるのやめて。は
・要件定義.mdだけじゃなくてそれぞれのテンプレートに則した絶対に必要なテンプレート書類を複数そろえて。現状全部思考停止で同じフォーマットになってる。
   中身も思考して記載して。yamlやmermaidが適していたらそれも含めていい。yaml構造など適したファイル形式で対話で埋めるべき項目と共に作って。
チャットクライアントなりでのやり取りの履歴確認、line,slack,マザーチャットにて、どれかを壁打ちしたい。
・チャットセッションが保存されてないのはダミーデータだから？入力欄もリサイズできるようにして。デフォルトもう少し高さ欲しい。あと相手の返答も長いと端折られる。
・従来との変更点は注意書きで追記。今までの作業で重要だったことはメタスタジオ要件定義に記して。
・重複してカオスになっている機能や、実装したけどつか荒れてない機能はないか、バグとりとともに調査して。
・全体的に未消化のタスクはない？要件定義を今一度見て、次に取り掛かるべきところを整理して教えて。なお勝手に実装は時始めずに確認してからにして。過去に指示して抜けてるものない？
・治っていないところがあるかもだから深く考えて直して。さらに追加できるアイデアや洗練させるべきところ、実装するべきところの壁打ちと整理。全体を見て質問があったらしてほしい、それで最終を煮詰めていきたい。
・一度githubにコミットして保存。いままでに指示して実装したかなり粒度の細かい詳細な記録を要件定義に追記。勝手に要素は絶対に消さない。

--

（統合したい）一旦無視して
  claude code　設定エクスポート
  scarapybara
  ccusage
  stagewise
  dia
  taskmaster
  code rabbit
  vscode エクステンションパロディ、マーケットプレイス

--

（アプリアイデア）  アイデア入れて実際にアプリつくってみる。 一旦無視して
  スタンプ
  宣材
  麻雀の最適解
  即刻ベンチマーク機能 新規LLMでたときに最速


--

（あとまわし）一旦無視して
  登録ユーザーサインインで記憶。データベースに保存されてる？それともメモリで単に保存されている？
  mastra,voltagentなど新規エージェントのお試しプレイグラウンド
  ローカル開発について
  Iot SmartGlass 次世代開発手法
  roo,subtask,boomerang,CCA,gitworktree,zellij、TDD設計、水車開発
  ユーザーカスタムAPIを設定する欄
  全体的に立体感デザイン。トランスルーセント
  GIT革新システムつくる。ツリーでビジョンとしてみられるように。。
  a2a,mcp、他サービスapi連携　comfyなど
  API料金マージン内包マネタイズ


--

（実装したがユーザー確認前）タスクリストを消化したら一どこここに移動して。

ーー

（指示済みで完了。ユーザー確認後）チェックが終わったらものはここに移動して。
☑️バックアップの開発
☑️faviconをロボエージェントに変えて。
☑️サイドバーのシステムを押して、メインペインに表示されるのは開発中。タブをもう一度押したりすると中身が表示される。
☑️メタスタジオ要件定義をみてリードミーの整合性破綻を修正して。
☑️ターミナルペインのリサイズハンドルがうまく動かない。旧、ターミナルペインが縮小なりいろんな動きをすると、下部がみきれたりする。ターミナルのリサイズハンドラが下辺にあるから、ターミナルの上辺にして
☑️claude codeにてcontext 7のインストールはできてる？
☑️チャットの入力送信ボタンと音声ボタンが横並びになっているから縦並びにして、入力ウインドウと同じ高さにして。
☑️github確認したけど、commitが１だけで増えてないよ？確認して。あと完全なリードミーも入れて。
☑️メタスタジオのアイコンをもうすこし大きくして
☑️タブの全消しを一番右上に髑髏マークで設置
☑️中央のメインパネルとターミナルだけレスポンシブになりきれてない。
☑️ソート機能強化
☑️各エージェントは現状どこから何にアクセスできるか教えて。claude code,mastraそれぞれ。
☑️プロジェクト開いても関連ファイルがメインパネルに出ない。
☑️左上のサイドバーのメタスタジオロゴを押したら、ユーザーのデータベースを俯瞰して表示する
☑️サイドバーのボタンというか要素ブロックの背景(ブロック事体のこと、サイドバーの下地背景ではない。)は黒にしよう。
☑️ランチャーで開いてた内容をきちんと表示させるようにして。また作っている最中、もしくは完成したアプリはボーダーで仕分けて、iosのアプリアイコンのように表示させて 
☑️チャットをエンターで送信するようにして。option エンターで送信
☑️チャットで要件定義プロジェクストを作るようなコアプロンプトを設定したいし、設定マークを設置して欲しい。チャットウインドウの右上とかに。
☑️全体的にサイドバー左下の要素全て、ランチャーのように重複しない限り新規タブでメインペインに出すようにして。
☑️メインパネル・ターミナルのレスポンシブ対応
☑️テンプレートから作成機能
☑️統計・プラグイン・エージェント管理の実装
☑️右サイドバーリサイズハンドラ位置調整
☑️タブが増えすぎると改行で高さが変わってレイアウトが崩れる。
☑️リサイズハンドラで調節したサイズなどが記憶されていない。
☑️ボタンは今のまま黒でいいけどその背景は全体的にダークグレーにして視認性を高めて。
☑️バージョン管理とロールバックはどこからできるの？
☑️星マークをクリックで変更できるように。



ーーーーーーー


（ウォッチリスト競合ライバルプロダクト）完全に無視していい

  神威/KAMUI
  Taiyo/open super agent
  HIBIKI/amatsu git  
  fox code /しとちゃ  
  orbic / るるむ　
  dreamcore　/りく
  AItuberKit/ニケちゃん
  maki@sunwood
  安東竜平
  ひろちゅー
  shinkaman
  あきらパパ
  kk@study
  ハカセアイ
  KAI
  torishima
  GOROMAN nullsensei
  R5
  mizchi
  のちうだよ
  まさお
  ブローリー
  ARB@
  えびまんじゅう
  えどいち
  ぬこぬこ
  koichi nishizuka
  ぽめ
  tesla0225
  divisionN
  NEW
  しば田
  炎鎮
  きのぴー
  らいあん
  深津
  OMG
  AKAGAMI
  まつにぃ
  左角
  yusan
  神の子KID
  ミロ
  promptpedia
  DOG
  黒ビール
  コタ
  PARK
  チャエン
  うまみ
  サトリ
  ryuhei
