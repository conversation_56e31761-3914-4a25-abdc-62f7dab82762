# Meta Studio - 脳内現実化ツール + Projext統合開発フレームワーク

[![Next.js](https://img.shields.io/badge/Next.js-15.3.3-black?logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-19.0.0-blue?logo=react)](https://react.dev/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.6.3-blue?logo=typescript)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-4.0.0-blue?logo=tailwindcss)](https://tailwindcss.com/)
[![DaisyUI](https://img.shields.io/badge/DaisyUI-5.0.0-green?logo=daisyui)](https://daisyui.com/)

## 概要

Meta Studioは脳内で思い描いたアイデアを即座に現実化するための統合開発プラットフォームです。AI駆動のプロジェクト管理、Projext統合フレームワーク、リアルタイムコラボレーション、音声制御、3Dキャラクターとの対話など、革新的な機能を備えています。

**正式名称**: メタスタジオ（脳内現実化ツール進化版）+ Projext統合開発フレームワーク  
**アイコン**: 😎🤖 サングラス + ビジネススーツロボット  
**コンセプト**: アイデアから実装まで、あらゆる創造的願望を自動実現するオールラウンド開発プラットフォーム + LLM駆動プロジェクト管理システム

### 🌟 主要機能

#### 🚀 統合開発環境
- **Next.js 15** + **React 19** による最新の開発環境
- **Turbopack** によるリアルタイム高速ビルド
- **TypeScript** による型安全性
- **Tailwind CSS v4** + **DaisyUI v5** によるモダンUI

#### 🤖 AI駆動プロジェクト管理
- 階層型AIエージェントシステム（神→王/母→将→兵）
- **Projext統合フレームワーク**: LLM可読な統一プロジェクト管理システム
- **SSoT（Single Source of Truth）**：統一的なプロジェクト構造管理
- 自動プロジェクト生成・管理
- インテリジェントタスク配分
- リアルタイム進捗追跡

#### 💰 包括的ライフ管理
- **金融管理**: キャッシュフロー、収入、投資、財務サマリー
- **タイムブロック**: 直感的なドラッグ&リサイズ時間管理
- **健康管理**: 睡眠、水分、歩数トラッキング
- **習慣トラッカー**: 継続的成長の可視化

#### 🎭 3D AIキャラクター
- VRM形式3Dモデル対応
- リアルタイム音声対話
- マルチキャラクター対談スタジオ
- 表情・アニメーション制御

#### 🎤 音声制御システム
- 連続音声入力・音声コマンド
- 音声インボックス管理
- 多言語音声認識
- リアルタイム音声合成

#### 📊 高度な分析・可視化
- プロジェクト完成率分析
- エージェント効率メトリクス
- パーソナル生産性インサイト
- カスタマイズ可能ダッシュボード

## 🏗️ アーキテクチャ

```
meta-studio/
├── apps/
│   ├── web/          # Next.js Webアプリケーション
│   └── mobile/       # React Native Expoアプリ
├── packages/         # 共有パッケージ
├── templates/        # プロジェクトテンプレート
├── knowledge/        # AIナレッジベース
└── backups/          # 自動バックアップ
```

### 技術スタック

**フロントエンド**
- Next.js 15.3.3 (App Router)
- React 19.0.0 (最新機能活用)
- TypeScript 5.6.3
- Tailwind CSS v4.0.0
- DaisyUI v5.0.0 beta

**バックエンド**
- tRPC (型安全API)
- Supabase (データベース)
- Vercel AI SDK
- WebSocket (リアルタイム通信)

**モバイル**
- React Native
- Expo SDK 52
- 3D Graphics (Three.js)

**AI・エージェント**
- **Claude Code SDK**: 神↔王対話システムのコア、Projextタスク実行エンジン
- **Mastraフレームワーク**: マルチエージェント管理システム、将・兵エージェントの生成・配置・管理
- **Projext統合**: LLM可読な統一ファイル構造、バージョン管理対応
- Web Speech API
- 音声合成API
- VRM 3Dモデル

## 🚀 クイックスタート

### 前提条件
- **Node.js 22 LTS** (推奨) または **Bun** (高速開発)
- **pnpm 8.0+** (モノレポ管理)
- Git
- **Claude Code SDK** (AI駆動開発)
- **Turborepo** (モノレポ統合)

### インストール

```bash
# リポジトリをクローン
git clone https://github.com/cgrikt/meta-studio.git
cd meta-studio

# 依存関係をインストール
pnpm install

# 環境変数設定
cp .env.example .env.local
# .env.localファイルを編集してAPIキーを設定

# 開発サーバー起動
cd apps/web
npm run dev
```

ブラウザで http://localhost:8080 を開いてMeta Studioにアクセス

### モバイルアプリ（iOS/Android）

```bash
cd apps/mobile
npx expo start
```

Expo GoアプリでQRコードをスキャンしてモバイル版を起動

## 📱 主要画面

### 🏠 パーソナルダッシュボード
- リアルタイム統計とメトリクス
- カスタマイズ可能なウィジェットグリッド
- 金融管理・健康管理の統合ビュー

### 🎯 プロジェクト管理
- ドラッグ&ドロップによる直感的操作
- AIによる自動プロジェクト生成
- リアルタイム進捗トラッキング

### 🤖 エージェント管理
- 階層型AIエージェントシステム
- タスク自動配分・実行
- パフォーマンス分析

### 🎭 AIキャラクター
- 3D VRMモデル表示
- 音声対話・表情制御
- マルチキャラクター対談

### 📊 統計・分析
- プロジェクト完成率分析
- 生産性インサイト
- カスタムメトリクス

## 🔧 開発者向け情報

### スクリプト

```bash
# 開発サーバー起動（バックグラウンド）
npm run dev:start

# 開発サーバー停止
npm run dev:stop

# ログ確認
npm run dev:log

# 本番ビルド
npm run build

# 型チェック
npm run type-check

# リント
npm run lint
```

### 設定ファイル

- `tailwind.config.js` - CSS設計・テーマ統一管理
- `next.config.js` - Next.js設定・Turbopack最適化
- `tsconfig.json` - TypeScript設定
- `CLAUDE.md` - Claude Code指示書・開発ガイドライン

### 重要な技術情報

**サーバー起動について**
- 開発サーバーは http://localhost:8080 で起動
- バックグラウンド起動スクリプトでプロセス管理
- macOS port 3000制限を回避

**CSS設計方針**
- DaisyUIテーマ設定で色・サイズを統一管理
- 個別コンポーネントでのスタイル指定を最小限に
- 確認必須：デザイン変更前に現在設定を確認

## 📊 プロジェクト統計

- **コンポーネント**: 25+個の高機能Reactコンポーネント
- **機能領域**: 8つの主要機能（プロジェクト、AI、音声、3D、金融等）
- **ページ**: 10+個の専用ページ
- **TypeScript**: 完全型安全実装
- **テスト**: 主要機能のテストカバレッジ

## 🗺️ ロードマップ

### Phase 1: 基盤完成 ✅
- [x] 基本UIフレームワーク
- [x] プロジェクト管理基盤  
- [x] AI統合基盤
- [x] Projext統合フレームワーク実装

### Phase 2: 高度機能 ✅
- [x] 3D AIキャラクター
- [x] 音声制御システム  
- [x] 金融管理システム
- [x] タイムブロック洗練
- [x] 階層型AIエージェントシステム

### Phase 3: 統合・自動化 🚧
- [x] Claude Code SDK統合
- [x] Mastraエージェント管理
- [ ] Boost.space連携（外部ツール統合）
- [ ] 自動収益化システム（Polar決済）
- [ ] パフォーマンス最適化

### Phase 4: 拡張・スケール 🔮
- [ ] プラグインエコシステム
- [ ] Zellij王国（マルチプロジェクト並行管理）
- [ ] モバイル音声特化機能
- [ ] クラウド同期・コラボレーション

## 🤝 コントリビューション

### 開発フロー

1. Issueの作成・確認
2. フィーチャーブランチでの開発
3. Pull Requestの作成
4. レビュー・マージ

### コーディング規約

- TypeScript strict mode
- ESLint + Prettier
- コンポーネント設計：Single Responsibility
- 状態管理：適切な抽象化レベル

### コミットメッセージ

```
feat: 新機能追加
fix: バグ修正  
docs: ドキュメント更新
style: コードスタイル修正
refactor: リファクタリング
test: テスト追加・修正
```

## 📄 ライセンス

MIT License - 詳細は [LICENSE](LICENSE) ファイルを参照

## 🔗 関連リンク

- [技術仕様書](./CLAUDE.md)
- [UIライブラリ比較](./UI_LIBRARY_COMPARISON.md)
- [要件定義書](./メタスタジオ統合完全要件定義書.md)
- [機能ロードマップ](./apps/web/FEATURE_ROADMAP.md)

## 📞 サポート

質問・バグ報告・機能要望は [GitHub Issues](https://github.com/cgrikt/meta-studio/issues) までお願いします。

---

**Meta Studio** - Powered by AI, Designed for Creators ✨