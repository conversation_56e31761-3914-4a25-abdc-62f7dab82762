// VRMアップロードテスト用JavaScript
console.log('🧪 VRMアップロードテスト開始');

// sample.vrmファイルを読み込んでFileオブジェクトを作成
fetch('/sample.vrm')
  .then(response => response.arrayBuffer())
  .then(arrayBuffer => {
    console.log('📁 VRMファイル読み込み完了:', arrayBuffer.byteLength, 'bytes');
    
    // FileオブジェクトとしてVRMファイルを作成
    const file = new File([arrayBuffer], 'sample.vrm', { type: 'application/octet-stream' });
    console.log('📦 Fileオブジェクト作成:', file.name, file.size, 'bytes');
    
    // CharacterContextのuploadVRMModelForAgent関数を直接呼び出し
    if (window.uploadVRMModelForAgent) {
      console.log('🚀 uploadVRMModelForAgent実行開始');
      window.uploadVRMModelForAgent('mother-cto', file)
        .then(() => {
          console.log('✅ VRMアップロード完了');
        })
        .catch(error => {
          console.error('❌ VRMアップロードエラー:', error);
        });
    } else {
      console.error('❌ uploadVRMModelForAgent関数が見つかりません');
      console.log('🔍 利用可能な関数:', Object.keys(window).filter(key => key.includes('VRM') || key.includes('upload')));
    }
  })
  .catch(error => {
    console.error('❌ VRMファイル読み込みエラー:', error);
  });
