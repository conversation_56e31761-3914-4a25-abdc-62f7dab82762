#!/bin/bash

# Meta Studio 開発サーバー起動スクリプト
# バックグラウンドで持続実行し、Claude Codeのタイムアウト問題を回避

PROJECT_DIR="/Users/<USER>/Dev/meta-studio"
WEB_DIR="$PROJECT_DIR/apps/web"
PID_FILE="$PROJECT_DIR/.dev-server.pid"
LOG_FILE="$PROJECT_DIR/.dev-server.log"

# 既存プロセスをチェックして停止
if [ -f "$PID_FILE" ]; then
    OLD_PID=$(cat "$PID_FILE")
    if ps -p $OLD_PID > /dev/null 2>&1; then
        echo "既存の開発サーバー (PID: $OLD_PID) を停止中..."
        kill $OLD_PID
        sleep 2
    fi
    rm -f "$PID_FILE"
fi

# ポート8080を使用しているプロセスを確認・終了
PORT_PID=$(lsof -ti:8080 2>/dev/null)
if [ ! -z "$PORT_PID" ]; then
    echo "ポート8080を使用中のプロセス (PID: $PORT_PID) を終了中..."
    kill $PORT_PID
    sleep 2
fi

# 開発サーバーをバックグラウンドで起動
echo "Meta Studio 開発サーバーを起動中..."
cd "$WEB_DIR"

# bunを使用してバックグラウンド起動
nohup bun run dev > "$LOG_FILE" 2>&1 &
SERVER_PID=$!

# PIDを保存
echo $SERVER_PID > "$PID_FILE"

# 起動確認（最大30秒待機）
echo "サーバー起動を確認中... (PID: $SERVER_PID)"
for i in {1..30}; do
    if curl -s http://localhost:8080 > /dev/null 2>&1; then
        echo "✅ Meta Studio 開発サーバーが正常に起動しました！"
        echo "🌐 URL: http://localhost:8080"
        echo "📁 PIDファイル: $PID_FILE"
        echo "📋 ログファイル: $LOG_FILE"
        echo ""
        echo "サーバーを停止するには: $PROJECT_DIR/stop-dev.sh"
        exit 0
    fi
    sleep 1
done

echo "❌ サーバーの起動確認に失敗しました"
echo "📋 ログを確認してください: tail -f $LOG_FILE"
exit 1