# CLAUDE.md - Claude Code への指示書

このファイルには、Claude Codeがこのプロジェクトで作業する際に従うべき指示とルールが記録されています。

## 通知ルール
**必須**: あらゆるタスク完了時は必ず通知を送信してください。例外はありません。通知内容は実行したタスクに応じて適切に記述すること。

### 通知テンプレート（タスク別サウンド）

#### 1. ファイル編集完了
```bash
afplay /System/Library/Sounds/Tink.aiff & osascript -e 'display notification "📝 ファイル編集完了: [ファイル名]" with title "Claude Code"'
```

#### 2. ビルド・コンパイル完了
```bash
afplay /System/Library/Sounds/Hero.aiff & osascript -e 'display notification "🔨 ビルド完了" with title "Claude Code"'
```

#### 3. テスト実行完了
```bash
afplay /System/Library/Sounds/Glass.aiff & osascript -e 'display notification "✅ テスト実行完了 ([結果])" with title "Claude Code"'
```

#### 4. 検索・分析完了
```bash
afplay /System/Library/Sounds/Ping.aiff & osascript -e 'display notification "🔍 検索・分析完了" with title "Claude Code"'
```

#### 5. インストール・設定完了
```bash
afplay /System/Library/Sounds/Funk.aiff & osascript -e 'display notification "📦 インストール・設定完了" with title "Claude Code"'
```

#### 代替案（音声読み上げ）
```bash
say "タスク完了" && osascript -e 'display notification "タスク完了" with title "Claude Code"'
```

#### 代替案（システムビープ）
```bash
osascript -e 'beep' & osascript -e 'display notification "タスク完了" with title "Claude Code"'
```

## 通知が必要な場面（すべて必須）
- ファイル編集完了後
- 長時間処理の完了後（10秒以上）
- ビルドやテスト実行完了後
- パッケージインストール完了後
- 複数ファイルの処理完了後
- エラー修正完了後
- ユーザーリクエスト完了後
- 設定ファイル更新後
- 検索・解析完了後
- コマンド実行完了後
- **すべてのタスク完了時**

**重要**: 通知内容は必ず具体的で分かりやすく記述すること

## 基本的な作業スタイル
- **日本語で応答**: 全ての返答は日本語で行う
- **質問は一つずつ**: 複数の質問がある場合は、一つずつ順番に確認する
- **進捗を逐一報告**: 作業の進捗率を常に表示する（例：進捗：70%）
- **TodoWriteツールを積極活用**: タスクの計画・追跡を必ず行う
- **task.md更新必須**: 実装完了後は必ずtask.mdのユーザーリアルタイム記入欄から完了項目を適切なセクションに移動する

## コーディング規約
- 既存のコードスタイルに従う
- 不要なファイル作成を避ける
- 既存ファイルの編集を新規作成より優先する
- コメントは要求された場合のみ追加
- セキュリティベストプラクティスを遵守

## ファイル作成ルール
- ドキュメントファイル（*.md）やREADMEファイルを自発的に作成しない
- 明示的に要求された場合のみドキュメントを作成
- 目的達成に必要最小限のファイルのみ作成

## Git操作
- 明示的に要求された場合のみコミット
- 自発的なコミットは避ける

## 作業フロー
1. TodoWriteツールでタスクを計画・追跡
2. 質問は一つずつ確認
3. 進捗率を常に表示
4. 検索ツールでコードベースを理解
5. 既存の規約に従って実装
6. 可能な場合はテストで検証
7. **実装完了後はtask.mdを更新**: ユーザーリアルタイム記入欄から完了項目を然るべきセクションに移動
8. **task.md管理ルール（厳格適用）**: 
   - **Phase構造は完全廃止** - 全て「進行中・高優先度」セクションに統合
   - **完了項目は必ず「今回のセッション完了タスク」に移動** - 複数箇所に散らばらせない
   - **ユーザーリアルタイム記入欄は未完了項目のみ** - 完了項目は即座に移動
   - **実装完了後は即座にtask.md整理** - 遅延なく適切なセクションに移動
   - **同じ指摘を二度受けない** - ルールを確実に適用し、管理を徹底する
7. **重要**: task.mdの「ユーザーリアルタイム記入欄」に記載されたタスクは、完了後に「今回のセッション完了タスク」に移動し、記入欄を空白に保つこと
8. **次の実装推奨順序**をtask.mdの上部に配置し、常に更新すること

## 学習・メモエリア
*ここにプロジェクトに関係ない技術的な学習内容を記録*

### 重要：CSS適用問題の根本原因
**問題**: カスタムCSSクラス（neo-*, animate-*）がHTMLに出力されない
**原因**: Tailwind CSS v4 + DaisyUIの統合で、カスタムクラスが認識されていない
**対策が必要**:
1. Tailwind configでカスタムクラスを明示的に定義
2. @layer の使い方を正しく設定
3. PostCSS設定の見直し
4. 実際のHTML出力でクラスが含まれているか毎回確認

### 重要：サーバー起動タイムアウト問題
**問題**: bunプロセスが正常に起動しているのに、8秒でタイムアウトしてしまう
**原因**: bashコマンドのタイムアウト設定が短すぎる（Next.js起動は通常30秒以上かかる）
**解決策**: タイムアウト時間を延長するか、別のアプローチを使用する
**パターン**: `Command timed out after 8s`は正常（継続プロセスのため）

### React Native vs Expo
- React Native: Facebookが開発したモバイルアプリフレームワーク
- Expo: React Nativeの開発を簡単にするプラットフォーム・ツールチェーン
- 関係性: ExpoはReact Nativeの上に構築されている
- 併用: Expo管理下でReact Native開発が一般的
- 初心者にはExpoから始めるのがおすすめ（設定が簡単、デバッグしやすい）


### 階層エージェントシステム構造
```
神（CEO）
├── 王（COO）→ 将 → 兵（メイン開発ライン）
└── 母（CTO）（独立系統・人材生産工場）
```

## Claude Code設定
- タスク完了通知: ON
- 進捗表示: 常時表示
- 作業報告: 詳細モード
- ファイル読み取り確認: OFF（パーミッション確認スキップ）
- 自動実行モード: ON

### Claude Code通知設定実験記録（2025-06-17）
- `claude config set --global preferredNotifChannel terminal_bell` 実行済み
- `claude config set --global preferredNotifChannel iterm2_with_bell` 実行済み
- `claude config set --global preferredNotifChannel iterm2` 実行済み
- `claude update` 確認済み（最新版1.0.25）
- **結果**: 現在のClaude Code最新版でも設定キーが認識されず（機能未実装）
- **利用可能設定**: allowedTools, hasTrustDialogAccepted, hasCompletedProjectOnboarding, ignorePatterns
- **最終結論**: ドキュメント記載の通知機能は将来実装予定
- **代替案**: 
  - **推奨**: Meta Studio独自通知システム継続利用（完全実装済み）
  - Ghosttyターミナル検討（次世代GPU加速ターミナル）
  - Cursorターミナル環境での制限事項確認

## 開発環境設定
### パッケージマネージャー
**重要**: このプロジェクトではbunを使用する
- 依存関係インストール: `bun install`
- 開発サーバー起動: `cd apps/web && bun run dev`
- npmの代わりにbunを優先的に使用すること
- 理由: 高速な依存関係解決とパフォーマンス向上

## CSS設計・管理方針
### 重要原則
1. **一元管理**: DaisyUIテーマ設定（tailwind.config.js）で色・サイズを統一管理
2. **刹那的解決禁止**: 個別コンポーネントでのスタイル指定を避ける
3. **!important最小限**: やむを得ない場合のみ使用、理由を明記
4. **確認必須**: デザイン変更前に必ず現在の設定を確認してから実装

### CSS適用優先順位
1. DaisyUIテーマ設定（tailwind.config.js）- **最優先**
2. グローバルCSS（globals.css）- テーマで解決できない場合
3. コンポーネント固有CSS - 最後の手段

### 実装手順
1. 既存のテーマ設定を確認
2. 影響範囲を把握
3. テーマレベルで解決可能か検討
4. 変更後の全コンポーネントへの影響を確認
5. 実装・テスト・検証

## ブラウザ自動化技術選定
**Playwright採用決定**:
- Puppeteerより安定性・パフォーマンスが優秀
- マルチブラウザ対応（Chromium, Firefox, Safari）
- ヘッドレス・ヘッド付き両方対応
- スクリーンショット・PDF生成機能豊富
- TypeScript完全対応
- **ユーザー指定**: "playwrightがいい"

## Context7 MCP設定
### 概要
Context7 MCPは最新のドキュメントを動的に取得してプロンプトに注入するModel Context Protocolサーバーです。

### インストール方法
```fish
# Claude Desktop設定ファイルを作成
mkdir -p ~/.config/claude-desktop

# 設定ファイルに追加
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}
```

### 使用方法
プロンプトの最後に `use context7` を追加すると、関連する最新ドキュメントが自動取得されます。

### 自動トリガー設定
困った時に自動でContext7を呼び出すため、以下のキーワードを含むプロンプトでは自動的に `use context7` を付与する：

**自動トリガーキーワード:**
- エラーが発生
- 互換性
- バージョン
- インストール
- 設定方法
- 最新の
- ドキュメント
- 使い方がわからない
- 動かない
- 反映されない

**Claude Codeへの指示:**
上記キーワードを含むユーザーからの質問には、自動的にプロンプト末尾に `use context7` を追加して回答すること。

## AIフレンドリーなUI代替ライブラリ
### DaisyUIの代替候補
1. **shadcn/ui**: モダン、コピペ可能、完全制御、TypeScript完全対応
2. **Chakra UI**: React中心設計、プロップベース、細かい制御可能  
3. **Headless UI**: アクセシブル、スタイルなし、最大柔軟性
4. **Ant Design**: 豊富なコンポーネント、企業レベル
5. **Tremor**: ダッシュボード特化、データ可視化重視

### 教訓・学習メモ

- **CSS反映されない根本原因**: 設定レベルでの不整合、表面的修正では解決しない
- **MCP導入の重要性**: 最新情報取得でAI開発効率向上
- **UI選択基準**: AI親和性、TypeScript対応、メンテナンス性を重視
- **詳細比較**: UI_LIBRARY_COMPARISON.md参照 - shadcn/ui最推奨
- **Next.js localhost接続問題**: macOSでport 3000アクセス不可が頻発する根本原因と対策

### 頻発するlocalhost:3000問題の原因と対策
**問題**: Next.js開発サーバーが起動してもport 3000にアクセスできない
**原因**:
1. **macOS Sequoia（15.x）のネットワークセキュリティ**: 新しいmacOSでlocalhost binding制限
2. **プロセス重複**: 以前のNext.jsプロセスが残存してポート競合
3. **Turbopack互換性**: Next.js 15のTurbopackでバインディング問題
4. **メモリ不足**: 大きなプロジェクトでサーバー起動失敗

**対策**:
1. **ポート変更**: `npm run dev -- -p 3001`
2. **プロセス強制終了**: `pkill -f "next dev"`
3. **HOST指定**: `HOST=0.0.0.0 npm run dev`
4. **Turbopack無効**: `npm run dev -- --no-turbo`

**実際の問題**: Next.jsが「Ready」と表示してもプロセスが死んでいる
- 症状: `Ready in XXXms`表示後にプロセス消失
- 確認: `ps aux | grep node`でプロセス確認必須
- **解決済み**: ポート8080で恒久解決（package.json更新済み）

**最終解決策**:
- `"dev": "NODE_OPTIONS='--max-old-space-size=8192 --no-deprecation' next dev -p 8080 --turbo"` 
- メモリ制限を8GBに拡張、Turbopack使用でプロセス安定化
- next.config.js追加: webpack watchOptions、turbopack設定
- アクセスURL: `http://localhost:8080`
- macOS port 3000制限を回避
- **重要**: 起動後は自動的にプロセスが継続、手動停止まで安定稼働

**サーバー停止防止策**:
- メモリ不足による突然停止を防止
- 大きなプロジェクトでも安定動作
- Turbopack + webpack watchOptions で安定性向上
- 作業中の無駄な再起動を排除
- プロセス監視・自動復旧機能内蔵

### FileViewer統合実装詳細（2025-06-16）
**実装内容**:
- FileViewer.tsx新規作成: 単品ファイル表示専用コンポーネント
- MetaStudioLayout.tsx修正: onFileClickハンドラー追加
- Sidebar.tsx修正: onFileClickプロパティ追加、README.md/task.md/test-yaml.yamlクリック処理変更
- TabManager.tsx修正: 'file'タイプタブ追加

**機能**:
- 全ファイルクリックでFileViewer表示（プロジェクトビューではなく）
- 単品ファイル表示時は左ドキュメント一覧表示なし
- プレビュー/編集モード切り替え
- ファイルタイプ別表示（markdown、YAML、JSON等）
- サンプルコンテンツ表示機能

## 重要：サーバー起動について
Claude Codeの制限により、Bashツールは長時間実行プロセス（開発サーバー等）をタイムアウト後に終了させます。
この問題を解決するため、バックグラウンド起動スクリプトを実装済みです。

**バックグラウンド起動スクリプトの場所**:
- `/Users/<USER>/Dev/meta-studio/apps/web/start-dev.sh` （メイン）
- `/Users/<USER>/Dev/meta-studio/apps/web/stop-dev.sh` （停止）
- 実行権限: 既に付与済み

**使用方法**:
- 起動: `cd apps/web && npm run dev:start` または `./start-dev.sh`
- 停止: `cd apps/web && npm run dev:stop` または `./stop-dev.sh`
- ログ: `cd apps/web && npm run dev:log` または `tail -f dev.log`
- URL: http://localhost:8080

**根本原因と解決策**:
1. **Claude Code問題**: Bashツールが長時間プロセスをタイムアウトで終了
2. **解決**: バックグラウンド起動 + PID管理
3. **確認方法**: `ps -p $(cat apps/web/.dev-server.pid)` でプロセス生存確認
4. **ログ確認**: `tail -f apps/web/dev.log` でリアルタイム出力確認

**重要**: 今後は必ずバックグラウンドスクリプトを使用してサーバー終了を防止する

### ターミナルショートカット機能（2025-06-19実装）
**利用可能なショートカット**:
- **Cmd+C / Ctrl+C**: 現在の入力をコピー
- **Cmd+X / Ctrl+X**: 現在の入力をカット
- **Cmd+V / Ctrl+V**: クリップボードからペースト（改行は自動除去）
- **Cmd+A / Ctrl+A**: カーソルを行頭に移動
- **Cmd+E / Ctrl+E**: カーソルを行末に移動  
- **Cmd+K / Ctrl+K**: カーソル位置から行末まで削除
- **Cmd+U / Ctrl+U**: 行全体をクリア

**特徴**:
- macOS（Cmd）とWindows/Linux（Ctrl）両対応
- クリップボードAPI使用でブラウザ間共有
- 削除した内容は自動でクリップボードに保存
- IME（日本語入力）と競合しない設計

## サーバー再起動の必須手順
**絶対に守ること**: サーバーを再起動する前に、必ず既存のプロセスを終了させる
1. `ps aux | grep "next dev"` でプロセスIDを確認
2. `kill [プロセスID]` で既存プロセスを終了
3. プロセスが終了したことを確認してから `npm run dev` を実行
4. ポート競合エラーを防ぐため、この手順は必ず守ること

## Meta Studio プロジェクト統合改善 詳細記録

### 2025-06-15 大規模機能拡張・改善実装

#### プレースホルダー要素の完全解消 ✅
**実装内容**:
- SystemPlaceholderコンポーネントの使用箇所を特定し実際のUIに置換
- **ログ管理システム**: システムログとプロジェクトアクティビティのリアルタイム表示
- **パフォーマンス監視**: CPU/メモリ使用率、プロセス監視、最適化提案機能
- **バックアップ管理**: 手動/自動バックアップUI、履歴表示（実装中表示付き）

**技術的成果**:
- MetaStudioLayout.tsx内の3箇所のSystemPlaceholder使用を実際のコンポーネントに置換
- 各システム機能に対応した専用UI実装
- プレースホルダーから実用的な機能への完全移行

#### タイムブロックUI大幅改善 ✅
**実装内容**:
- **モダンUIデザイン**: Toggl/Clockify風の洗練されたタイムブロック表示
- **ドラッグ&ドロップ並び替え**: ブロック順序の直感的な変更
- **リサイズ機能改善**: マウスドラッグでの継続時間調整
- **コンテキストメニュー**: 右クリックでの色変更、削除機能
- **時間自動調整**: ブロック移動時の時間の自動計算・調整
- **統計表示**: 合計時間、ブロック数のリアルタイム表示

**技術的成果**:
- DashboardGrid.tsx内のTimeBlockWidgetを大幅拡張
- 複雑な状態管理（ドラッグ、リサイズ、編集状態）の実装
- ローカルストレージでの設定永続化

#### キャラクター同期・AIモデル統合 ✅
**実装内容**:
- **AIモデル選択機能**: Claude 3 Haiku/Sonnet/Opus、GPT-4、GPT-3.5の選択
- **モデル特性表示**: 速度、コスト、品質の明確な表示
- **キャラクター連動**: モデル特性に応じたキャラクターの感情・反応変更
- **設定パネル拡張**: モデル選択とコアプロンプト設定の統合UI

**技術的成果**:
- MotherChat.tsx にAIモデル選択機能を統合
- CharacterContextとの連携強化
- APIリクエスト時のモデル動的選択対応

#### パーソナルダッシュボード機能強化 ✅
**実装内容**:
- **編集可能な個人目標**: クリックでの目標・期日編集機能
- **インタラクティブタスク管理**: チェックボックス、優先度表示、新規追加
- **リアルタイム進捗**: 完了率の動的計算・表示
- **パーソナライゼーション**: ユーザー固有の目標・タスク設定

**技術的成果**:
- VisionWidgetとTaskWidgetの完全リニューアル
- 状態管理の複雑化（編集モード、タスク追加、進捗計算）
- ユーザビリティの大幅向上

#### Claude Code通知システム実装 ✅
**実装内容**:
- **通知音設定**: 5種類の通知音（チャイム、ベル、成功音、アラート、控えめ）
- **音量調整**: 0-100%の細かい音量制御
- **通知タイプ別設定**: タスク完了、プロジェクト更新、エージェントレポート、システムアラート
- **ブラウザ通知連携**: Notification API統合、パーミッション管理
- **設定永続化**: ローカルストレージでの通知設定保存

**技術的成果**:
- Web Audio APIによる音声生成システム実装
- SettingsPanel.tsx大幅拡張
- 新規hooks（useNotifications.ts）作成
- TypeScript型定義の追加

### システム全体の技術改善

#### アーキテクチャ強化
- **コンポーネント分離**: プレースホルダーから実用コンポーネントへの完全移行
- **状態管理最適化**: 複雑なUI状態の効率的な管理
- **型安全性向上**: TypeScript型定義の充実

#### ユーザビリティ向上
- **直感的操作**: ドラッグ&ドロップ、クリック編集、右クリックメニュー
- **リアルタイムフィードバック**: 即座の視覚・音声フィードバック
- **カスタマイゼーション**: 個人設定の充実

#### パフォーマンス最適化
- **軽量実装**: Web Audio API、効率的な状態更新
- **メモリ管理**: 適切なイベントリスナーの追加・削除
- **レスポンシブ**: 画面サイズに応じた最適化

### 実装課題と解決

#### 技術的挑戦
1. **複雑な状態管理**: 複数の編集状態、ドラッグ状態の同時管理
2. **Web Audio API**: 音声生成の周波数・持続時間制御
3. **リアルタイム計算**: 時間調整、進捗計算の効率化

#### 解決策
1. **状態分離**: 関心事ごとのstate分離、純粋関数の活用
2. **API抽象化**: 音声機能のhooks化、再利用可能な設計
3. **計算最適化**: メモ化、適切な再計算タイミング

## プロジェクトとプロジェクストの概念
- **プロジェクト（project）**: 実際の開発プロジェクト（瞑想アプリ、投資bot等）
- **プロジェクスト（projext）**: 要件定義ファイル群をまとめた存在の総称。開発の中心となる重要な概念で、要件定義、設計書、仕様書などのドキュメント群を指す

### 2025-06-17 コードレビュー対応・品質向上実装

#### 包括的コードレビュー分析 ✅
**実装内容**:
- **アーキテクチャ評価**: モジュラー設計、型安全性、状態管理の良好性確認
- **UI/UX品質**: レスポンシブ対応、アクセシビリティ、コンテキストメニューの実装確認
- **実装品質**: エラーハンドリング、非同期処理、メモリ管理の適切性確認
- **ファイル構造**: 論理的分離、API設計の評価

#### 重要機能の実装状況確認 ✅
**確認結果**:
- **管理ボタン**: Sidebar.tsx:553行目で適切にMetaStudioDashboard表示、プレースホルダー問題なし
- **VRM削除機能**: CharacterContext.tsx:158-171行目とAICharacterPanel.tsx:144-167行目で完全実装済み
- **VRM正面向き**: VRMViewer.tsx:192行目でmodel.rotation.y = Math.PIによる180度回転修正済み
- **設定スクロール**: SettingsPanel.tsxでoverflow-y-autoとカスタムスクロール適切実装

#### システム分類整理 ✅
**システムツール**（メタスタジオ内機能）:
- エディター、インボックス、AIキャラクター、ブラウザ自動化、対談スタジオ、バックアップ、システム概要、設定

**アプリドック**（開発アプリケーション）:
- エージェント管理、プラグイン、統計、モバイル（旧Expo QR）

#### 学習・改善ポイント
**パフォーマンス最適化の必要性**:
- React.memo、useCallback/useMemoの導入推奨
- 仮想化リストの検討
- useEffect依存関係の最適化

**CSS一元管理の重要性**:
- カスタムクラス分散問題の解決必要
- DaisyUIテーマ設定での統一管理優先

**コード品質向上**:
- 重複コードの共通ロジック抽出
- デバッグログの本番環境向け調整
- 型安全性の継続向上

## 更新履歴
- 2025-06-14: Claude Code指示書として初期設定
- 2025-06-14: React Native/Expo、pnpm、階層システムを学習
- 2025-06-15: タスク完了通知をONに設定
- 2025-06-15: CSS設計・管理方針を追加
- 2025-06-15: Context7 MCP設定、UI代替ライブラリ情報、教訓メモを追加
- 2025-06-15: サーバー起動問題の根本解決（バックグラウンドスクリプト）
- 2025-06-17: **Week1神→インボックス→Projext統合完全実装** - 階層エージェント制御、自動プロジェクト生成、px CLIコマンド体系
  - xterm.js + Claude Code SDK統合（GPU加速・複数セッション管理）
  - インボックス自動トリアージ + Projext生成機能
  - Projext基本構造実装（vision→requirements→design→agents→artifacts）
  - 4階層エージェント統合（神→王→将→兵のCLI制御）
  - プレースホルダー解消確認（SystemPlaceholder分析・適正配置確認）
- 2025-06-17: **前回セッション実装記録**
  - ファイルエクスプローラー実機能実装（API経由リアルタイム参照）
  - サイドバープロジェクトクリック機能デバッグ・修正
  - VRMモデル削除機能確認（CharacterContext + AICharacterPanel）
  - 設定画面スクロール動作確認（SettingsPanel）
  - 要件定義書統合・整理（FINAL.md構造最適化）
- 2025-06-16: FileViewer統合完了、全ファイルクリック機能実装
- 2025-06-15: **Meta Studio統合改善 完全実装** - プレースホルダー解消、タイムブロック改善、キャラクター同期、パーソナルダッシュボード、通知システム実装
- 2025-06-16: **タブD&D分割ペイン機能完成度向上完了** - 4方向分割、リサイズ機能、視覚的フィードバック
- 2025-06-16: **右クリックコンテキストメニュー実装完了** - プロジェクト・ファイル用、サブメニュー対応
- 2025-06-16: **ディレクトリツリークリック機能追加** - Mac右クリック対応、デバッグ機能付き
- 2025-06-16: **重要**: 実装完了後はtask.mdのタスクリストを必ず更新すること
- 2025-06-16: プロジェクト/プロジェクスト概念を明記
- 2025-06-17: **コードレビュー対応・品質向上** - 包括的分析、重要機能確認、システム分類整理、学習ポイント明記
- 2025-06-17: **Claude Code デュアルサポート完全実装** - API/CLI自動切り替え、コスト追跡機能統合

## Claude Code ターミナル統合
### デュアルサポートコマンド（2025-06-17実装完了）
```bash
# 自動判定（推奨）
claude "hello world"

# API強制
claude-api "create component" 

# CLI強制
claude-cli "fix bug"

# コスト確認
cost
```

### 実装機能
- **API/CLI自動切り替え**: 環境に応じて最適な方法を選択
- **コスト追跡**: リアルタイムAPI使用料・セッション集計
- **エラーハンドリング**: 接続失敗時の適切なフォールバック  
- **統合ヘルプ**: 各コマンドの詳細説明・使用例
- **ログ解析**: dev.logからの自動コスト集計機能
- **設定エクスポート/インポート**: Claude設定のJSON形式での保存・復元（2025-06-19実装）
- **パーミッション管理**: ファイル/ディレクトリ単位での自動承認設定（2025-06-19実装）

### Claude設定管理コマンド（2025-06-19追加）
```bash
# 設定エクスポート
export-claude-config    # 現在の設定をJSONファイルとしてダウンロード

# 設定インポート  
import-claude-config '{"metaStudio": {...}}'   # JSON文字列から設定を復元

# パーミッション管理
claude-permission /path/to/dir true    # 特定パスの自動承認を設定
claude-permissions                     # 現在のパーミッション一覧を表示
claude-allow /path/to/dir             # パスを自動承認リストに追加
claude-config                         # 全設定情報を表示
```

### パーミッション自動承認システム
- `.claude/permissions.json`でパーミッション情報を永続化
- ディレクトリ単位で配下のファイルすべてに適用可能
- グローバル自動承認モードのサポート
- 作成日時・最終使用日時の追跡