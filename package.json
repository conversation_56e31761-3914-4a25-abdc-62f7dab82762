{"name": "meta-studio", "version": "1.0.0", "private": true, "description": "メタスタジオ（脳内現実化ツール）統合開発プラットフォーム", "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo build", "dev": "turbo dev", "dev:web": "cd apps/web && bun run dev", "dev:start": "./start-dev.sh", "dev:stop": "./stop-dev.sh", "dev:log": "tail -f .dev-server.log", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "type-check": "turbo type-check", "px": "bun run packages/px-cli/index.ts", "install:all": "bun install && cd apps/web && bun install"}, "devDependencies": {"@turbo/gen": "^1.12.4", "turbo": "^1.12.4", "prettier": "^3.2.5", "typescript": "^5.3.3"}, "engines": {"node": ">=18"}, "packageManager": "bun@1.0.0", "dependencies": {"@anthropic-ai/sdk": "^0.54.0", "@mastra/core": "^0.10.5", "@mastra/rag": "^0.10.3", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.50.0", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "^5.0.0", "@trpc/client": "^11.4.1", "@trpc/next": "^11.4.1", "@trpc/react-query": "^11.4.1", "@trpc/server": "^11.4.1", "superjson": "^2.2.2", "zod": "^3.25.64"}}