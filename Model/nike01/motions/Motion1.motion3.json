{"Version": 3, "Meta": {"Duration": 4.7, "Fps": 30.0, "Loop": false, "AreBeziersRestricted": false, "CurveCount": 22, "TotalSegmentCount": 121, "TotalPointCount": 341, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -8, 1, 0.067, -8, 0.133, -8, 0.2, -8, 1, 0.278, -8, 0.356, -8, 0.433, -8, 1, 0.556, -8, 0.678, -8, 0.8, -8, 1, 0.956, -8, 1.111, -8, 1.267, -8, 1, 1.522, -8, 1.778, 0, 2.033, 0, 1, 2.122, 0, 2.211, 0, 2.3, 0, 1, 2.556, 0, 2.811, 9, 3.067, 9, 1, 3.356, 9, 3.644, 0, 3.933, 0, 1, 4.067, 0, 4.2, 1, 4.333, 1, 0, 4.7, 1]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -5, 1, 0.067, -5, 0.133, -5, 0.2, -5, 1, 0.322, -5, 0.444, 16, 0.567, 16, 1, 0.711, 16, 0.856, -12, 1, -12, 1, 1.089, -12, 1.178, 11, 1.267, 11, 1, 1.356, 11, 1.444, 9.376, 1.533, 3, 1, 1.611, -2.579, 1.689, -8, 1.767, -8, 1, 1.856, -8, 1.944, 0, 2.033, 0, 1, 2.122, 0, 2.211, 0, 2.3, 0, 1, 2.578, 0, 2.856, -30, 3.133, -30, 1, 3.278, -30, 3.422, 15, 3.567, 15, 1, 3.689, 15, 3.811, -9, 3.933, -9, 0, 4.7, -9]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.278, 0, 0.356, 0, 0.433, 0, 1, 0.711, 0, 0.989, 0, 1.267, 0, 1, 1.522, 0, 1.778, 14, 2.033, 14, 1, 2.178, 14, 2.322, 14, 2.467, 14, 1, 2.8, 14, 3.133, 16, 3.467, 16, 1, 3.633, 16, 3.8, -13, 3.967, -13, 1, 4.111, -13, 4.256, -11.207, 4.4, -11.207, 0, 4.7, -11.207]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 4.7, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.556, 1, 1.111, 1, 1.667, 1, 1, 1.711, 1, 1.756, 0, 1.8, 0, 1, 1.822, 0, 1.844, 0, 1.867, 0, 1, 1.911, 0, 1.956, 1, 2, 1, 1, 2.489, 1, 2.978, 1, 3.467, 1, 1, 3.511, 1, 3.556, 0, 3.6, 0, 1, 3.622, 0, 3.644, 0, 3.667, 0, 1, 3.733, 0, 3.8, 1, 3.867, 1, 0, 4.7, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 4.7, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.556, 1, 1.111, 1, 1.667, 1, 1, 1.711, 1, 1.756, 0, 1.8, 0, 1, 1.822, 0, 1.844, 0, 1.867, 0, 1, 1.911, 0, 1.956, 1, 2, 1, 1, 2.489, 1, 2.978, 1, 3.467, 1, 1, 3.511, 1, 3.556, 0, 3.6, 0, 1, 3.622, 0, 3.644, 0, 3.667, 0, 1, 3.733, 0, 3.8, 1, 3.867, 1, 0, 4.7, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 4.7, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.156, 0, 0.311, -0.02, 0.467, -0.02, 1, 0.578, -0.02, 0.689, 0, 0.8, 0, 1, 1.056, 0, 1.311, 0, 1.567, 0, 1, 1.767, 0, 1.967, -0.131, 2.167, -0.15, 1, 2.644, -0.195, 3.122, -0.2, 3.6, -0.2, 1, 3.622, -0.2, 3.644, 0.803, 3.667, 0.803, 0, 4.7, 0.803]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.156, 0, 0.311, 0.01, 0.467, 0.08, 1, 0.578, 0.13, 0.689, 0.21, 0.8, 0.21, 1, 1.056, 0.21, 1.311, 0.21, 1.567, 0.21, 1, 1.767, 0.21, 1.967, 0.08, 2.167, 0.08, 1, 2.644, 0.08, 3.122, 0.086, 3.6, 0.1, 1, 3.622, 0.101, 3.644, 0.794, 3.667, 0.794, 0, 4.7, 0.794]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 4.7, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 4.7, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 0, 4.7, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 4.7, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, -6, 1, 0.856, -6, 1.711, 1, 2.567, 1, 1, 2.789, 1, 3.011, 1.145, 3.233, 0, 1, 3.467, -1.202, 3.7, -6, 3.933, -6, 0, 4.7, -6]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.278, 0, 0.356, 7, 0.433, 7, 1, 0.611, 7, 0.789, -4, 0.967, -4, 1, 1.078, -4, 1.189, 6, 1.3, 6, 1, 1.433, 6, 1.567, -3, 1.7, -3, 1, 1.789, -3, 1.878, -1.459, 1.967, 0, 1, 2.067, 1.642, 2.167, 3.47, 2.267, 4, 1, 2.367, 4.53, 2.467, 4.312, 2.567, 5, 1, 2.889, 7.215, 3.211, 10, 3.533, 10, 1, 3.667, 10, 3.8, 0, 3.933, 0, 1, 4.067, 0, 4.2, 0, 4.333, 0, 0, 4.7, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.822, 0, 1.644, 0, 2.467, 0, 1, 2.722, 0, 2.978, 5, 3.233, 5, 1, 3.489, 5, 3.744, -5, 4, -5, 1, 4.156, -5, 4.311, -3.976, 4.467, -3.976, 0, 4.7, -3.976]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 4.7, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, -10, 1, 0.178, -10, 0.356, -7, 0.533, -7, 1, 0.7, -7, 0.867, -10, 1.033, -10, 1, 1.2, -10, 1.367, -8, 1.533, -8, 1, 1.611, -8, 1.689, -8.746, 1.767, -9, 1, 2.011, -9.797, 2.256, -10, 2.5, -10, 1, 2.556, -10, 2.611, -10, 2.667, -10, 1, 2.789, -10, 2.911, -10, 3.033, -10, 0, 4.7, -10]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, -10, 1, 0.178, -10, 0.356, -7, 0.533, -7, 1, 0.7, -7, 0.867, -10, 1.033, -10, 1, 1.2, -10, 1.367, -6, 1.533, -6, 1, 1.611, -6, 1.689, -6.903, 1.767, -7.5, 1, 2.011, -9.377, 2.256, -10, 2.5, -10, 1, 2.567, -10, 2.633, -8.958, 2.7, -8.958, 1, 2.811, -8.958, 2.922, -10, 3.033, -10, 0, 4.7, -10]}, {"Target": "Parameter", "Id": "ParamHairAhoge", "Segments": [0, 0, 1, 0.144, 0, 0.289, 10, 0.433, 10, 1, 0.578, 10, 0.722, -10, 0.867, -10, 1, 0.989, -10, 1.111, 4, 1.233, 4, 1, 1.322, 4, 1.411, 2.767, 1.5, 0, 1, 1.611, -3.459, 1.722, -5.351, 1.833, -5.351, 1, 1.922, -5.351, 2.011, 1.661, 2.1, 1.661, 1, 2.233, 1.661, 2.367, 0, 2.5, 0, 1, 2.867, 0, 3.233, 10, 3.6, 10, 1, 3.722, 10, 3.844, -10, 3.967, -10, 1, 4.078, -10, 4.189, 6, 4.3, 6, 1, 4.356, 6, 4.411, 0, 4.467, 0, 0, 4.7, 0]}, {"Target": "PartOpacity", "Id": "PartArmA", "Segments": [0, 1, 0, 4.7, 1]}]}