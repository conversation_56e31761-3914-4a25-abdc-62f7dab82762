{"Version": 3, "Meta": {"Duration": 8.57, "Fps": 30.0, "Loop": false, "AreBeziersRestricted": false, "CurveCount": 22, "TotalSegmentCount": 194, "TotalPointCount": 560, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.189, 0, 0.378, 1, 0.567, 1, 1, 1.122, 1, 1.678, 1, 2.233, 1, 1, 2.533, 1, 2.833, -30, 3.133, -30, 1, 4.067, -30, 5, -30, 5.933, -30, 1, 6.322, -30, 6.711, 0, 7.1, 0, 0, 8.567, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.189, 0, 0.378, 18, 0.567, 18, 1, 0.856, 18, 1.144, -9.95, 1.433, -9.95, 1, 1.522, -9.95, 1.611, -1.589, 1.7, -1.589, 1, 1.911, -1.589, 2.122, -8.047, 2.333, -8.047, 1, 2.6, -8.047, 2.867, 30, 3.133, 30, 1, 3.478, 30, 3.822, 29.529, 4.167, 27.847, 1, 4.311, 27.142, 4.456, 25.42, 4.6, 24.243, 1, 4.922, 21.616, 5.244, 20.338, 5.567, 20.338, 1, 5.789, 20.338, 6.011, 27.397, 6.233, 27.397, 1, 6.522, 27.397, 6.811, 0, 7.1, 0, 0, 8.567, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.8, 0, 1.033, 25, 1.267, 25, 1, 1.4, 25, 1.533, 25, 1.667, 25, 1, 1.878, 25, 2.089, 25, 2.3, 25, 1, 2.578, 25, 2.856, -30, 3.133, -30, 1, 4, -30, 4.867, -30, 5.733, -30, 1, 5.8, -30, 5.867, -30, 5.933, -30, 1, 6.322, -30, 6.711, 0, 7.1, 0, 0, 8.567, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 2.844, 0, 5.089, 0, 7.333, 0, 0, 8.567, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.278, 1, 0.556, 1, 0.833, 1, 1, 0.867, 1, 0.9, 0, 0.933, 0, 1, 1.222, 0, 1.511, 0, 1.8, 0, 1, 1.889, 0, 1.978, 1, 2.067, 1, 1, 2.322, 1, 2.578, 1, 2.833, 1, 1, 2.889, 1, 2.944, 0, 3, 0, 1, 3.022, 0, 3.044, 0, 3.067, 0, 1, 3.122, 0, 3.178, 1, 3.233, 1, 1, 3.5, 1, 3.767, 1, 4.033, 1, 1, 4.089, 1, 4.144, 0, 4.2, 0, 1, 4.222, 0, 4.244, 0, 4.267, 0, 1, 4.322, 0, 4.378, 1, 4.433, 1, 1, 5.133, 1, 5.833, 1, 6.533, 1, 1, 6.589, 1, 6.644, 0, 6.7, 0, 1, 6.722, 0, 6.744, 0, 6.767, 0, 1, 6.822, 0, 6.878, 1, 6.933, 1, 1, 7.067, 1, 7.2, 1, 7.333, 1, 0, 8.567, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.278, 0, 0.556, 0.812, 0.833, 0.812, 0, 8.567, 0.812]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.278, 1, 0.556, 1, 0.833, 1, 1, 0.867, 1, 0.9, 0, 0.933, 0, 1, 1.222, 0, 1.511, 0, 1.8, 0, 1, 1.889, 0, 1.978, 1, 2.067, 1, 1, 2.322, 1, 2.578, 1, 2.833, 1, 1, 2.889, 1, 2.944, 0, 3, 0, 1, 3.022, 0, 3.044, 0, 3.067, 0, 1, 3.122, 0, 3.178, 1, 3.233, 1, 1, 3.5, 1, 3.767, 1, 4.033, 1, 1, 4.089, 1, 4.144, 0, 4.2, 0, 1, 4.222, 0, 4.244, 0, 4.267, 0, 1, 4.322, 0, 4.378, 1, 4.433, 1, 1, 5.133, 1, 5.833, 1, 6.533, 1, 1, 6.589, 1, 6.644, 0, 6.7, 0, 1, 6.722, 0, 6.744, 0, 6.767, 0, 1, 6.822, 0, 6.878, 1, 6.933, 1, 1, 7.067, 1, 7.2, 1, 7.333, 1, 0, 8.567, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.278, 0, 0.556, 0.812, 0.833, 0.812, 1, 1.967, 0.812, 3.1, 0.8, 4.233, 0.8, 0, 8.567, 0.8]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.2, 0, 0.4, -0.065, 0.6, -0.14, 1, 0.811, -0.219, 1.022, -0.24, 1.233, -0.24, 1, 1.367, -0.24, 1.5, -0.24, 1.633, -0.24, 1, 1.844, -0.24, 2.056, -0.24, 2.267, -0.24, 1, 2.556, -0.24, 2.844, 0.661, 3.133, 0.68, 1, 3.5, 0.704, 3.867, 0.7, 4.233, 0.7, 1, 4.5, 0.7, 4.767, -0.75, 5.033, -0.75, 1, 5.589, -0.75, 6.144, 0.68, 6.7, 0.68, 1, 6.789, 0.68, 6.878, 0, 6.967, 0, 0, 8.567, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.2, 0, 0.4, -0.38, 0.6, -0.38, 1, 0.811, -0.38, 1.022, 0.21, 1.233, 0.21, 1, 1.367, 0.21, 1.5, 0.21, 1.633, 0.21, 1, 1.844, 0.21, 2.056, 0.21, 2.267, 0.21, 1, 2.556, 0.21, 2.844, -0.33, 3.133, -0.33, 1, 3.767, -0.33, 4.4, 0.625, 5.033, 0.625, 1, 5.589, 0.625, 6.144, -0.33, 6.7, -0.33, 1, 6.789, -0.33, 6.878, 0, 6.967, 0, 0, 8.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 0, 8.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 0, 8.567, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.2, 1, 0.4, 1, 0.6, 1, 1, 0.889, 1, 1.178, 1, 1.467, 1, 1, 1.611, 1, 1.756, 1, 1.9, 1, 1, 2.189, 1, 2.478, 0, 2.767, 0, 1, 3.989, 0, 5.211, 0, 6.433, 0, 1, 6.533, 0, 6.633, 1, 6.733, 1, 0, 8.567, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.667, 0, 0.733, 0, 0.8, 0, 1, 0.844, 0, 0.889, 1, 0.933, 1, 1, 1.111, 1, 1.289, 1, 1.467, 1, 1, 1.611, 1, 1.756, 0, 1.9, 0, 1, 2.056, 0, 2.211, 0, 2.367, 0, 1, 2.5, 0, 2.633, 1, 2.767, 1, 1, 3.989, 1, 5.211, 1, 6.433, 1, 1, 6.533, 1, 6.633, 0, 6.733, 0, 0, 8.567, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0, 0.4, 0, 1, 0.5, 0, 0.6, -1.353, 0.7, -1.353, 1, 0.933, -1.353, 1.167, 10, 1.4, 10, 1, 1.544, 10, 1.689, 9.799, 1.833, 9.799, 1, 2.089, 9.799, 2.344, 9.875, 2.6, 9.875, 1, 2.944, 9.875, 3.289, -9.599, 3.633, -9.599, 1, 3.844, -9.599, 4.056, -2.657, 4.267, -2.657, 1, 4.467, -2.657, 4.667, -9.098, 4.867, -9.098, 1, 5.144, -9.098, 5.422, -4.812, 5.7, -4.812, 1, 5.967, -4.812, 6.233, -10, 6.5, -10, 1, 6.778, -10, 7.056, -1.43, 7.333, 0, 1, 7.533, 1.03, 7.733, 0.752, 7.933, 0.752, 1, 8.011, 0.752, 8.089, -0.326, 8.167, -0.326, 0, 8.567, -0.326]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.2, 0, 0.4, 5.421, 0.6, 5.421, 1, 0.867, 5.421, 1.133, -3.737, 1.4, -3.737, 1, 1.511, -3.737, 1.622, 0.246, 1.733, 0.246, 1, 1.933, 0.246, 2.133, 0, 2.333, 0, 1, 2.389, 0, 2.444, 2.993, 2.5, 4, 1, 2.711, 7.827, 2.922, 9, 3.133, 9, 1, 4.256, 9, 5.378, 9, 6.5, 9, 1, 6.778, 9, 7.056, -5, 7.333, -5, 1, 7.5, -5, 7.667, 2, 7.833, 2, 1, 7.9, 2, 7.967, 0, 8.033, 0, 0, 8.567, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.2, 0, 0.4, 2, 0.6, 2, 1, 0.867, 2, 1.133, -10, 1.4, -10, 1, 1.533, -10, 1.667, -10, 1.8, -10, 1, 2, -10, 2.2, -10.197, 2.4, -6.566, 1, 2.6, -2.936, 2.8, 10, 3, 10, 1, 3.433, 10, 3.867, -4.436, 4.3, -4.436, 1, 4.578, -4.436, 4.856, 10, 5.133, 10, 1, 5.433, 10, 5.733, 0.695, 6.033, -4.261, 1, 6.211, -7.197, 6.389, -6.792, 6.567, -6.792, 1, 6.644, -6.792, 6.722, -6.224, 6.8, -4.11, 1, 6.989, 1.023, 7.178, 4.236, 7.367, 4.236, 1, 7.689, 4.236, 8.011, 0.025, 8.333, 0.025, 0, 8.567, 0.025]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.122, 0, 0.244, 1, 0.367, 1, 1, 0.511, 1, 0.656, 0, 0.8, 0, 1, 1.022, 0, 1.244, 1, 1.467, 1, 1, 1.689, 1, 1.911, 0, 2.133, 0, 1, 2.344, 0, 2.556, 1, 2.767, 1, 1, 2.967, 1, 3.167, 0, 3.367, 0, 1, 3.578, 0, 3.789, 1, 4, 1, 1, 4.233, 1, 4.467, 0, 4.7, 0, 1, 4.9, 0, 5.1, 0, 5.3, 0, 1, 5.522, 0, 5.744, 1, 5.967, 1, 1, 6.244, 1, 6.522, 0, 6.8, 0, 1, 7.022, 0, 7.244, 1, 7.467, 1, 1, 7.689, 1, 7.911, 0, 8.133, 0, 0, 8.567, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, -10, 1, 0.2, -10, 0.4, -10, 0.6, -10, 1, 0.833, -10, 1.067, -7.083, 1.3, -7.083, 1, 1.433, -7.083, 1.567, -7.083, 1.7, -7.083, 1, 1.911, -7.083, 2.122, -10, 2.333, -10, 1, 3.467, -10, 4.6, -10, 5.733, -10, 1, 5.8, -10, 5.867, -10, 5.933, -10, 1, 6.4, -10, 6.867, -10, 7.333, -10, 0, 8.567, -10]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, -10, 1, 0.2, -10, 0.4, -10, 0.6, -10, 1, 0.833, -10, 1.067, 2.917, 1.3, 2.917, 1, 1.478, 2.917, 1.656, 3.029, 1.833, 2.6, 1, 2.033, 2.117, 2.233, -10, 2.433, -10, 1, 3.533, -10, 4.633, -10, 5.733, -10, 1, 5.8, -10, 5.867, -10, 5.933, -10, 1, 6.133, -10, 6.333, -10, 6.533, -10, 1, 6.8, -10, 7.067, -10, 7.333, -10, 0, 8.567, -10]}, {"Target": "Parameter", "Id": "ParamHairAhoge", "Segments": [0, 0, 1, 0.178, 0, 0.356, -10, 0.533, -10, 1, 0.833, -10, 1.133, 10, 1.433, 10, 1, 1.567, 10, 1.7, 0, 1.833, 0, 1, 2.311, 0, 2.789, 10, 3.267, 10, 1, 3.344, 10, 3.422, 10, 3.5, 10, 1, 3.633, 10, 3.767, -10, 3.9, -10, 1, 4.267, -10, 4.633, 7.69, 5, 9.705, 1, 5.122, 10.376, 5.244, 10, 5.367, 10, 1, 5.756, 10, 6.144, -9.975, 6.533, -9.975, 1, 6.622, -9.975, 6.711, -10.207, 6.8, -9.779, 1, 7.033, -8.654, 7.267, 10, 7.5, 10, 1, 7.7, 10, 7.9, -6.753, 8.1, -6.753, 1, 8.2, -6.753, 8.3, 0, 8.4, 0, 0, 8.567, 0]}, {"Target": "PartOpacity", "Id": "PartArmA", "Segments": [0, 1, 0, 8.57, 1]}]}