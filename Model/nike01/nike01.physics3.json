{"Version": 3, "Meta": {"PhysicsSettingCount": 37, "TotalInputCount": 58, "TotalOutputCount": 71, "VertexCount": 98, "Fps": 60, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "身体バウンドX"}, {"Id": "PhysicsSetting2", "Name": "身体バウンドY"}, {"Id": "PhysicsSetting3", "Name": "頭X"}, {"Id": "PhysicsSetting4", "Name": "頭Y"}, {"Id": "PhysicsSetting5", "Name": "頭Z"}, {"Id": "PhysicsSetting6", "Name": "身体X"}, {"Id": "PhysicsSetting7", "Name": "身体Y"}, {"Id": "PhysicsSetting8", "Name": "身体Z"}, {"Id": "PhysicsSetting9", "Name": "下半身Z"}, {"Id": "PhysicsSetting10", "Name": "瞳X"}, {"Id": "PhysicsSetting11", "Name": "瞳Y"}, {"Id": "PhysicsSetting12", "Name": "左ハイライト揺れ"}, {"Id": "PhysicsSetting13", "Name": "右ハイライト揺れ"}, {"Id": "PhysicsSetting14", "Name": "左瞳"}, {"Id": "PhysicsSetting15", "Name": "右瞳"}, {"Id": "PhysicsSetting16", "Name": "左瞳揺れ"}, {"Id": "PhysicsSetting17", "Name": "右瞳揺れ"}, {"Id": "PhysicsSetting18", "Name": "左まぶた揺れ"}, {"Id": "PhysicsSetting19", "Name": "右まぶた揺れ"}, {"Id": "PhysicsSetting20", "Name": "左まぶた揺れ2"}, {"Id": "PhysicsSetting21", "Name": "右まぶた揺れ2"}, {"Id": "PhysicsSetting22", "Name": "口揺れ"}, {"Id": "PhysicsSetting23", "Name": "瞳縮小"}, {"Id": "PhysicsSetting24", "Name": "前髪中揺れX"}, {"Id": "PhysicsSetting25", "Name": "前髪左揺れX"}, {"Id": "PhysicsSetting26", "Name": "前髪右揺れX"}, {"Id": "PhysicsSetting27", "Name": "前髪揺れY"}, {"Id": "PhysicsSetting28", "Name": "横髪左揺れX"}, {"Id": "PhysicsSetting29", "Name": "横髪右揺れX"}, {"Id": "PhysicsSetting30", "Name": "横髪揺れY"}, {"Id": "PhysicsSetting31", "Name": "アホ毛揺れ"}, {"Id": "PhysicsSetting32", "Name": "ポニテ揺れX"}, {"Id": "PhysicsSetting33", "Name": "腕揺れ"}, {"Id": "PhysicsSetting34", "Name": "胸揺れX"}, {"Id": "PhysicsSetting35", "Name": "胸揺れY"}, {"Id": "PhysicsSetting36", "Name": "服揺れX"}, {"Id": "PhysicsSetting37", "Name": "服揺れY"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleX3"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.92, "Acceleration": 1.7, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleY4"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.92, "Acceleration": 1.7, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamAngleX2"}, "VertexIndex": 1, "Scale": 43, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.75, "Delay": 1.2, "Acceleration": 0.9, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamAngleY2"}, "VertexIndex": 1, "Scale": 43, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.75, "Delay": 1.2, "Acceleration": 0.9, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamAngleZ2"}, "VertexIndex": 1, "Scale": 43, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.75, "Delay": 1.2, "Acceleration": 0.9, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX3"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleX2"}, "VertexIndex": 1, "Scale": 13.4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.75, "Delay": 1.2, "Acceleration": 0.9, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY4"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleY2"}, "VertexIndex": 1, "Scale": 13, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleY3"}, "VertexIndex": 1, "Scale": 13, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.75, "Delay": 1.2, "Acceleration": 0.9, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ2"}, "VertexIndex": 1, "Scale": 16, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.7, "Delay": 0.85, "Acceleration": 0.83, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ2"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ3"}, "VertexIndex": 1, "Scale": 16.753, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.76, "Delay": 0.6, "Acceleration": 1.53, "Radius": 20}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeBallX"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX2"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamPupilX"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.5, "Delay": 3, "Acceleration": 3, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeBallY"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY2"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamPupilY"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.5, "Delay": 3, "Acceleration": 3, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHighlightLX"}, "VertexIndex": 2, "Scale": 0.65, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHighlightLY"}, "VertexIndex": 1, "Scale": 0.8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHighlightLZ"}, "VertexIndex": 3, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8.8}, "Mobility": 0.96, "Delay": 1.1, "Acceleration": 0.95, "Radius": 8.8}, {"Position": {"X": 0, "Y": 16.6}, "Mobility": 0.89, "Delay": 1.04, "Acceleration": 0.99, "Radius": 7.8}, {"Position": {"X": 0, "Y": 23.4}, "Mobility": 0.86, "Delay": 1.08, "Acceleration": 0.86, "Radius": 6.8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHighlightRX"}, "VertexIndex": 2, "Scale": 0.65, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHighlightRY"}, "VertexIndex": 1, "Scale": 0.8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHighlightRZ"}, "VertexIndex": 3, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8.8}, "Mobility": 0.96, "Delay": 1.1, "Acceleration": 0.95, "Radius": 8.8}, {"Position": {"X": 0, "Y": 16.6}, "Mobility": 0.89, "Delay": 1.04, "Acceleration": 0.99, "Radius": 7.8}, {"Position": {"X": 0, "Y": 23.4}, "Mobility": 0.86, "Delay": 1.08, "Acceleration": 0.86, "Radius": 6.8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamPupilL"}, "VertexIndex": 2, "Scale": 0.7, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9.6}, "Mobility": 0.91, "Delay": 0.9, "Acceleration": 1.1, "Radius": 9.6}, {"Position": {"X": 0, "Y": 16.7}, "Mobility": 0.87, "Delay": 1.14, "Acceleration": 0.55, "Radius": 7.1}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting15", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamPupilR"}, "VertexIndex": 2, "Scale": 0.7, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9.6}, "Mobility": 0.91, "Delay": 0.9, "Acceleration": 1.1, "Radius": 9.6}, {"Position": {"X": 0, "Y": 16.7}, "Mobility": 0.87, "Delay": 1.14, "Acceleration": 0.55, "Radius": 7.1}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting16", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamPupilL2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.96, "Delay": 1.01, "Acceleration": 0.76, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting17", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamPupilR2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.96, "Delay": 1.01, "Acceleration": 0.76, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting18", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyelidsL1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.81, "Acceleration": 1.5, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting19", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyelidsR1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.81, "Acceleration": 1.5, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting20", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyelashesL"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamEyelidsL2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 0.99, "Acceleration": 0.7, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting21", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyelashesR"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamEyelidsR2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 0.99, "Acceleration": 0.7, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting22", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMouthOpenY"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamMouthY"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 11}, "Mobility": 0.85, "Delay": 1.05, "Acceleration": 1.23, "Radius": 11}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting23", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBrowsY"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamPupil"}, "VertexIndex": 1, "Scale": 1.039, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.91, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting24", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX2"}, "Weight": 45, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ2"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairCX1"}, "VertexIndex": 2, "Scale": 0.55, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairCX2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9.5}, "Mobility": 0.95, "Delay": 0.91, "Acceleration": 0.83, "Radius": 9.5}, {"Position": {"X": 0, "Y": 15.7}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 0.7, "Radius": 6.2}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting25", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX2"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ2"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY2"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairLX1"}, "VertexIndex": 2, "Scale": 0.6, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairLX2"}, "VertexIndex": 1, "Scale": 1.1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9.5}, "Mobility": 0.95, "Delay": 0.91, "Acceleration": 0.83, "Radius": 9.5}, {"Position": {"X": 0, "Y": 15.7}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 0.7, "Radius": 6.2}], "Normalization": {"Position": {"Minimum": -20, "Default": 0, "Maximum": 20}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting26", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX2"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ2"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY2"}, "Weight": 10, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairRX1"}, "VertexIndex": 2, "Scale": 0.6, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairRX2"}, "VertexIndex": 1, "Scale": 1.1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9.5}, "Mobility": 0.95, "Delay": 0.91, "Acceleration": 0.83, "Radius": 9.5}, {"Position": {"X": 0, "Y": 15.7}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 0.7, "Radius": 6.2}], "Normalization": {"Position": {"Minimum": -20, "Default": 0, "Maximum": 20}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting27", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY2"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairLY1"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairLY2"}, "VertexIndex": 1, "Scale": 0.9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairRY1"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairRY2"}, "VertexIndex": 1, "Scale": 0.9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairCY1"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairCY2"}, "VertexIndex": 1, "Scale": 0.9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7.1}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.83, "Radius": 7.1}, {"Position": {"X": 0, "Y": 12.1}, "Mobility": 0.85, "Delay": 1.18, "Acceleration": 0.8, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting28", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX2"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ2"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY2"}, "Weight": 15, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamSHairLX1"}, "VertexIndex": 3, "Scale": 0.55, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSHairLX2"}, "VertexIndex": 2, "Scale": 0.55, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSHairLX3"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9.5}, "Mobility": 0.96, "Delay": 0.91, "Acceleration": 0.83, "Radius": 9.5}, {"Position": {"X": 0, "Y": 15.7}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.7, "Radius": 6.2}, {"Position": {"X": 0, "Y": 22.1}, "Mobility": 0.88, "Delay": 0.79, "Acceleration": 0.68, "Radius": 6.4}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting29", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX2"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ2"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY2"}, "Weight": 15, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamSHairRX1"}, "VertexIndex": 3, "Scale": 0.55, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSHairRX2"}, "VertexIndex": 2, "Scale": 0.55, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSHairRX3"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9.5}, "Mobility": 0.96, "Delay": 0.91, "Acceleration": 0.83, "Radius": 9.5}, {"Position": {"X": 0, "Y": 15.7}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.7, "Radius": 6.2}, {"Position": {"X": 0, "Y": 22.1}, "Mobility": 0.88, "Delay": 0.79, "Acceleration": 0.68, "Radius": 6.4}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting30", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY2"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBHairY1"}, "VertexIndex": 2, "Scale": 1.4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBHairY2"}, "VertexIndex": 1, "Scale": 0.95, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSHairLY1"}, "VertexIndex": 2, "Scale": 1.4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSHairLY2"}, "VertexIndex": 1, "Scale": 0.95, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSHairRY1"}, "VertexIndex": 2, "Scale": 1.4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSHairRY2"}, "VertexIndex": 1, "Scale": 0.95, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7.1}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.83, "Radius": 7.1}, {"Position": {"X": 0, "Y": 12.1}, "Mobility": 0.85, "Delay": 1.18, "Acceleration": 0.8, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting31", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY2"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX2"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ2"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairA1"}, "VertexIndex": 2, "Scale": 0.7, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairA2"}, "VertexIndex": 1, "Scale": 1.4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8.3}, "Mobility": 0.96, "Delay": 0.91, "Acceleration": 0.83, "Radius": 8.3}, {"Position": {"X": 0, "Y": 14.2}, "Mobility": 0.93, "Delay": 0.8, "Acceleration": 0.7, "Radius": 5.9}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting32", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY2"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX2"}, "Weight": 35, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ2"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBHairX1"}, "VertexIndex": 2, "Scale": 0.7, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBHairX2"}, "VertexIndex": 1, "Scale": 1.7, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.96, "Delay": 0.91, "Acceleration": 0.83, "Radius": 10}, {"Position": {"X": 0, "Y": 17}, "Mobility": 0.93, "Delay": 0.8, "Acceleration": 0.7, "Radius": 7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting33", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX2"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY2"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ2"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamArm1"}, "VertexIndex": 1, "Scale": 1.6, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamArm2"}, "VertexIndex": 2, "Scale": 1.3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamArm3"}, "VertexIndex": 3, "Scale": 1.8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12.9}, "Mobility": 0.86, "Delay": 0.88, "Acceleration": 0.73, "Radius": 12.9}, {"Position": {"X": 0, "Y": 19.9}, "Mobility": 0.83, "Delay": 0.78, "Acceleration": 0.79, "Radius": 7}, {"Position": {"X": 0, "Y": 26.5}, "Mobility": 0.83, "Delay": 0.8, "Acceleration": 0.82, "Radius": 6.6}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting34", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX2"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ2"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBreastX3"}, "VertexIndex": 2, "Scale": 1.1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBreastX4"}, "VertexIndex": 1, "Scale": 1.8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8.9}, "Mobility": 0.94, "Delay": 0.99, "Acceleration": 0.99, "Radius": 8.9}, {"Position": {"X": 0, "Y": 14.7}, "Mobility": 0.88, "Delay": 0.93, "Acceleration": 0.97, "Radius": 5.8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting35", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY2"}, "Weight": 65, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBreastX1"}, "VertexIndex": 3, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBreastX2"}, "VertexIndex": 2, "Scale": 0.6, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBreastY1"}, "VertexIndex": 2, "Scale": 0.8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBreastY2"}, "VertexIndex": 1, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8.4}, "Mobility": 0.92, "Delay": 0.93, "Acceleration": 0.96, "Radius": 8.4}, {"Position": {"X": 0, "Y": 15.5}, "Mobility": 0.88, "Delay": 0.91, "Acceleration": 0.9, "Radius": 7.1}, {"Position": {"X": 0, "Y": 21.1}, "Mobility": 0.83, "Delay": 0.86, "Acceleration": 0.89, "Radius": 5.6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting36", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX2"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ2"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamClothesX1"}, "VertexIndex": 2, "Scale": 0.8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamClothesX2"}, "VertexIndex": 1, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10.3}, "Mobility": 0.95, "Delay": 0.91, "Acceleration": 0.83, "Radius": 10.3}, {"Position": {"X": 0, "Y": 17.1}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 0.7, "Radius": 6.8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting37", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY2"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamClothesY1"}, "VertexIndex": 2, "Scale": 0.9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamClothesY2"}, "VertexIndex": 1, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10.3}, "Mobility": 0.91, "Delay": 0.89, "Acceleration": 0.83, "Radius": 10.3}, {"Position": {"X": 0, "Y": 17.1}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 0.7, "Radius": 6.8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}