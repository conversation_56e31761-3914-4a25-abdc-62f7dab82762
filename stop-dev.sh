#!/bin/bash

# Meta Studio 開発サーバー停止スクリプト

PROJECT_DIR="/Users/<USER>/Dev/meta-studio"
PID_FILE="$PROJECT_DIR/.dev-server.pid"

if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null 2>&1; then
        echo "開発サーバー (PID: $PID) を停止中..."
        kill $PID
        
        # プロセス終了を確認
        sleep 2
        if ps -p $PID > /dev/null 2>&1; then
            echo "強制終了します..."
            kill -9 $PID
        fi
        
        echo "✅ 開発サーバーを停止しました"
    else
        echo "⚠️  PIDファイルに記録されたプロセスは既に終了しています"
    fi
    
    rm -f "$PID_FILE"
else
    echo "⚠️  PIDファイルが見つかりません。手動でプロセスを確認してください:"
    echo "ps aux | grep 'next dev'"
    echo "lsof -i :8080"
fi

# ポート8080の残存プロセスもチェック
PORT_PID=$(lsof -ti:8080 2>/dev/null)
if [ ! -z "$PORT_PID" ]; then
    echo "ポート8080を使用中のプロセス (PID: $PORT_PID) も終了します..."
    kill $PORT_PID
fi

echo "🔌 ポート8080が解放されました"