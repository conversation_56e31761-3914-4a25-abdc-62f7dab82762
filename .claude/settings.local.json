{"permissions": {"allow": ["Bash(ls:*)", "Bash(bun create:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(bun install:*)", "Bash(bun run:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(bunx next dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(bun add:*)", "Bash(npx expo start:*)", "Bash(git init:*)", "Bash(git remote add:*)", "Bash(git push:*)", "Bash(gh repo create:*)", "Bash(git remote remove:*)", "Bash(git rm:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git branch:*)", "Bash(rg:*)", "Bash(npm run build:*)", "Bash(npm i:*)", "Bash(rm:*)", "Bash(npm run dev:*)", "Bash(HOST=127.0.0.1 npm run dev)", "Bash(HOST=0.0.0.0 PORT=8080 npm run dev)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(chmod:*)", "Bash(./start-dev.sh:*)", "Bash(./stop-dev.sh:*)", "Bash(find:*)", "Bash(cp:*)", "Bash(npx eslint:*)", "Bash(npx next build:*)", "Bash(timeout 20s npx next build)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(/Users/<USER>/Dev/meta-studio/start-dev.sh:*)", "WebFetch(domain:github.com)", "Bash(ccusage)", "Bash(claude config get preferredNotifChannel)", "<PERSON><PERSON>(claude config --help)", "<PERSON><PERSON>(claude config set --help)", "WebFetch(domain:docs.anthropic.com)", "Bash(claude config set:*)", "<PERSON><PERSON>(echo:*)", "Bash(claude --version)", "<PERSON><PERSON>(claude config list)", "<PERSON><PERSON>(claude init)", "Bash(/Users/<USER>/.bun/bin/claude --version)", "Bash(/Users/<USER>/.bun/bin/claude \"help\")", "WebFetch(domain:console.anthropic.com)", "WebFetch(domain:support.anthropic.com)", "<PERSON>sh(claude /logout)", "Bash(claude update)", "<PERSON><PERSON>(claude)", "Bash(npx tsc:*)", "Bash(node:*)", "<PERSON><PERSON>(sed:*)", "mcp__ide__getDiagnostics", "Bash(kill:*)", "Bash(osascript:*)", "Bash(open /Users/<USER>/Dev/meta-studio/debug-console.html)", "Bash(open debug-console.html)", "Bash(npm view:*)", "Bash(bun remove:*)", "Bash(ccusage daily)", "Bash(raycast)", "<PERSON><PERSON>(open:*)"], "deny": []}}