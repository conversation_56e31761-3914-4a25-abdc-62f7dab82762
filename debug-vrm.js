const puppeteer = require('puppeteer');

async function debugVRM() {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: null,
    args: ['--start-maximized']
  });
  
  const page = await browser.newPage();
  
  // コンソールログをキャプチャ
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('📋') || text.includes('🔧') || text.includes('✅') || text.includes('❌') || text.includes('🎯') || text.includes('📦') || text.includes('⏱️')) {
      console.log(`[BROWSER] ${text}`);
    }
  });
  
  // エラーをキャプチャ
  page.on('pageerror', error => {
    console.log(`[PAGE ERROR] ${error.message}`);
  });
  
  try {
    console.log('Navigating to http://localhost:8080...');
    await page.goto('http://localhost:8080', { waitUntil: 'networkidle0' });
    
    console.log('Page loaded. Waiting for VRM logs...');
    
    // 30秒間ログを監視
    await new Promise(resolve => setTimeout(resolve, 30000));
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await browser.close();
  }
}

debugVRM().catch(console.error);