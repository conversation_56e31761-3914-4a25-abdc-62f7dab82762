/**
 * 統一モデルシステム - 型定義
 * VRM、LIVE2D、FBX、GLB等の全モデル形式を統一的に扱うための型定義
 */

// サポートするモデル形式
export enum ModelFormat {
  VRM = 'vrm',
  LIVE2D = 'live2d',
  GLB = 'glb',
  GLTF = 'gltf',
  FBX = 'fbx',
  OBJ = 'obj',
  PMX = 'pmx',
  MMD = 'mmd',
  MIXAMO = 'mixamo',
  UNKNOWN = 'unknown'
}

// モデルタイプ
export enum ModelType {
  AVATAR = 'avatar',      // アバター（VRM、LIVE2D等）
  PROP = 'prop',          // 小道具
  ENVIRONMENT = 'environment', // 環境
  EFFECT = 'effect'       // エフェクト
}

// モデル機能
export interface ModelCapabilities {
  expressions: boolean;     // 表情制御
  animations: boolean;      // アニメーション
  physics: boolean;         // 物理演算
  lipSync: boolean;         // リップシンク
  eyeTracking: boolean;     // 視線追跡
  blinking: boolean;        // まばたき
  morphTargets: boolean;    // モーフターゲット
  bones: boolean;           // ボーン制御
  materials: boolean;       // マテリアル制御
}

// モデルメタデータ
export interface ModelMetadata {
  title?: string;
  author?: string;
  version?: string;
  description?: string;
  license?: string;
  thumbnail?: string;
  tags?: string[];
  created?: Date;
  modified?: Date;
  fileSize?: number;
  [key: string]: any;
}

// モデルデータ（形式固有）
export interface ModelData {
  // VRM用
  vrm?: any;
  scene?: THREE.Scene;
  
  // LIVE2D用
  live2dModel?: any;
  
  // 汎用3D用
  mesh?: THREE.Mesh;
  group?: THREE.Group;
  
  // アニメーション
  animations?: THREE.AnimationClip[];
  mixer?: THREE.AnimationMixer;
  
  // その他
  [key: string]: any;
}

// 統一モデルインターフェース
export interface UniversalModel {
  id: string;
  name: string;
  type: ModelType;
  format: ModelFormat;
  data: ModelData;
  capabilities: ModelCapabilities;
  metadata: ModelMetadata;
  
  // 状態管理
  isLoaded: boolean;
  isVisible: boolean;
  isAnimating: boolean;
  
  // ファイル情報
  fileName: string;
  fileSize: number;
  arrayBuffer?: ArrayBuffer;
  
  // レンダリング情報
  boundingBox?: THREE.Box3;
  position?: THREE.Vector3;
  rotation?: THREE.Euler;
  scale?: THREE.Vector3;
}

// モデル読み込み結果
export interface ModelLoadResult {
  success: boolean;
  model?: UniversalModel;
  error?: string;
  warnings?: string[];
}

// モデル検証結果
export interface ModelValidationResult {
  isValid: boolean;
  format: ModelFormat;
  confidence: number; // 0-1の信頼度
  issues?: string[];
}

// アニメーション制御
export interface AnimationState {
  name: string;
  weight: number;
  time: number;
  loop: boolean;
  playing: boolean;
}

// 表情制御
export interface ExpressionState {
  [expressionName: string]: number; // 0-1の重み
}

// リップシンク用ビセーム
export interface VisemeData {
  a: number;    // あ
  i: number;    // い
  u: number;    // う
  e: number;    // え
  o: number;    // お
  [key: string]: number;
}

// モデル制御インターフェース
export interface ModelController {
  // 基本制御
  setVisible(visible: boolean): void;
  setPosition(position: THREE.Vector3): void;
  setRotation(rotation: THREE.Euler): void;
  setScale(scale: THREE.Vector3): void;
  
  // アニメーション制御
  playAnimation(name: string, loop?: boolean): void;
  stopAnimation(name: string): void;
  setAnimationWeight(name: string, weight: number): void;
  
  // 表情制御
  setExpression(name: string, weight: number): void;
  setExpressions(expressions: ExpressionState): void;
  
  // リップシンク
  setLipSync(visemes: VisemeData): void;
  
  // まばたき
  setBlink(weight: number): void;
  setAutoBlinking(enabled: boolean): void;
  
  // 視線制御
  setLookAt(target: THREE.Vector3): void;
  
  // 更新
  update(deltaTime: number): void;
  
  // 破棄
  dispose(): void;
}

// イベント
export interface ModelEvent {
  type: string;
  model: UniversalModel;
  data?: any;
}

export type ModelEventListener = (event: ModelEvent) => void;

// モデルローダーオプション
export interface ModelLoaderOptions {
  enablePhysics?: boolean;
  enableAnimations?: boolean;
  enableExpressions?: boolean;
  autoPlay?: boolean;
  scale?: number;
  position?: THREE.Vector3;
  rotation?: THREE.Euler;
}
