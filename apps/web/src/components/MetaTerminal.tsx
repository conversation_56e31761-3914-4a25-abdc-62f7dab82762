'use client';

import { useEffect, useRef, useState } from 'react';
import { Crown, Swords, Play, Terminal as TerminalIcon, Plus, X, Settings } from 'lucide-react';
import { processPxCommand, processPxCommandStreaming } from '@/lib/ai-agents';

// xterm.js動的インポート用の型定義
interface TerminalConstructor {
  new (options?: any): any;
}
interface FitAddonConstructor {
  new (): any;
}
interface WebglAddonConstructor {
  new (): any;
}

interface TerminalSession {
  id: string;
  name: string;
  icon: React.ReactNode;
  status: string;
  color: string;
  terminal?: any;
  fitAddon?: any;
}

export default function MetaTerminal() {
  const [sessions, setSessions] = useState<TerminalSession[]>([]);
  const [activeSessionId, setActiveSessionId] = useState<string>('');
  const [isClient, setIsClient] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const terminalRef = useRef<HTMLDivElement>(null);
  const sessionIdCounter = useRef(1);

  // クライアントサイドでのみ実行
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 初期セッション作成
  useEffect(() => {
    if (isClient && sessions.length === 0) {
      createInitialSessions().then(newSessions => {
        setSessions(newSessions);
        if (newSessions.length > 0) {
          setActiveSessionId(newSessions[0].id);
        }
      });
    }
  }, [isClient]);

  // アクティブターミナル表示
  useEffect(() => {
    if (isClient && terminalRef.current && activeSessionId) {
      const activeSession = sessions.find(s => s.id === activeSessionId);
      if (activeSession?.terminal) {
        terminalRef.current.innerHTML = '';
        activeSession.terminal.open(terminalRef.current);
        if (activeSession.fitAddon) {
          activeSession.fitAddon.fit();
        }
      }
    }
  }, [activeSessionId, sessions, isClient]);

  // 新規ターミナル作成
  const createNewTerminal = async () => {
    if (!isClient || typeof window === 'undefined') return;

    try {
      const [
        { Terminal },
        { FitAddon },
        { WebglAddon }
      ] = await Promise.all([
        import('xterm'),
        import('xterm-addon-fit'),
        import('xterm-addon-webgl')
      ]);

      await import('xterm/css/xterm.css');

      const newSessionId = `session-${sessionIdCounter.current++}`;
      const newSession: TerminalSession = {
        id: newSessionId,
        name: `Terminal ${sessionIdCounter.current}`,
        icon: <TerminalIcon size={14} />,
        status: 'Ready',
        color: 'text-accent'
      };

      const terminal = new Terminal({
        fontSize: 14,
        fontFamily: 'Menlo, Monaco, "Courier New", monospace',
        theme: {
          background: '#1a1a1a',
          foreground: '#ffffff',
          cursor: '#ffffff',
          selection: '#555555',
          black: '#000000',
          red: '#ff5555',
          green: '#50fa7b',
          yellow: '#f1fa8c',
          blue: '#bd93f9',
          magenta: '#ff79c6',
          cyan: '#8be9fd',
          white: '#bbbbbb',
          brightBlack: '#555555',
          brightRed: '#ff5555',
          brightGreen: '#50fa7b',
          brightYellow: '#f1fa8c',
          brightBlue: '#bd93f9',
          brightMagenta: '#ff79c6',
          brightCyan: '#8be9fd',
          brightWhite: '#ffffff'
        },
        cursorBlink: true,
        rows: 30,
        cols: 120
      });

      const fitAddon = new FitAddon();
      terminal.loadAddon(fitAddon);

      try {
        const webglAddon = new WebglAddon();
        terminal.loadAddon(webglAddon);
      } catch (e) {
        console.warn('WebGL addon failed to load:', e);
      }

      // 初期表示
      terminal.writeln(`╭─ Meta Studio Terminal - ${newSession.name}`);
      terminal.writeln(`├─ Status: ${newSession.status}`);
      terminal.writeln(`╰─ Type 'px help' for commands`);
      terminal.writeln('');
      terminal.write(`meta-studio>   `);

      // 入力処理を設定（IME対応版）
      let currentInput = '';
      let cursorPosition = 0;
      let isComposing = false;
      let compositionText = '';
      
      // IME対応のためのtextarea追加
      const setupIME = () => {
        const terminalContainer = document.querySelector('.terminal-container') as HTMLElement;
        if (terminalContainer) {
          const textArea = document.createElement('textarea');
          textArea.style.position = 'absolute';
          textArea.style.left = '-9999px';
          textArea.style.opacity = '0';
          terminalContainer.appendChild(textArea);
          
          textArea.addEventListener('compositionstart', () => {
            isComposing = true;
            compositionText = '';
          });
          
          textArea.addEventListener('compositionupdate', (e: any) => {
            if (compositionText.length > 0) {
              for (let i = 0; i < compositionText.length; i++) {
                terminal.write('\b \b');
              }
            }
            compositionText = e.data;
            terminal.write(compositionText);
          });
          
          textArea.addEventListener('compositionend', (e: any) => {
            isComposing = false;
            if (compositionText.length > 0) {
              for (let i = 0; i < compositionText.length; i++) {
                terminal.write('\b \b');
              }
            }
            const finalText = e.data;
            currentInput = currentInput.slice(0, cursorPosition) + finalText + currentInput.slice(cursorPosition);
            cursorPosition += finalText.length;
            
            const prompt = `meta-studio>   `;
            terminal.write('\r\x1b[K');
            terminal.write(`\x1b[36m${prompt}\x1b[0m${currentInput}`);
            const totalPos = prompt.length + cursorPosition;
            terminal.write(`\x1b[${totalPos + 1}G`);
            
            compositionText = '';
            textArea.value = '';
          });
          
          terminal.onKey(() => textArea.focus());
        }
      };
      
      setTimeout(setupIME, 100);
      
      terminal.onData(async (data) => {
        if (isComposing) return;
        const code = data.charCodeAt(0);
        
        // ESCシーケンス処理（矢印キーなど）
        if (code === 27) { // ESC
          if (data.length > 1) {
            const sequence = data.slice(1);
            if (sequence === '[A' || sequence === '[B') { // Up/Down arrow
              return;
            } else if (sequence === '[C') { // Right arrow
              if (cursorPosition < currentInput.length) {
                cursorPosition++;
                terminal.write('\x1b[C');
              }
              return;
            } else if (sequence === '[D') { // Left arrow
              if (cursorPosition > 0) {
                cursorPosition--;
                terminal.write('\x1b[D');
              }
              return;
            }
          }
          return;
        }
        
        // Ctrl-C (中断) - ただしコピー以外の場合のみ
        if (code === 3) {
          // 空行またはClipboard APIが利用できない場合は中断として処理
          if (!currentInput.trim() || !navigator.clipboard) {
            terminal.writeln('\n^C');
            currentInput = '';
            cursorPosition = 0;
            terminal.write(`\x1b[36mmeta-studio>   \x1b[0m`);
          }
          return;
        }
        
        if (code === 13) { // Enter
          terminal.writeln('');
          if (currentInput.trim()) {
            // ストリーミング対応のコマンド処理
            const isClaudeCommand = currentInput.trim().startsWith('claude') || 
                                  !['px', 'cost', 'auth', 'login', 'logout', 'help', 'status', 'agents', 'projext'].includes(currentInput.trim().split(' ')[0]);
            
            if (isClaudeCommand) {
              // Claude命令の場合はストリーミング表示
              terminal.writeln('🚀 Claude処理開始...');
              try {
                const result = await processPxCommandStreaming(currentInput.trim(), terminal);
              } catch (error) {
                terminal.writeln(`\x1b[31m❌ Error: ${error}\x1b[0m`);
              }
            } else {
              // 通常コマンドは従来通り
              terminal.writeln('\x1b[33m⚡ Executing...\x1b[0m');
              try {
                const result = await processPxCommand(currentInput.trim());
                
                // 結果を行ごとに分割して表示（カラーハイライト適用）
                const lines = result.split('\n');
                lines.forEach(line => {
                  // エラー行は赤色
                  if (line.includes('Error') || line.includes('❌')) {
                    terminal.writeln(`\x1b[31m${line}\x1b[0m`);
                  }
                  // 成功行は緑色
                  else if (line.includes('✅') || line.includes('Success')) {
                    terminal.writeln(`\x1b[32m${line}\x1b[0m`);
                  }
                  // 警告行は黄色
                  else if (line.includes('⚠️') || line.includes('Warning')) {
                    terminal.writeln(`\x1b[33m${line}\x1b[0m`);
                  }
                  // 情報行は青色
                  else if (line.includes('ℹ️') || line.includes('Info')) {
                    terminal.writeln(`\x1b[36m${line}\x1b[0m`);
                  }
                  // その他は通常色
                  else {
                    terminal.writeln(line);
                  }
                });
              } catch (error) {
                terminal.writeln(`\x1b[31m❌ Error: ${error}\x1b[0m`);
              }
            }
          }
          currentInput = '';
          cursorPosition = 0;
          terminal.write(`\x1b[36mmeta-studio>   \x1b[0m`);
        } else if (code === 127) { // Backspace
          if (cursorPosition > 0) {
            // カーソル位置の文字を削除
            currentInput = currentInput.slice(0, cursorPosition - 1) + currentInput.slice(cursorPosition);
            cursorPosition--;
            
            // 画面更新: 現在行をクリアして再描画
            const prompt = `meta-studio>   `;
            terminal.write('\r\x1b[K'); // 行をクリア
            terminal.write(`\x1b[36m${prompt}\x1b[0m${currentInput}`);
            
            // カーソルを正しい位置に移動
            const totalPos = prompt.length + cursorPosition;
            terminal.write(`\x1b[${totalPos + 1}G`);
          }
        } else if (code >= 32) { // 印刷可能文字
          // カーソル位置に文字を挿入
          currentInput = currentInput.slice(0, cursorPosition) + data + currentInput.slice(cursorPosition);
          cursorPosition++;
          
          // 画面更新: 現在行をクリアして再描画
          const prompt = `meta-studio>   `;
          terminal.write('\r\x1b[K'); // 行をクリア
          terminal.write(`\x1b[36m${prompt}\x1b[0m${currentInput}`);
          
          // カーソルを正しい位置に移動
          const totalPos = prompt.length + cursorPosition;
          terminal.write(`\x1b[${totalPos + 1}G`);
        }
      });

      const sessionWithTerminal = {
        ...newSession,
        terminal,
        fitAddon
      };

      setSessions(prev => [...prev, sessionWithTerminal]);
      setActiveSessionId(newSessionId);
    } catch (error) {
      console.error('Failed to create new terminal:', error);
    }
  };

  // ターミナルセッション削除
  const closeTerminalSession = (sessionId: string) => {
    setSessions(prev => {
      const filtered = prev.filter(s => s.id !== sessionId);
      
      // アクティブなセッションが削除された場合、別のセッションをアクティブにする
      if (activeSessionId === sessionId && filtered.length > 0) {
        setActiveSessionId(filtered[0].id);
      }
      
      return filtered;
    });
  };

  const createInitialSessions = async (): Promise<TerminalSession[]> => {
    if (typeof window === 'undefined') return [];

    try {
      // 動的インポート
      const [
        { Terminal },
        { FitAddon },
        { WebglAddon }
      ] = await Promise.all([
        import('xterm'),
        import('xterm-addon-fit'),
        import('xterm-addon-webgl')
      ]);

      // CSS動的ロード
      await import('xterm/css/xterm.css');

      const initialSessions = [
        {
          id: 'main',
          name: 'Terminal',
          icon: <TerminalIcon size={14} />,
          status: 'Ready',
          color: 'text-accent'
        }
      ];

      const newSessions = initialSessions.map(config => {
        const terminal = new Terminal({
          fontSize: 14,
          fontFamily: 'Menlo, Monaco, "Courier New", monospace',
          theme: {
            background: '#1a1a1a',
            foreground: '#ffffff',
            cursor: '#ffffff',
            selection: '#555555',
            black: '#000000',
            red: '#ff5555',
            green: '#50fa7b',
            yellow: '#f1fa8c',
            blue: '#bd93f9',
            magenta: '#ff79c6',
            cyan: '#8be9fd',
            white: '#bbbbbb',
            brightBlack: '#555555',
            brightRed: '#ff5555',
            brightGreen: '#50fa7b',
            brightYellow: '#f1fa8c',
            brightBlue: '#bd93f9',
            brightMagenta: '#ff79c6',
            brightCyan: '#8be9fd',
            brightWhite: '#ffffff'
          },
          cursorBlink: true,
          rows: 30,
          cols: 120
        });

        const fitAddon = new FitAddon();
        terminal.loadAddon(fitAddon);

        try {
          const webglAddon = new WebglAddon();
          terminal.loadAddon(webglAddon);
        } catch (e) {
          console.warn('WebGL addon failed to load:', e);
        }

        // 初期表示
        terminal.writeln(`╭─ Meta Studio Terminal - ${config.name}`);
        terminal.writeln(`├─ Status: ${config.status}`);
        terminal.writeln(`╰─ Type 'px help' for commands`);
        terminal.writeln('');
        terminal.write(`meta-studio>   `);

        // 入力処理（改善版：矢印キー・Ctrl-C・IME対応）
        let currentInput = '';
        let cursorPosition = 0;
        let isComposing = false;
        let compositionText = '';
        
        // IME（日本語入力）対応（バグ修正版）
        const terminalElement = terminalRef.current;
        if (terminalElement) {
          // 既存のtextAreaがあれば削除
          const existingTextArea = terminalElement.querySelector('textarea');
          if (existingTextArea) {
            existingTextArea.remove();
          }

          const textArea = document.createElement('textarea');
          textArea.style.position = 'absolute';
          textArea.style.left = '-9999px';
          textArea.style.top = '0';
          textArea.style.width = '1px';
          textArea.style.height = '1px';
          textArea.style.opacity = '0';
          textArea.style.border = 'none';
          textArea.style.outline = 'none';
          textArea.style.resize = 'none';
          textArea.style.zIndex = '-1';
          textArea.autocomplete = 'off';
          textArea.autocorrect = 'off';
          textArea.autocapitalize = 'off';
          textArea.spellcheck = false;
          textArea.setAttribute('inputmode', 'text');
          
          terminalElement.appendChild(textArea);
          
          let compositionStartPos = 0;
          let isInComposition = false;
          
          // ターミナルクリック時にフォーカス
          const handleTerminalClick = () => {
            textArea.focus();
          };
          
          // IMEイベントハンドラー（開始位置修正版）
          const handleCompositionStart = () => {
            isInComposition = true;
            isComposing = true;
            compositionStartPos = cursorPosition;
            compositionText = '';
            
            // textAreaの値をクリアして正確な開始位置を確保
            textArea.value = '';
          };
          
          const handleCompositionUpdate = (e: CompositionEvent) => {
            if (!isInComposition) return;
            
            const newCompositionText = e.data || '';
            
            // 前の変換候補をクリア
            if (compositionText.length > 0) {
              cursorPosition = compositionStartPos;
              currentInput = currentInput.slice(0, compositionStartPos) + currentInput.slice(compositionStartPos + compositionText.length);
            }
            
            // 新しい変換候補を表示
            compositionText = newCompositionText;
            if (compositionText.length > 0) {
              currentInput = currentInput.slice(0, cursorPosition) + compositionText + currentInput.slice(cursorPosition);
            }
            
            // 画面更新
            const prompt = `meta-studio>   `;
            terminal.write('\r\x1b[K');
            terminal.write(`\x1b[36m${prompt}\x1b[0m${currentInput}`);
            
            // カーソル位置を変換候補の末尾に設定
            const displayPos = prompt.length + compositionStartPos + compositionText.length;
            terminal.write(`\x1b[${displayPos + 1}G`);
          };
          
          const handleCompositionEnd = (e: CompositionEvent) => {
            if (!isInComposition) return;
            
            isInComposition = false;
            isComposing = false;
            
            const finalText = e.data || '';
            
            // 変換候補をクリア
            if (compositionText.length > 0) {
              cursorPosition = compositionStartPos;
              currentInput = currentInput.slice(0, compositionStartPos) + currentInput.slice(compositionStartPos + compositionText.length);
            }
            
            // 確定テキストを挿入
            if (finalText.length > 0) {
              currentInput = currentInput.slice(0, cursorPosition) + finalText + currentInput.slice(cursorPosition);
              cursorPosition += finalText.length;
            }
            
            // 画面更新（確定後）
            const prompt = `meta-studio>   `;
            terminal.write('\r\x1b[K');
            terminal.write(`\x1b[36m${prompt}\x1b[0m${currentInput}`);
            const totalPos = prompt.length + cursorPosition;
            terminal.write(`\x1b[${totalPos + 1}G`);
            
            // 状態をリセット
            compositionText = '';
            textArea.value = '';
            
            // 再度フォーカスを設定（次の入力準備）
            setTimeout(() => {
              textArea.focus();
            }, 10);
          };
          
          // キーダウンイベント（エンターキー等の制御）
          const handleKeyDown = async (e: KeyboardEvent) => {
            if (isInComposition) return;
            
            if (e.key === 'Enter') {
              e.preventDefault();
              terminal.writeln('');
              if (currentInput.trim()) {
                // ストリーミング対応のコマンド処理
                const isClaudeCommand = currentInput.trim().startsWith('claude') || 
                                      !['px', 'cost', 'auth', 'login', 'logout', 'help', 'status', 'agents', 'projext'].includes(currentInput.trim().split(' ')[0]);
                
                if (isClaudeCommand) {
                  // Claude命令の場合はストリーミング表示
                  terminal.writeln('🚀 Claude処理開始...');
                  try {
                    const result = await processPxCommandStreaming(currentInput.trim(), terminal);
                  } catch (error) {
                    terminal.writeln(`\x1b[31m❌ Error: ${error}\x1b[0m`);
                  }
                } else {
                  // 通常コマンドは従来通り
                  terminal.writeln('\x1b[33m⚡ Executing...\x1b[0m');
                  try {
                    const result = await processPxCommand(currentInput.trim());
                    
                    // 結果を行ごとに分割して表示（カラーハイライト適用）
                    const lines = result.split('\n');
                    lines.forEach(line => {
                      if (line.includes('Error') || line.includes('❌')) {
                        terminal.writeln(`\x1b[31m${line}\x1b[0m`);
                      } else if (line.includes('✅') || line.includes('Success')) {
                        terminal.writeln(`\x1b[32m${line}\x1b[0m`);
                      } else if (line.includes('⚠️') || line.includes('Warning')) {
                        terminal.writeln(`\x1b[33m${line}\x1b[0m`);
                      } else if (line.includes('ℹ️') || line.includes('Info')) {
                        terminal.writeln(`\x1b[36m${line}\x1b[0m`);
                      } else {
                        terminal.writeln(line);
                      }
                    });
                  } catch (error) {
                    terminal.writeln(`\x1b[31m❌ Error: ${error}\x1b[0m`);
                  }
                }
              }
              currentInput = '';
              cursorPosition = 0;
              terminal.write(`\x1b[36mmeta-studio>   \x1b[0m`);
            } else if (e.key === 'Backspace') {
              e.preventDefault();
              if (cursorPosition > 0) {
                currentInput = currentInput.slice(0, cursorPosition - 1) + currentInput.slice(cursorPosition);
                cursorPosition--;
                
                const prompt = `meta-studio>   `;
                terminal.write('\r\x1b[K');
                terminal.write(`\x1b[36m${prompt}\x1b[0m${currentInput}`);
                const totalPos = prompt.length + cursorPosition;
                terminal.write(`\x1b[${totalPos + 1}G`);
              }
            } else if (e.ctrlKey || e.metaKey) {
              // ショートカット処理
              e.preventDefault();
              
              switch (e.key.toLowerCase()) {
                case 'c':
                  // Ctrl+C / Cmd+C: 選択テキストをクリップボードにコピー
                  if (currentInput.trim()) {
                    navigator.clipboard.writeText(currentInput);
                    terminal.writeln(`\n📋 コピーしました: "${currentInput}"`);
                    terminal.write(`\x1b[36mmeta-studio>   \x1b[0m${currentInput}`);
                    const totalPos = `meta-studio>   `.length + cursorPosition;
                    terminal.write(`\x1b[${totalPos + 1}G`);
                  }
                  break;
                  
                case 'x':
                  // Ctrl+X / Cmd+X: 現在の入力をカット
                  if (currentInput.trim()) {
                    navigator.clipboard.writeText(currentInput);
                    terminal.writeln(`\n✂️ カットしました: "${currentInput}"`);
                    currentInput = '';
                    cursorPosition = 0;
                    terminal.write(`\x1b[36mmeta-studio>   \x1b[0m`);
                  }
                  break;
                  
                case 'v':
                  // Ctrl+V / Cmd+V: クリップボードからペースト
                  navigator.clipboard.readText().then(text => {
                    if (text) {
                      // 改行を除去してペースト
                      const cleanText = text.replace(/[\r\n]+/g, ' ').trim();
                      currentInput = currentInput.slice(0, cursorPosition) + cleanText + currentInput.slice(cursorPosition);
                      cursorPosition += cleanText.length;
                      
                      const prompt = `meta-studio>   `;
                      terminal.write('\r\x1b[K');
                      terminal.write(`\x1b[36m${prompt}\x1b[0m${currentInput}`);
                      const totalPos = prompt.length + cursorPosition;
                      terminal.write(`\x1b[${totalPos + 1}G`);
                      
                      terminal.writeln(`\n📝 ペーストしました: "${cleanText}"`);
                      terminal.write(`\x1b[36m${prompt}\x1b[0m${currentInput}`);
                      terminal.write(`\x1b[${totalPos + 1}G`);
                    }
                  }).catch(() => {
                    terminal.writeln('\n❌ クリップボードアクセスに失敗しました');
                    terminal.write(`\x1b[36mmeta-studio>   \x1b[0m${currentInput}`);
                    const totalPos = `meta-studio>   `.length + cursorPosition;
                    terminal.write(`\x1b[${totalPos + 1}G`);
                  });
                  break;
                  
                case 'a':
                  // Ctrl+A / Cmd+A: 全選択（カーソルを行頭へ）
                  cursorPosition = 0;
                  const prompt = `meta-studio>   `;
                  terminal.write(`\x1b[${prompt.length + 1}G`);
                  break;
                  
                case 'e':
                  // Ctrl+E / Cmd+E: 行末へ移動
                  cursorPosition = currentInput.length;
                  const promptE = `meta-studio>   `;
                  terminal.write(`\x1b[${promptE.length + cursorPosition + 1}G`);
                  break;
                  
                case 'k':
                  // Ctrl+K / Cmd+K: カーソル位置から行末まで削除
                  if (cursorPosition < currentInput.length) {
                    const deletedText = currentInput.slice(cursorPosition);
                    navigator.clipboard.writeText(deletedText);
                    currentInput = currentInput.slice(0, cursorPosition);
                    
                    const promptK = `meta-studio>   `;
                    terminal.write('\r\x1b[K');
                    terminal.write(`\x1b[36m${promptK}\x1b[0m${currentInput}`);
                    const totalPos = promptK.length + cursorPosition;
                    terminal.write(`\x1b[${totalPos + 1}G`);
                  }
                  break;
                  
                case 'u':
                  // Ctrl+U / Cmd+U: 行全体をクリア
                  if (currentInput.length > 0) {
                    navigator.clipboard.writeText(currentInput);
                    currentInput = '';
                    cursorPosition = 0;
                    terminal.write('\r\x1b[K');
                    terminal.write(`\x1b[36mmeta-studio>   \x1b[0m`);
                  }
                  break;
              }
            }
          };
          
          // 通常の入力（英数字）
          const handleInput = (e: any) => {
            if (isInComposition) return;
            
            const inputText = e.target.value;
            if (inputText) {
              currentInput = currentInput.slice(0, cursorPosition) + inputText + currentInput.slice(cursorPosition);
              cursorPosition += inputText.length;
              
              const prompt = `meta-studio>   `;
              terminal.write('\r\x1b[K');
              terminal.write(`\x1b[36m${prompt}\x1b[0m${currentInput}`);
              const totalPos = prompt.length + cursorPosition;
              terminal.write(`\x1b[${totalPos + 1}G`);
              
              e.target.value = '';
            }
          };
          
          // イベントリスナー登録
          terminalElement.addEventListener('click', handleTerminalClick);
          textArea.addEventListener('compositionstart', handleCompositionStart);
          textArea.addEventListener('compositionupdate', handleCompositionUpdate);
          textArea.addEventListener('compositionend', handleCompositionEnd);
          textArea.addEventListener('keydown', handleKeyDown);
          textArea.addEventListener('input', handleInput);
          
          // 初期フォーカス
          setTimeout(() => textArea.focus(), 100);
        }

        // 基本的なキーボード入力処理（フォールバック）
        terminal.onData(async (data) => {
          // IME入力中は通常の入力処理をスキップ
          if (isComposing) return;
          
          const code = data.charCodeAt(0);
          
          // ESCシーケンス処理（矢印キーなど）
          if (code === 27) { // ESC
            if (data.length > 1) {
              const sequence = data.slice(1);
              if (sequence === '[A' || sequence === '[B') { // Up/Down arrow
                return;
              } else if (sequence === '[C') { // Right arrow
                if (cursorPosition < currentInput.length) {
                  cursorPosition++;
                  terminal.write('\x1b[C');
                }
                return;
              } else if (sequence === '[D') { // Left arrow
                if (cursorPosition > 0) {
                  cursorPosition--;
                  terminal.write('\x1b[D');
                }
                return;
              }
            }
            return;
          }
          
          // Ctrl-C (中断)
          if (code === 3) {
            terminal.writeln('\n^C');
            currentInput = '';
            cursorPosition = 0;
            terminal.write(`\x1b[36mmeta-studio>   \x1b[0m`);
            return;
          }
          
          if (code === 13) { // Enter
            terminal.writeln('');
            if (currentInput.trim()) {
              // ストリーミング対応のコマンド処理
              const isClaudeCommand = currentInput.trim().startsWith('claude') || 
                                    !['px', 'cost', 'auth', 'login', 'logout', 'help', 'status', 'agents', 'projext'].includes(currentInput.trim().split(' ')[0]);
              
              if (isClaudeCommand) {
                // Claude命令の場合はストリーミング表示
                terminal.writeln('🚀 Claude処理開始...');
                try {
                  const result = await processPxCommandStreaming(currentInput.trim(), terminal);
                } catch (error) {
                  terminal.writeln(`\x1b[31m❌ Error: ${error}\x1b[0m`);
                }
              } else {
                // 通常コマンドは従来通り
                terminal.writeln('\x1b[33m⚡ Executing...\x1b[0m');
                try {
                  const result = await processPxCommand(currentInput.trim());
                  
                  // 結果を行ごとに分割して表示（カラーハイライト適用）
                  const lines = result.split('\n');
                  lines.forEach(line => {
                    if (line.includes('Error') || line.includes('❌')) {
                      terminal.writeln(`\x1b[31m${line}\x1b[0m`);
                    } else if (line.includes('✅') || line.includes('Success')) {
                      terminal.writeln(`\x1b[32m${line}\x1b[0m`);
                    } else if (line.includes('⚠️') || line.includes('Warning')) {
                      terminal.writeln(`\x1b[33m${line}\x1b[0m`);
                    } else if (line.includes('ℹ️') || line.includes('Info')) {
                      terminal.writeln(`\x1b[36m${line}\x1b[0m`);
                    } else {
                      terminal.writeln(line);
                    }
                  });
                } catch (error) {
                  terminal.writeln(`\x1b[31m❌ Error: ${error}\x1b[0m`);
                }
              }
            }
            currentInput = '';
            cursorPosition = 0;
            terminal.write(`\x1b[36mmeta-studio>   \x1b[0m`);
          } else if (code === 127) { // Backspace
            if (cursorPosition > 0) {
              currentInput = currentInput.slice(0, cursorPosition - 1) + currentInput.slice(cursorPosition);
              cursorPosition--;
              
              const prompt = `meta-studio>   `;
              terminal.write('\r\x1b[K');
              terminal.write(`\x1b[36m${prompt}\x1b[0m${currentInput}`);
              const totalPos = prompt.length + cursorPosition;
              terminal.write(`\x1b[${totalPos + 1}G`);
            }
          } else if (code >= 32) { // 印刷可能文字
            currentInput = currentInput.slice(0, cursorPosition) + data + currentInput.slice(cursorPosition);
            cursorPosition++;
            
            const prompt = `meta-studio>   `;
            terminal.write('\r\x1b[K');
            terminal.write(`\x1b[36m${prompt}\x1b[0m${currentInput}`);
            const totalPos = prompt.length + cursorPosition;
            terminal.write(`\x1b[${totalPos + 1}G`);
          }
        });

        return {
          ...config,
          terminal,
          fitAddon
        };
      });

      return newSessions;
    } catch (error) {
      console.error('Failed to create terminal sessions:', error);
      return [];
    }
  };

  if (!isClient) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-base-content/50">Loading terminal...</div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-base-300">
      {/* ターミナルタブ */}
      <div className="flex items-center justify-between p-2 border-b border-base-content/10">
        <div className="flex space-x-1">
          {sessions.map((session) => (
            <button
              key={session.id}
              onClick={() => setActiveSessionId(session.id)}
              className={`px-3 py-1 rounded text-xs flex items-center space-x-2 transition-colors ${
                activeSessionId === session.id
                  ? 'bg-primary text-primary-content'
                  : 'bg-base-200 text-base-content hover:bg-base-100'
              }`}
            >
              {session.icon}
              <span>{session.name}</span>
              {sessions.length > 1 && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    closeTerminalSession(session.id);
                  }}
                  className="ml-1 hover:text-error"
                >
                  <X size={12} />
                </button>
              )}
            </button>
          ))}
        </div>
        
        <div className="flex items-center space-x-2">
          <button 
            onClick={createNewTerminal}
            className="p-1 hover:bg-base-200 rounded tooltip tooltip-left"
            data-tip="新規ターミナル"
          >
            <Plus size={16} />
          </button>
          <button 
            onClick={() => setShowSettings(!showSettings)}
            className="p-1 hover:bg-base-200 rounded tooltip tooltip-left"
            data-tip="設定"
          >
            <Settings size={16} />
          </button>
        </div>
      </div>

      {/* 設定パネル */}
      {showSettings && (
        <div className="absolute top-12 right-2 bg-base-100 rounded-lg shadow-lg p-4 z-50 w-64 border border-base-content/20">
          <h3 className="text-sm font-bold mb-4">ターミナル設定</h3>
          
          <div className="space-y-3">
            <div>
              <label className="text-xs">フォントサイズ</label>
              <input 
                type="range" 
                min="10" 
                max="20" 
                defaultValue="14"
                className="range range-xs range-primary w-full" 
              />
            </div>
            
            <div>
              <label className="text-xs">カラーテーマ</label>
              <select className="select select-bordered select-xs w-full">
                <option>ダーク（標準）</option>
                <option>ライト</option>
                <option>Monokai</option>
                <option>Solarized</option>
              </select>
            </div>
            
            <div>
              <label className="text-xs">行数</label>
              <input 
                type="number" 
                defaultValue="30" 
                className="input input-bordered input-xs w-full" 
              />
            </div>
            
            <div>
              <label className="text-xs">列数</label>
              <input 
                type="number" 
                defaultValue="120" 
                className="input input-bordered input-xs w-full" 
              />
            </div>
            
            <div className="divider my-2"></div>
            
            <button 
              onClick={() => setShowSettings(false)}
              className="btn btn-sm btn-primary w-full"
            >
              閉じる
            </button>
          </div>
        </div>
      )}

      {/* ターミナル表示エリア */}
      <div className="flex-1 p-2 relative terminal-container">
        <div
          ref={terminalRef}
          className="w-full h-full bg-[#1a1a1a] rounded border border-base-content/10"
        />
      </div>
    </div>
  );
}