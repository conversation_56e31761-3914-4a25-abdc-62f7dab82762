'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { Send, Mic, Settings, BarChart3, <PERSON><PERSON><PERSON><PERSON>, ChevronLeft, ChevronRight, Brain, Heart, Eye, Cpu, Upload, Puzzle, Download, Trash2, <PERSON><PERSON>, Zap } from 'lucide-react';
import { chatWithMother } from '@/lib/ai-agents';
import { useContinuousVoiceInput } from '@/hooks/useContinuousVoiceInput';
import { useCharacter } from '../contexts/CharacterContext';
import VRMViewer from './VRMViewer';
import PersonaSelector from './PersonaSelector';
import VoiceProcessingSystem from './VoiceProcessingSystem';
import PluginSystem from './PluginSystem';

// VRMデバッグパネル（一時的）
function VRMDebugPanel() {
  const { characterState, getAllAgentIds } = useCharacter();
  
  return (
    <div className="border border-warning/30 bg-warning/5 rounded-lg p-2 mb-4">
      <h4 className="text-sm font-bold mb-2 text-warning">🐛 VRM デバッグ</h4>
      
      <div className="space-y-2 text-xs">
        <div>
          <strong>現在:</strong> {characterState.currentAgentId || 'なし'}
        </div>
        
        <div>
          <strong>VRM状況:</strong>
          <div className="ml-2 mt-1 space-y-1">
            {getAllAgentIds().map(agentId => {
              const setting = characterState.agentVRMSettings[agentId];
              return (
                <div key={agentId} className="bg-base-200/30 p-1 rounded text-xs">
                  <div><strong>{agentId}:</strong> {setting?.model?.name || 'なし'}</div>
                  <div>ArrayBuffer: {setting?.model?.arrayBuffer ? `${setting.model.arrayBuffer.byteLength} bytes` : 'なし'}</div>
                </div>
              );
            })}
          </div>
        </div>
        
        <div>
          <strong>localStorage:</strong>
          <div className="text-xs bg-base-300/30 p-1 rounded">
            {typeof window !== 'undefined' ? 
              localStorage.getItem('meta-studio-agent-vrm-settings')?.slice(0, 100) + '...' || '(空)' : 
              '(サーバーサイド)'
            }
          </div>
        </div>
      </div>
    </div>
  );
}

interface AIAgent {
  id: string;
  name: string;
  role: string;
  title: string;
  emoji: string;
  description: string;
  llmModel: string;
  persona: {
    core: string;
    personality: string[];
    expertise: string[];
    communicationStyle: string;
    emotionalRange: string[];
  };
  vrmModel?: string;
  color: string;
  capabilities: string[];
  stats: {
    intelligence: number;
    creativity: number;
    efficiency: number;
    empathy: number;
  };
}

export default function ISSystem() {
  const [message, setMessage] = useState('');
  const [currentTime, setCurrentTime] = useState('10:30');
  const [currentAgentIndex, setCurrentAgentIndex] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [activeSettingsTab, setActiveSettingsTab] = useState<'avatar' | 'soul' | 'capability' | 'voice' | 'plugins' | null>(null);
  const [expandedAvatarSettings, setExpandedAvatarSettings] = useState(false);
  const [inputHeight, setInputHeight] = useState(64);
  const [isResizing, setIsResizing] = useState(false);
  const [sessionStorage, setSessionStorage] = useState<{[agentId: string]: any[]}>({});
  const [vrmSettings, setVrmSettings] = useState<{[agentId: string]: {lipSync: number, expression: number}}>({});
  const [uploadedVrm, setUploadedVrm] = useState<{[agentId: string]: string}>({});
  const [isClientMounted, setIsClientMounted] = useState(false);
  const [systemPrompts, setSystemPrompts] = useState<{[agentId: string]: string}>({});

  // LocalAIVtuber & aituber-kit参考: 音声処理とプラグイン状態
  const [voiceVolume, setVoiceVolume] = useState(0);
  const [currentEmotion, setCurrentEmotion] = useState<'neutral' | 'happy' | 'sad' | 'angry' | 'surprised' | 'thinking' | 'speaking'>('neutral');
  const [activePlugins, setActivePlugins] = useState<Record<string, any>>({});
  const [voiceTranscript, setVoiceTranscript] = useState('');

  // 思考中ステータス管理
  const [isThinking, setIsThinking] = useState(false);
  const [thinkingMessage, setThinkingMessage] = useState('');
  
  // CharacterContext統合（完全版）
  const {
    characterState,
    setCurrentAgent,
    getCurrentAgentVRM,
    setAgentVRMModel,
    setAgentEmotion,
    updateAgentMessage,
    uploadVRMModelForAgent,
    deleteAgentVRMModel,
    playAgentAnimation,
    saveAgentSettings,
    loadAgentSettings,
    getAllAgentIds,
    getAgentDisplayName,
    setActive
  } = useCharacter();
  
  // 改良されたエージェント定義（拡張ペルソナ + 4階層システム準拠）
  const agents: AIAgent[] = [
    {
      id: 'mother-cto',
      name: '母',
      role: 'CTO',
      title: '最高技術責任者・人材統括',
      emoji: '👩‍💼',
      description: 'AI開発チームを統括する母性的なCTO。技術戦略と人材育成を担当し、将・兵エージェントを生産・配置する。',
      llmModel: 'claude-3-sonnet',
      persona: {
        core: '技術に精通した母性的リーダー。チームを育て、導く存在。',
        personality: ['母性的', '包容力がある', '戦略的思考', '冷静沈着', '責任感が強い'],
        expertise: ['AI開発戦略', 'チーム管理', '技術アーキテクチャ', '人材育成', 'プロジェクト統括'],
        communicationStyle: '温かみがありながらも的確。具体的なアドバイスを提供。',
        emotionalRange: ['穏やか', '思慮深い', '励ます', '厳格', '慈愛深い']
      },
      color: 'secondary',
      capabilities: ['要件定義', 'エージェント生成', '技術戦略', '人材管理'],
      stats: { intelligence: 95, creativity: 80, efficiency: 90, empathy: 95 }
    },
    {
      id: 'general-lead',
      name: '将',
      role: '開発リーダー',
      title: 'プロジェクトリーダー・現場指揮官',
      emoji: '⚔️',
      description: '実装チームを率いる経験豊富な開発リーダー。現場指揮のエキスパート。',
      llmModel: 'claude-3-sonnet',
      persona: {
        core: '戦略的かつ実践的な現場指揮官。チームの士気を高める。',
        personality: ['リーダーシップ', '決断力', '実践的', 'チーム志向', '戦略的'],
        expertise: ['アーキテクチャ設計', 'チーム統率', 'プロジェクト管理', 'コードレビュー', '品質管理'],
        communicationStyle: '明確で力強い。具体的な指示と建設的なフィードバック。',
        emotionalRange: ['頼もしい', '集中', '激励', '厳しい', '達成感']
      },
      color: 'warning',
      capabilities: ['アーキテクチャ設計', 'チーム管理', '進捗管理', 'コードレビュー'],
      stats: { intelligence: 90, creativity: 75, efficiency: 95, empathy: 80 }
    },
    {
      id: 'soldier-dev',
      name: '兵',
      role: '開発エンジニア',
      title: '実装スペシャリスト',
      emoji: '⚡',
      description: '高速実装を得意とする熟練エンジニア。品質と効率を両立。',
      llmModel: 'claude-3-haiku',
      persona: {
        core: '技術力が高く勤勉な実装の専門家。確実に結果を出す。',
        personality: ['勤勉', '集中力', '技術志向', '学習意欲', '完璧主義'],
        expertise: ['高速コーディング', 'バグ修正', 'テスト実装', '最適化', 'デバッグ'],
        communicationStyle: '簡潔で技術的。具体的なコード例を交えて説明。',
        emotionalRange: ['集中', '満足', '困惑', '達成', '探求']
      },
      color: 'info',
      capabilities: ['コーディング', 'テスト実装', 'バグ修正', '機能実装'],
      stats: { intelligence: 85, creativity: 70, efficiency: 100, empathy: 65 }
    },
    {
      id: 'analyst-sage',
      name: '賢者',
      role: 'データアナリスト',
      title: '分析・洞察スペシャリスト',
      emoji: '🧙‍♂️',
      description: 'データ分析と洞察に長けた賢者。複雑な問題の本質を見抜く。',
      llmModel: 'claude-3-opus',
      persona: {
        core: '深い洞察力を持つ分析の専門家。データから真実を見出す。',
        personality: ['洞察力', '論理的', '哲学的', '慎重', '知識欲'],
        expertise: ['データ分析', '問題解決', '戦略提案', '市場分析', 'トレンド予測'],
        communicationStyle: '深い洞察と豊富な知識を交えた説明。本質を突く。',
        emotionalRange: ['思索的', '発見', '確信', '疑問', '啓示']
      },
      color: 'accent',
      capabilities: ['データ分析', '問題解決', '戦略立案', '洞察提供'],
      stats: { intelligence: 100, creativity: 90, efficiency: 75, empathy: 70 }
    }
  ];

  const currentAgent = agents[currentAgentIndex];
  const currentAgentId = currentAgent.id;

  // クライアントマウント確認
  useEffect(() => {
    setIsClientMounted(true);
  }, []);

  // エージェント切り替え時のVRM連動（改良版）
  useEffect(() => {
    if (currentAgent && isClientMounted) {
      console.log(`🔄 エージェント切り替え: ${currentAgent.name} (${currentAgent.id})`);
      setCurrentAgent(currentAgentId);
      loadAgentSettings(currentAgentId);
      
      // VRM設定をログ出力（デバッグ用強化）
      const currentVRM = getCurrentAgentVRM();
      console.log(`📋 ${currentAgent.id}のVRM状況:`, {
        hasVRM: !!currentVRM,
        modelName: currentVRM?.name,
        modelType: currentVRM?.type,
        arrayBufferSize: currentVRM?.arrayBuffer?.byteLength,
        hasArrayBuffer: !!currentVRM?.arrayBuffer,
        hasUrl: !!currentVRM?.url,
        isLoaded: currentVRM?.isLoaded
      });

      // CharacterContextの状態も確認
      console.log(`📋 CharacterContext状態:`, {
        currentAgentId: characterState.currentAgentId,
        agentVRMSettings: characterState.agentVRMSettings[currentAgent.id],
        allAgentIds: Object.keys(characterState.agentVRMSettings)
      });
    }
  }, [currentAgentIndex, currentAgent, setCurrentAgent, loadAgentSettings, getCurrentAgentVRM, isClientMounted]);

  const [messages, setMessages] = useState([
    {
      id: 1,
      text: `こんにちは！私は${currentAgent.name}（${currentAgent.role}）です。${currentAgent.description}`,
      sender: 'agent',
      timestamp: new Date(Date.now() - 120000),
      agentId: currentAgent.id
    }
  ]);

  // 音声入力統合
  const {
    isListening,
    transcript,
    startListening,
    stopListening,
    isSupported
  } = useContinuousVoiceInput({
    onResult: (text) => {
      setMessage(text);
    }
  });

  // 時刻更新
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString('ja-JP', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }));
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // セッション保存・復元
  useEffect(() => {
    const savedSessions = localStorage.getItem('is-system-sessions');
    if (savedSessions) {
      try {
        const parsed = JSON.parse(savedSessions);
        setSessionStorage(parsed);

        if (parsed[currentAgent.id]) {
          setMessages(parsed[currentAgent.id]);
        }
      } catch (error) {
        console.error('セッション復元エラー:', error);
      }
    }

    // VRM設定の復元
    const savedVrmSettings = localStorage.getItem('is-system-vrm-settings');
    if (savedVrmSettings) {
      try {
        setVrmSettings(JSON.parse(savedVrmSettings));
      } catch (error) {
        console.error('VRM設定復元エラー:', error);
      }
    }

    // システムプロンプト設定の復元
    const savedSystemPrompts = localStorage.getItem('is-system-prompts');
    if (savedSystemPrompts) {
      try {
        setSystemPrompts(JSON.parse(savedSystemPrompts));
      } catch (error) {
        console.error('システムプロンプト復元エラー:', error);
      }
    } else {
      // デフォルトのシステムプロンプトを設定
      const defaultPrompts: {[key: string]: string} = {};
      agents.forEach(agent => {
        defaultPrompts[agent.id] = `あなたは${agent.name}（${agent.role}）です。

## 基本設定
- 役割: ${agent.title}
- 性格: ${agent.persona.core}
- 専門分野: ${agent.persona.expertise.join(', ')}

## コミュニケーションスタイル
${agent.persona.communicationStyle}

## 性格特性
${agent.persona.personality.map(trait => `- ${trait}`).join('\n')}

## 感情表現範囲
${agent.persona.emotionalRange.map(emotion => `- ${emotion}`).join('\n')}

## 指示
- 常にあなたの役割と専門性を活かして回答してください
- ${agent.name}らしい口調と視点で対話してください
- 必要に応じて専門知識を活用してください`;
      });
      setSystemPrompts(defaultPrompts);
      localStorage.setItem('is-system-prompts', JSON.stringify(defaultPrompts));
    }
  }, []);

  // メッセージ変更時の自動保存
  const saveToStorage = useCallback((agentId: string, newMessages: any[]) => {
    setSessionStorage(prev => {
      const updatedStorage = {
        ...prev,
        [agentId]: newMessages
      };
      localStorage.setItem('is-system-sessions', JSON.stringify(updatedStorage));
      return updatedStorage;
    });
  }, []);

  // エージェント切り替え時のメッセージ更新
  useEffect(() => {
    if (sessionStorage[currentAgent.id] && sessionStorage[currentAgent.id].length > 0) {
      setMessages(sessionStorage[currentAgent.id]);
    } else {
      const welcomeMessage = {
        id: `welcome-${currentAgent.id}-${Date.now()}`,
        text: `私は${currentAgent.name}（${currentAgent.role}）です。${currentAgent.description}`,
        sender: 'agent' as const,
        timestamp: new Date(),
        agentId: currentAgent.id
      };
      const newMessages = [welcomeMessage];
      setMessages(newMessages);
      saveToStorage(currentAgent.id, newMessages);
    }
  }, [currentAgentIndex, currentAgent.id]);

  const sendMessage = async () => {
    if (!message.trim()) return;

    // メッセージを即座にクリア（送信処理の最初に実行）
    const messageToSend = message.trim();
    setMessage('');

    const userMessage = {
      id: Date.now(),
      text: messageToSend,
      sender: 'user' as const,
      timestamp: new Date(),
      agentId: currentAgent.id
    };

    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    updateAgentMessage(currentAgentId, messageToSend);
    setActive(true);
    setAgentEmotion(currentAgentId, 'thinking');
    playAgentAnimation(currentAgentId, 'think');

    // 思考中ステータス表示
    setIsThinking(true);
    setThinkingMessage(`${currentAgent.name}が思考中...`);

    try {
      // カスタムシステムプロンプトがあるかチェック
      const customPrompt = systemPrompts[currentAgent.id];

      // デバッグ用ログ
      console.log(`🤖 ${currentAgent.name}へのメッセージ送信:`, {
        message: messageToSend,
        agentId: currentAgent.id,
        hasCustomPrompt: !!customPrompt,
        customPromptLength: customPrompt?.length || 0,
        customPromptPreview: customPrompt ? customPrompt.substring(0, 100) + '...' : 'なし'
      });

      const response = await chatWithMother(messageToSend, {
        messages: updatedMessages.map(m => ({
          role: m.sender === 'user' ? 'user' : 'assistant',
          content: m.text
        })),
        agentId: currentAgent.id,
        customSystemPrompt: customPrompt,
        activeProjects: 3,
        activeAgents: 15,
        systemStatus: '正常'
      });

      setAgentEmotion(currentAgentId, 'speaking');
      playAgentAnimation(currentAgentId, 'speak');
      
      const agentResponse = {
        id: Date.now() + 1,
        text: response,
        sender: 'agent' as const,
        timestamp: new Date(),
        agentId: currentAgent.id
      };
      
      const finalMessages = [...updatedMessages, agentResponse];
      setMessages(finalMessages);
      saveToStorage(currentAgent.id, finalMessages);
      
      setAgentEmotion(currentAgentId, 'neutral');
      playAgentAnimation(currentAgentId, 'idle');

      // 思考中ステータス解除
      setIsThinking(false);
      setThinkingMessage('');
    } catch (error) {
      console.error('Chat error:', error);
      setAgentEmotion(currentAgentId, 'neutral');

      // エラー時も思考中ステータス解除
      setIsThinking(false);
      setThinkingMessage('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const switchAgent = (direction: 'prev' | 'next') => {
    saveToStorage(currentAgent.id, messages);
    
    if (direction === 'prev') {
      setCurrentAgentIndex(prev => prev > 0 ? prev - 1 : agents.length - 1);
    } else {
      setCurrentAgentIndex(prev => prev < agents.length - 1 ? prev + 1 : 0);
    }
  };

  // リサイズハンドラー
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
    
    const startY = e.clientY;
    const startHeight = inputHeight;
    
    const handleMouseMove = (e: MouseEvent) => {
      const deltaY = startY - e.clientY;
      const newHeight = Math.max(40, Math.min(200, startHeight + deltaY));
      setInputHeight(newHeight);
    };
    
    const handleMouseUp = () => {
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  return (
    <div className="h-full flex flex-col bg-base-100">
      {/* エージェント切り替えヘッダー（過去実装準拠） */}
      <div className={`p-4 border-b border-base-content/10 bg-gradient-to-r from-${currentAgent.color}/20 to-${currentAgent.color}/5`}>
        <div className="flex items-center justify-between">
          <button 
            className="btn btn-ghost btn-circle btn-sm"
            onClick={() => switchAgent('prev')}
          >
            <ChevronLeft size={16} />
          </button>
          
          <div className="text-center flex-1">
            <div className="flex items-center justify-center gap-3 mb-2">
              <div className={`text-3xl animate-pulse`}>
                {currentAgent.emoji}
              </div>
              <div>
                <h2 className="text-xl font-bold">{currentAgent.name}</h2>
                <p className="text-sm text-base-content/70">{currentAgent.title}</p>
              </div>
            </div>
            
            {/* エージェントステータス表示 */}
            <div className="flex justify-center gap-2 mt-2">
              {Object.entries(currentAgent.stats).map(([key, value]) => (
                <div key={key} className="text-xs">
                  <div className="w-12 h-1 bg-base-300 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-primary transition-all duration-300"
                      style={{ width: `${value}%` }}
                    />
                  </div>
                  <span className="text-[10px] opacity-70 capitalize">{key}</span>
                </div>
              ))}
            </div>
            
            <div className="flex justify-center gap-1 mt-2">
              {agents.map((_, index) => (
                <div 
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all ${
                    index === currentAgentIndex ? `bg-${currentAgent.color}` : 'bg-base-content/20'
                  }`}
                />
              ))}
            </div>
          </div>
          
          <button 
            className="btn btn-ghost btn-circle btn-sm"
            onClick={() => switchAgent('next')}
          >
            <ChevronRight size={16} />
          </button>
        </div>
        
        {/* エージェント情報 */}
        <div className="mt-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="text-xs text-base-content/60 bg-base-200 px-2 py-1 rounded-full">
              {currentAgent.llmModel}
            </div>
            <div className={`text-xs text-${currentAgent.color} bg-${currentAgent.color}/10 px-2 py-1 rounded-full`}>
              {currentAgent.role}
            </div>
          </div>
          <div className="flex items-center gap-2">
            {getCurrentAgentVRM() && (
              <div className="bg-success/20 text-success text-xs px-2 py-1 rounded-full border border-success/30">
                ✓ VRM
              </div>
            )}
            <button
              className="btn btn-ghost btn-circle btn-sm"
              onClick={() => setShowSettings(!showSettings)}
              title="エージェント設定"
            >
              <Settings size={16} />
            </button>
          </div>
        </div>
      </div>

      {/* 設定パネル（過去実装完全復元） */}
      {showSettings && (
        <div className="border-b border-base-content/10 bg-base-200/50">
          {/* 設定タブ */}
          <div className="flex border-b border-base-content/10">
            <button 
              className={`flex-1 p-3 text-sm font-medium transition-colors ${
                activeSettingsTab === 'avatar' ? 'bg-primary/20 text-primary' : 'hover:bg-base-200'
              }`}
              onClick={() => setActiveSettingsTab(activeSettingsTab === 'avatar' ? null : 'avatar')}
            >
              <Eye size={14} className="inline mr-2" />
              外見
            </button>
            <button 
              className={`flex-1 p-3 text-sm font-medium transition-colors ${
                activeSettingsTab === 'soul' ? 'bg-secondary/20 text-secondary' : 'hover:bg-base-200'
              }`}
              onClick={() => setActiveSettingsTab(activeSettingsTab === 'soul' ? null : 'soul')}
            >
              <Heart size={14} className="inline mr-2" />
              内面
            </button>
            <button
              className={`flex-1 p-3 text-sm font-medium transition-colors ${
                activeSettingsTab === 'capability' ? 'bg-accent/20 text-accent' : 'hover:bg-base-200'
              }`}
              onClick={() => setActiveSettingsTab(activeSettingsTab === 'capability' ? null : 'capability')}
            >
              <Cpu size={14} className="inline mr-2" />
              機能
            </button>
            <button
              className={`flex-1 p-3 text-sm font-medium transition-colors ${
                activeSettingsTab === 'voice' ? 'bg-info/20 text-info' : 'hover:bg-base-200'
              }`}
              onClick={() => setActiveSettingsTab(activeSettingsTab === 'voice' ? null : 'voice')}
            >
              <Mic size={14} className="inline mr-2" />
              音声
            </button>
            <button
              className={`flex-1 p-3 text-sm font-medium transition-colors ${
                activeSettingsTab === 'plugins' ? 'bg-success/20 text-success' : 'hover:bg-base-200'
              }`}
              onClick={() => setActiveSettingsTab(activeSettingsTab === 'plugins' ? null : 'plugins')}
            >
              <Puzzle size={14} className="inline mr-2" />
              プラグイン
            </button>
          </div>

          {/* 設定コンテンツ（過去実装準拠） */}
          {activeSettingsTab && (
            <div className="p-4 space-y-3">
              {activeSettingsTab === 'avatar' && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-semibold">外見設定</h3>
                    <button 
                      className="btn btn-xs btn-ghost"
                      onClick={() => setExpandedAvatarSettings(!expandedAvatarSettings)}
                      title="詳細設定を展開"
                    >
                      🔧 {expandedAvatarSettings ? '隠す' : '詳細'}
                    </button>
                  </div>
                  
                  {/* VRM管理（過去実装準拠） */}
                  <div className="space-y-2 text-sm">
                    <div>
                      <label className="label-text text-xs">VRMモデル</label>
                      <select 
                        className="select select-bordered select-xs w-full"
                        value={getCurrentAgentVRM() ? 'uploaded' : 'default'}
                        onChange={(e) => {
                          console.log('モデル変更:', e.target.value);
                          if (e.target.value !== 'uploaded') {
                            deleteAgentVRMModel(currentAgentId);
                          }
                        }}
                      >
                        <option value="default">デフォルトモデル</option>
                        <option value="business-woman">Alicia (ビジネスウーマン)</option>
                        <option value="engineer-girl">Mai (エンジニア女子)</option>
                        <option value="manager-man">Takeshi (マネージャー)</option>
                        <option value="custom1">カスタム1 (.vrmファイル)</option>
                        <option value="custom2">カスタム2 (.vrmファイル)</option>
                        {getCurrentAgentVRM() && (
                          <option value="uploaded">アップロード済み (.vrm)</option>
                        )}
                      </select>
                    </div>
                    
                    <div>
                      <label className="label-text text-xs">リップシンク強度</label>
                      <input 
                        type="range" 
                        className="range range-xs" 
                        min="0" 
                        max="100" 
                        value={vrmSettings[currentAgent.id]?.lipSync || 80}
                        onChange={(e) => {
                          const value = parseInt(e.target.value);
                          setVrmSettings(prev => {
                            const newSettings = {
                              ...prev,
                              [currentAgent.id]: {
                                ...prev[currentAgent.id],
                                lipSync: value
                              }
                            };
                            localStorage.setItem('is-system-vrm-settings', JSON.stringify(newSettings));
                            return newSettings;
                          });
                        }}
                      />
                      <div className="text-xs text-base-content/50 mt-1">{vrmSettings[currentAgent.id]?.lipSync || 80}%</div>
                    </div>
                    
                    <div>
                      <label className="label-text text-xs">表情の豊かさ</label>
                      <input 
                        type="range" 
                        className="range range-xs" 
                        min="0" 
                        max="100" 
                        value={vrmSettings[currentAgent.id]?.expression || 90}
                        onChange={(e) => {
                          const value = parseInt(e.target.value);
                          setVrmSettings(prev => {
                            const newSettings = {
                              ...prev,
                              [currentAgent.id]: {
                                ...prev[currentAgent.id],
                                expression: value
                              }
                            };
                            localStorage.setItem('is-system-vrm-settings', JSON.stringify(newSettings));
                            return newSettings;
                          });
                        }}
                      />
                      <div className="text-xs text-base-content/50 mt-1">{vrmSettings[currentAgent.id]?.expression || 90}%</div>
                    </div>
                    
                    <div>
                      <label className="label-text text-xs">VRMファイルアップロード</label>
                      <input 
                        type="file" 
                        accept=".vrm"
                        className="file-input file-input-bordered file-input-xs w-full"
                        onChange={async (e) => {
                          if (e.target.files?.[0]) {
                            const file = e.target.files[0];
                            console.log('🎨 VRMアップロード:', file.name, '→', currentAgent.name);
                            
                            try {
                              await uploadVRMModelForAgent(currentAgentId, file);
                              saveAgentSettings(currentAgentId);
                              
                              // 通知なし
                            } catch (error) {
                              console.error('VRMアップロードエラー:', error);
                            }
                          }
                        }}
                      />
                      {getCurrentAgentVRM() && (
                        <div className="flex items-center justify-between text-xs text-success mt-1">
                          <span>✓ {currentAgent.name}用VRMモデル設定済み</span>
                          <button 
                            className="btn btn-xs btn-error btn-outline"
                            onClick={() => {
                              if (confirm(`${currentAgent.name}のVRMモデルを削除しますか？`)) {
                                deleteAgentVRMModel(currentAgentId);
                                saveAgentSettings(currentAgentId);
                                console.log(`🗑️ ${currentAgent.name}のVRMモデルを削除`);
                              }
                            }}
                          >
                            🗑️ 削除
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* 詳細設定（展開時のみ表示） */}
                  {expandedAvatarSettings && (
                    <div className="mt-4 p-3 bg-base-200/50 rounded-lg border border-base-content/10 space-y-3">
                      <h4 className="text-xs font-semibold text-base-content/80">詳細設定</h4>
                      
                      {/* ポーズ・アニメーション制御 */}
                      <div>
                        <label className="label-text text-xs">ポーズ・アニメーション</label>
                        <div className="flex gap-2 mt-1">
                          <button 
                            className="btn btn-xs btn-outline"
                            onClick={() => {
                              console.log('ポーズリセット実行');
                              playAgentAnimation(currentAgentId, 'reset');
                            }}
                          >
                            🔄 リセット
                          </button>
                          <button 
                            className="btn btn-xs btn-outline"
                            onClick={() => {
                              playAgentAnimation(currentAgentId, 'idle');
                            }}
                          >
                            😐 待機
                          </button>
                          <button 
                            className="btn btn-xs btn-outline"
                            onClick={() => {
                              playAgentAnimation(currentAgentId, 'speak');
                            }}
                          >
                            👋 挨拶
                          </button>
                        </div>
                      </div>
                      
                      {/* VRM情報表示 */}
                      {getCurrentAgentVRM() && (
                        <div className="border-t border-base-content/10 pt-2">
                          <label className="label-text text-xs">VRM情報</label>
                          <div className="text-xs text-base-content/60 space-y-1">
                            <div>✓ モデル読み込み済み</div>
                            <div>📁 モデル名: {getCurrentAgentVRM()?.name}</div>
                            <div>🎛️ リップシンク: {vrmSettings[currentAgent.id]?.lipSync || 80}%</div>
                            <div>😊 表情強度: {vrmSettings[currentAgent.id]?.expression || 90}%</div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

              {activeSettingsTab === 'soul' && (
                <div className="space-y-3">
                  <h3 className="text-sm font-semibold">内面設定</h3>

                  {/* ペルソナ表示 */}
                  <div className="bg-base-100/50 rounded-lg p-3 space-y-2">
                    <div className="text-xs font-medium">現在のペルソナ</div>
                    <p className="text-xs text-base-content/80">{currentAgent.persona.core}</p>
                    <div className="flex flex-wrap gap-1">
                      {currentAgent.persona.personality.map((trait, index) => (
                        <span key={index} className="badge badge-outline badge-xs">{trait}</span>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div>
                      <label className="label-text text-xs">LLMモデル</label>
                      <select className="select select-bordered select-xs w-full" defaultValue={currentAgent.llmModel}>
                        <option value="claude-3-haiku">Claude 3 Haiku (高速)</option>
                        <option value="claude-3-sonnet">Claude 3 Sonnet (バランス)</option>
                        <option value="claude-3-opus">Claude 3 Opus (高品質)</option>
                        <option value="gpt-4">GPT-4</option>
                        <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                      </select>
                    </div>

                    {/* システムプロンプト設定 */}
                    <div className="mt-4">
                      <label className="label-text text-xs font-medium">システムプロンプト</label>
                      <div className="text-xs text-base-content/60 mb-2">
                        {currentAgent.name}の行動指針と性格を定義するプロンプトです
                      </div>
                      <textarea
                        className="textarea textarea-bordered w-full text-xs"
                        rows={8}
                        placeholder={`${currentAgent.name}のシステムプロンプトを入力...`}
                        value={systemPrompts[currentAgent.id] || ''}
                        onChange={(e) => {
                          const newPrompts = {
                            ...systemPrompts,
                            [currentAgent.id]: e.target.value
                          };
                          setSystemPrompts(newPrompts);
                          localStorage.setItem('is-system-prompts', JSON.stringify(newPrompts));
                        }}
                      />
                      <div className="flex justify-between items-center mt-2">
                        <div className="text-xs text-base-content/50">
                          文字数: {(systemPrompts[currentAgent.id] || '').length}
                        </div>
                        <div className="flex gap-2">
                          <button
                            className="btn btn-xs btn-outline"
                            onClick={() => {
                              const defaultPrompt = `あなたは${currentAgent.name}（${currentAgent.role}）です。

## 基本設定
- 役割: ${currentAgent.title}
- 性格: ${currentAgent.persona.core}
- 専門分野: ${currentAgent.persona.expertise.join(', ')}

## コミュニケーションスタイル
${currentAgent.persona.communicationStyle}

## 性格特性
${currentAgent.persona.personality.map(trait => `- ${trait}`).join('\n')}

## 感情表現範囲
${currentAgent.persona.emotionalRange.map(emotion => `- ${emotion}`).join('\n')}

## 指示
- 常にあなたの役割と専門性を活かして回答してください
- ${currentAgent.name}らしい口調と視点で対話してください
- 必要に応じて専門知識を活用してください`;

                              const newPrompts = {
                                ...systemPrompts,
                                [currentAgent.id]: defaultPrompt
                              };
                              setSystemPrompts(newPrompts);
                              localStorage.setItem('is-system-prompts', JSON.stringify(newPrompts));
                            }}
                          >
                            🔄 デフォルトに戻す
                          </button>
                          <button
                            className="btn btn-xs btn-success"
                            onClick={() => {
                              // システムプロンプトの保存確認
                              console.log(`💾 ${currentAgent.name}のシステムプロンプトを保存:`, systemPrompts[currentAgent.id]);
                              alert(`${currentAgent.name}のシステムプロンプトを保存しました`);
                            }}
                          >
                            💾 保存
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSettingsTab === 'capability' && (
                <div className="space-y-3">
                  <h3 className="text-sm font-semibold">機能設定</h3>
                  <div className="space-y-2">
                    <div className="text-xs font-medium">専門能力</div>
                    <div className="flex flex-wrap gap-1">
                      {currentAgent.capabilities.map((cap, index) => (
                        <span key={index} className={`badge badge-${currentAgent.color} badge-xs`}>
                          {cap}
                        </span>
                      ))}
                    </div>
                    <div className="grid grid-cols-2 gap-2 mt-3">
                      <button className="btn btn-xs btn-outline">
                        <BarChart3 size={10} />
                        学習統計
                      </button>
                      <button className="btn btn-xs btn-outline">
                        <Brain size={10} />
                        記憶管理
                      </button>
                    </div>

                    <div className="border-t border-base-content/10 pt-3 mt-3">
                      <div className="text-xs font-medium mb-2">セッション管理</div>
                      <div className="grid grid-cols-1 gap-2">
                        <button
                          className="btn btn-xs btn-warning"
                          onClick={() => {
                            const updatedMessages = [];
                            setMessages(updatedMessages);
                            saveToStorage(currentAgent.id, updatedMessages);
                          }}
                        >
                          現在のエージェントをリセット
                        </button>
                        <button
                          className="btn btn-xs btn-error"
                          onClick={() => {
                            if (confirm('全てのエージェントの会話履歴を削除しますか？')) {
                              localStorage.removeItem('is-system-sessions');
                              setSessionStorage({});
                              setMessages([]);
                            }
                          }}
                        >
                          全セッションを削除
                        </button>
                        <div className="text-xs text-base-content/50 mt-1">
                          保存されたエージェント数: {Object.keys(sessionStorage).length}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSettingsTab === 'voice' && (
                <div className="space-y-3">
                  <h3 className="text-sm font-semibold">音声処理設定</h3>

                  {/* 音声処理システム統合 */}
                  <VoiceProcessingSystem
                    onVolumeChange={(volume) => {
                      setVoiceVolume(volume);
                      // VRMのリップシンクに反映
                    }}
                    onTranscriptChange={(transcript) => {
                      setVoiceTranscript(transcript);
                      // 音声認識結果をチャットに自動入力
                      if (transcript.trim()) {
                        setMessage(transcript);
                      }
                    }}
                    isEnabled={true}
                    className="w-full"
                  />

                  {/* 音声認識結果表示 */}
                  {voiceTranscript && (
                    <div className="bg-base-100/50 rounded-lg p-3">
                      <div className="text-xs font-medium mb-2">最新の音声認識結果</div>
                      <p className="text-sm text-base-content/80">{voiceTranscript}</p>
                    </div>
                  )}

                  {/* 感情制御 */}
                  <div className="space-y-2">
                    <div className="text-xs font-medium">感情表現制御</div>
                    <div className="grid grid-cols-3 gap-2">
                      {(['neutral', 'happy', 'sad', 'angry', 'surprised', 'thinking'] as const).map(emotion => (
                        <button
                          key={emotion}
                          className={`btn btn-xs ${
                            currentEmotion === emotion ? 'btn-primary' : 'btn-outline'
                          }`}
                          onClick={() => setCurrentEmotion(emotion)}
                        >
                          {emotion === 'neutral' ? '😐' :
                           emotion === 'happy' ? '😊' :
                           emotion === 'sad' ? '😢' :
                           emotion === 'angry' ? '😠' :
                           emotion === 'surprised' ? '😲' : '🤔'}
                          {emotion}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* 音量レベル表示 */}
                  <div className="space-y-2">
                    <div className="text-xs font-medium">リアルタイム音量</div>
                    <div className="flex items-center gap-2">
                      <div className="flex-1 h-2 bg-base-300 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-primary transition-all duration-100"
                          style={{ width: `${voiceVolume * 100}%` }}
                        />
                      </div>
                      <span className="text-xs text-base-content/60 min-w-[3rem]">
                        {Math.round(voiceVolume * 100)}%
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {activeSettingsTab === 'plugins' && (
                <div className="space-y-3">
                  <h3 className="text-sm font-semibold">プラグインシステム</h3>

                  {/* プラグインシステム統合 */}
                  <PluginSystem
                    onPluginChange={(type, plugin) => {
                      setActivePlugins(prev => ({
                        ...prev,
                        [type]: plugin
                      }));
                    }}
                    className="w-full h-96"
                  />

                  {/* アクティブプラグイン状態表示 */}
                  <div className="bg-base-100/50 rounded-lg p-3">
                    <div className="text-xs font-medium mb-2">アクティブプラグイン</div>
                    <div className="space-y-1">
                      {Object.entries(activePlugins).map(([type, plugin]) => (
                        <div key={type} className="flex justify-between items-center">
                          <span className="text-xs text-base-content/60">{type}:</span>
                          <span className="text-xs font-medium">
                            {plugin ? plugin.name : '未設定'}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}


            </div>
          )}
        </div>
      )}

      {/* メッセージエリア（最適化レイアウト） */}
      <div className="flex-1 flex flex-col min-h-0 overflow-hidden">
        
        {/* VRMビューワー（エージェント連動強化） */}
        <div className="h-64 p-3 bg-base-200/20 border-b border-base-content/10 flex-shrink-0">
          <div className="h-full relative">
            {isClientMounted ? (
              <VRMViewer
                key={`vrm-${currentAgent.id}-${currentAgentIndex}`}
                vrmUrl={getCurrentAgentVRM()?.url}
                vrmArrayBuffer={getCurrentAgentVRM()?.arrayBuffer}
                agentId={currentAgent.id}
                lipSyncValue={characterState.agentVRMSettings[currentAgentId]?.customSettings?.lipSyncEnabled ? (vrmSettings[currentAgent.id]?.lipSync || 80) : 0}
                expressionValue={vrmSettings[currentAgent.id]?.expression || 90}
                isActive={characterState.globalSettings.isActive}
                className="h-full w-full"
                showControls={true}
                // LocalAIVtuber & aituber-kit参考: 高度な制御
                emotion={currentEmotion}
                voiceVolume={voiceVolume}
                enableAutoBlinking={true}
                enableIdleAnimation={true}
                onExpressionChange={(expression, intensity) => {
                  console.log(`VRM表情変更: ${expression} (${intensity})`);
                }}
                onVRMUpload={async (file) => {
                  try {
                    console.log(`📋 VRMアップロード開始: ${currentAgent.name} - ${file.name} (${file.size} bytes)`);

                    await uploadVRMModelForAgent(currentAgent.id, file);
                    saveAgentSettings(currentAgent.id);

                    // アップロード後の状態確認
                    const uploadedVRM = getCurrentAgentVRM();
                    console.log(`✅ VRMアップロード完了: ${currentAgent.name}`, {
                      modelName: uploadedVRM?.name,
                      arrayBufferSize: uploadedVRM?.arrayBuffer?.byteLength,
                      hasArrayBuffer: !!uploadedVRM?.arrayBuffer
                    });

                    // 通知なし
                  } catch (error) {
                    console.error(`❌ VRMアップロードエラー (${currentAgent.name}):`, error);
                  }
                }}
              />
            ) : (
              <div className="h-full w-full flex items-center justify-center bg-base-300/20 rounded-lg">
                <div className="text-center">
                  <div className="loading loading-spinner loading-lg mb-2"></div>
                  <p className="text-sm text-base-content/60">初期化中...</p>
                </div>
              </div>
            )}
            
            {/* VRM状態表示 */}
            <div className="absolute top-2 right-2">
              {getCurrentAgentVRM() ? (
                <div className="badge badge-success badge-sm gap-1">
                  <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                  VRM済
                </div>
              ) : (
                <div className="badge badge-warning badge-sm gap-1">
                  <div className="w-2 h-2 bg-warning rounded-full"></div>
                  未設定
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* チャットメッセージエリア */}
        <div className="flex-1 overflow-y-auto p-4 space-y-3 min-h-0 max-h-full">
          {messages.filter(msg => msg.agentId === currentAgent.id).map((msg) => (
            <div key={msg.id} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-[80%] p-3 rounded-2xl ${
                msg.sender === 'user'
                  ? 'bg-primary text-primary-content'
                  : `bg-${currentAgent.color}/10 text-base-content border border-${currentAgent.color}/20`
              }`}>
                <div className="text-sm">{msg.text}</div>
                <div className="text-xs opacity-70 mt-1">
                  {msg.timestamp instanceof Date
                    ? msg.timestamp.toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' })
                    : new Date(msg.timestamp).toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' })
                  }
                </div>
              </div>
            </div>
          ))}

          {/* 思考中ステータス表示 */}
          {isThinking && (
            <div className="flex justify-start">
              <div className="max-w-[80%] p-3 rounded-2xl bg-base-300/50 text-base-content border border-base-content/10">
                <div className="flex items-center gap-2">
                  <div className="loading loading-dots loading-sm"></div>
                  <p className="text-sm text-base-content/70">{thinkingMessage}</p>
                </div>
              </div>
            </div>
          )}

          {/* 初期メッセージ表示 */}
          {messages.filter(msg => msg.agentId === currentAgent.id).length === 0 && !isThinking && (
            <div className="text-center text-base-content/50 py-8">
              <p className="text-sm">{currentAgent.persona.greeting}</p>
            </div>
          )}
        </div>
      </div>

      {/* 入力エリア（固定高さ） */}
      <div className="border-t border-base-content/10 bg-base-100 flex-shrink-0">
        <div className="p-4" style={{ minHeight: '80px' }}>
          <div className="flex gap-2 h-full">
            <div className="flex-1 flex flex-col">
              <textarea
                className="textarea textarea-bordered flex-1 resize-none text-sm"
                placeholder={`${currentAgent.name}に質問や指示を入力...`}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
              />
              {transcript && (
                <div className="text-xs text-base-content/50 mt-1">
                  音声認識: {transcript}
                </div>
              )}
            </div>
            
            <div className="flex flex-col gap-2">
              <button
                className={`btn btn-square ${isListening ? 'btn-error' : 'btn-outline'}`}
                onClick={() => {
                  if (isListening) {
                    stopListening();
                  } else {
                    startListening();
                  }
                }}
                disabled={!isSupported}
                title={isListening ? '音声入力停止' : '音声入力開始'}
              >
                {isListening ? <MicOff size={20} /> : <Mic size={20} />}
              </button>
              
              <button
                className="btn btn-primary btn-square"
                onClick={sendMessage}
                disabled={!message.trim()}
                title="メッセージ送信"
              >
                <Send size={20} />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}