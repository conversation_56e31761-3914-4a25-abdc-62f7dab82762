'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { Send, Mic, Setting<PERSON>, BarChart<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChevronLeft, ChevronRight, Brain, Heart, Eye, Cpu } from 'lucide-react';
import { chat<PERSON>ith<PERSON>other } from '@/lib/ai-agents';
import { useContinuousVoiceInput } from '@/hooks/useContinuousVoiceInput';
import { useCharacter } from '../contexts/CharacterContext';
import VRMViewer from './VRMViewer';
import PersonaSelector from './PersonaSelector';

interface AIAgent {
  id: string;
  name: string;
  role: string;
  title: string;
  emoji: string;
  description: string;
  llmModel: string;
  persona: string;
  vrmModel?: string;
  color: string;
  capabilities: string[];
}

function ISSystem() {
  const [message, setMessage] = useState('');
  const [currentTime, setCurrentTime] = useState('10:30');
  const [currentAgentIndex, setCurrentAgentIndex] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [activeSettingsTab, setActiveSettingsTab] = useState<'avatar' | 'soul' | 'capability' | null>(null);
  const [expandedAvatarSettings, setExpandedAvatarSettings] = useState(false);
  const [inputHeight, setInputHeight] = useState(64); // デフォルト64px
  const [isResizing, setIsResizing] = useState(false);
  const [sessionStorage, setSessionStorage] = useState<{[agentId: string]: any[]}>({});
  const [isClientMounted, setIsClientMounted] = useState(false);
  
  // 新しいCharacterContext使用
  const {
    characterState,
    setCurrentAgent,
    getCurrentAgentVRM,
    setAgentVRMModel,
    setAgentEmotion,
    updateAgentMessage,
    uploadVRMModelForAgent,
    deleteAgentVRMModel,
    playAgentAnimation,
    saveAgentSettings,
    loadAgentSettings,
    getAllAgentIds,
    getAgentDisplayName,
    setActive
  } = useCharacter();
  
  // AIエージェント定義
  const agents: AIAgent[] = [
    {
      id: 'mother-cto',
      name: '母',
      role: 'CTO',
      title: '技術・人材統括',
      emoji: '👩‍💼',
      description: '技術戦略立案と人材管理のエキスパート。要件定義からエージェント生成まで一手に担う。',
      llmModel: 'claude-3-haiku',
      persona: '優秀で思いやりのあるCTO。技術的判断力に長け、チームを適切に導く。母性と厳格さを併せ持つ。',
      color: 'secondary',
      capabilities: ['要件定義', 'エージェント生成', '技術戦略', '人材管理']
    },
    {
      id: 'general-lead',
      name: '将',
      role: '開発リーダー', 
      title: 'プロジェクト統括',
      emoji: '⚔️',
      description: '実装チームを率いる開発リーダー。アーキテクチャ設計と開発進行を管理。',
      llmModel: 'claude-3-sonnet',
      persona: '経験豊富な開発リーダー。冷静で判断力があり、チームの士気を高める力を持つ。',
      color: 'warning',
      capabilities: ['アーキテクチャ設計', 'チーム管理', '進捗管理', 'コードレビュー']
    },
    {
      id: 'soldier-dev',
      name: '兵',
      role: '開発エンジニア',
      title: '実装担当',
      emoji: '⚡',
      description: '具体的な実装を担当する開発エンジニア。高速で正確なコーディングが得意。',
      llmModel: 'gpt-3.5-turbo',
      persona: '勤勉で技術力の高いエンジニア。与えられたタスクを確実に完遂する。学習意欲が高い。',
      color: 'info',
      capabilities: ['コーディング', 'テスト実装', 'バグ修正', '機能実装']
    }
  ];

  const currentAgent = agents[currentAgentIndex];
  const currentAgentId = currentAgent.id;

  // エージェント切り替え時のVRM連動
  useEffect(() => {
    setCurrentAgent(currentAgentId);
    // 保存された設定を読み込み
    loadAgentSettings(currentAgentId);
    console.log(`🔄 エージェント切り替え: ${currentAgent.name} (${currentAgentId})`);
  }, [currentAgentIndex, currentAgentId, setCurrentAgent, loadAgentSettings, currentAgent.name]);
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: `こんにちは！私は${currentAgent.name}（${currentAgent.role}）です。${currentAgent.description}`,
      sender: 'agent',
      timestamp: new Date(Date.now() - 120000),
      agentId: currentAgent.id
    }
  ]);

  // 音声入力
  const {
    isListening,
    transcript,
    startListening,
    stopListening,
    isSupported
  } = useContinuousVoiceInput({
    onResult: (text) => {
      setMessage(text);
    }
  });

  // 時刻更新とクライアントマウント状態
  useEffect(() => {
    setIsClientMounted(true);
    const timer = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString('ja-JP', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }));
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // セッション保存・復元
  useEffect(() => {
    const savedSessions = localStorage.getItem('is-system-sessions');
    if (savedSessions) {
      try {
        const parsed = JSON.parse(savedSessions);
        setSessionStorage(parsed);
        
        // 現在のエージェントのメッセージを復元
        if (parsed[currentAgent.id]) {
          setMessages(parsed[currentAgent.id]);
        }
      } catch (error) {
        console.error('セッション復元エラー:', error);
      }
    }
    
    // VRM設定の復元は新しいCharacterContextで管理
  }, []);

  // メッセージ変更時の自動保存
  const saveToStorage = useCallback((agentId: string, newMessages: any[]) => {
    setSessionStorage(prev => {
      const updatedStorage = {
        ...prev,
        [agentId]: newMessages
      };
      localStorage.setItem('is-system-sessions', JSON.stringify(updatedStorage));
      return updatedStorage;
    });
  }, []);

  // エージェント切り替え時のメッセージ更新
  useEffect(() => {
    // 保存されたセッションがあるかチェック
    if (sessionStorage[currentAgent.id] && sessionStorage[currentAgent.id].length > 0) {
      setMessages(sessionStorage[currentAgent.id]);
    } else {
      // 新しいエージェントの場合、ウェルカムメッセージを追加
      const welcomeMessage = {
        id: `welcome-${currentAgent.id}-${Date.now()}`,
        text: `私は${currentAgent.name}（${currentAgent.role}）です。${currentAgent.description}`,
        sender: 'agent' as const,
        timestamp: new Date(),
        agentId: currentAgent.id
      };
      const newMessages = [welcomeMessage];
      setMessages(newMessages);
      saveToStorage(currentAgent.id, newMessages);
    }
  }, [currentAgentIndex, currentAgent.id, currentAgent.name, currentAgent.role, currentAgent.description, sessionStorage, saveToStorage]);

  const sendMessage = async () => {
    if (!message.trim()) return;

    const userMessage = {
      id: Date.now(),
      text: message,
      sender: 'user' as const,
      timestamp: new Date(),
      agentId: currentAgent.id
    };

    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    updateAgentMessage(currentAgentId, message);
    setActive(true);
    setAgentEmotion(currentAgentId, 'thinking');
    playAgentAnimation(currentAgentId, 'think');

    try {
      const response = await chatWithMother(message, updatedMessages.map(m => ({ 
        role: m.sender === 'user' ? 'user' : 'assistant', 
        content: m.text 
      })));
      
      setAgentEmotion(currentAgentId, 'speaking');
      playAgentAnimation(currentAgentId, 'speak');
      
      const agentResponse = {
        id: Date.now() + 1,
        text: response,
        sender: 'agent' as const,
        timestamp: new Date(),
        agentId: currentAgent.id
      };
      
      const finalMessages = [...updatedMessages, agentResponse];
      setMessages(finalMessages);
      saveToStorage(currentAgent.id, finalMessages);
      
      setAgentEmotion(currentAgentId, 'neutral');
      playAgentAnimation(currentAgentId, 'idle');
      setActive(false);
    } catch (error) {
      console.error('Chat error:', error);
      setAgentEmotion(currentAgentId, 'neutral');
    }

    setMessage('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const switchAgent = (direction: 'prev' | 'next') => {
    // 現在のエージェントのメッセージを保存
    saveToStorage(currentAgent.id, messages);
    
    if (direction === 'prev') {
      setCurrentAgentIndex(prev => prev > 0 ? prev - 1 : agents.length - 1);
    } else {
      setCurrentAgentIndex(prev => prev < agents.length - 1 ? prev + 1 : 0);
    }
  };

  // リサイズハンドラー
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
    
    const startY = e.clientY;
    const startHeight = inputHeight;
    
    const handleMouseMove = (e: MouseEvent) => {
      const deltaY = startY - e.clientY; // 上方向が正の値
      const newHeight = Math.max(40, Math.min(200, startHeight + deltaY));
      setInputHeight(newHeight);
    };
    
    const handleMouseUp = () => {
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [inputHeight]);

  return (
    <div className="h-full flex flex-col bg-base-100">
      {/* エージェント切り替えヘッダー */}
      <div className="p-4 border-b border-base-content/10 bg-gradient-to-r from-primary/20 to-primary/5">
        <div className="flex items-center justify-between">
          <button 
            className="btn btn-ghost btn-circle btn-sm"
            onClick={() => switchAgent('prev')}
          >
            <ChevronLeft size={16} />
          </button>
          
          <div className="text-center flex-1">
            <div className="flex items-center justify-center gap-3 mb-2">
              <div className={`text-3xl animate-pulse`}>
                {currentAgent.emoji}
              </div>
              <div>
                <h2 className="text-xl font-bold">{currentAgent.name}</h2>
                <p className="text-sm text-base-content/70">{currentAgent.title}</p>
              </div>
            </div>
            <div className="flex justify-center gap-1">
              {agents.map((_, index) => (
                <div 
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all ${
                    index === currentAgentIndex ? `bg-${currentAgent.color}` : 'bg-base-content/20'
                  }`}
                />
              ))}
            </div>
          </div>
          
          <button 
            className="btn btn-ghost btn-circle btn-sm"
            onClick={() => switchAgent('next')}
          >
            <ChevronRight size={16} />
          </button>
        </div>
        
        {/* エージェント情報 */}
        <div className="mt-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="text-xs text-base-content/60 bg-base-200 px-2 py-1 rounded-full">
              {currentAgent.llmModel}
            </div>
            <div className={`text-xs text-${currentAgent.color} bg-${currentAgent.color}/10 px-2 py-1 rounded-full`}>
              {currentAgent.role}
            </div>
          </div>
          <div className="flex items-center gap-2">
            {isClientMounted && getCurrentAgentVRM() && (
              <div className="bg-success/20 text-success text-xs px-2 py-1 rounded-full border border-success/30">
                ✓ {currentAgent.name}VRM
              </div>
            )}
            <button
              className="btn btn-ghost btn-circle btn-sm"
              onClick={() => setShowSettings(!showSettings)}
              title="エージェント設定"
            >
              <Settings size={16} />
            </button>
          </div>
        </div>
      </div>

      {/* 設定パネル */}
      {showSettings && (
        <div className="border-b border-base-content/10 bg-base-200/50">
          {/* 設定タブ */}
          <div className="flex border-b border-base-content/10">
            <button 
              className={`flex-1 p-3 text-sm font-medium transition-colors ${
                activeSettingsTab === 'avatar' ? 'bg-primary/20 text-primary' : 'hover:bg-base-200'
              }`}
              onClick={() => setActiveSettingsTab(activeSettingsTab === 'avatar' ? null : 'avatar')}
            >
              <Eye size={14} className="inline mr-2" />
              外見
            </button>
            <button 
              className={`flex-1 p-3 text-sm font-medium transition-colors ${
                activeSettingsTab === 'soul' ? 'bg-secondary/20 text-secondary' : 'hover:bg-base-200'
              }`}
              onClick={() => setActiveSettingsTab(activeSettingsTab === 'soul' ? null : 'soul')}
            >
              <Heart size={14} className="inline mr-2" />
              内面
            </button>
            <button 
              className={`flex-1 p-3 text-sm font-medium transition-colors ${
                activeSettingsTab === 'capability' ? 'bg-accent/20 text-accent' : 'hover:bg-base-200'
              }`}
              onClick={() => setActiveSettingsTab(activeSettingsTab === 'capability' ? null : 'capability')}
            >
              <Cpu size={14} className="inline mr-2" />
              機能
            </button>
          </div>

          {/* 設定コンテンツ */}
          {activeSettingsTab && (
            <div className="p-4 space-y-3">
              {activeSettingsTab === 'avatar' && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-semibold">外見設定</h3>
                  <button 
                    className="btn btn-xs btn-ghost"
                    onClick={() => setExpandedAvatarSettings(!expandedAvatarSettings)}
                    title="詳細設定を展開"
                  >
                    🔧 {expandedAvatarSettings ? '隠す' : '詳細'}
                  </button>
                </div>
                
                {/* 基本設定 */}
                <div className="space-y-2 text-sm">
                  <div>
                    <label className="label-text text-xs">VRMモデル</label>
                    <select 
                      className="select select-bordered select-xs w-full"
                      value={isClientMounted && getCurrentAgentVRM() ? 'uploaded' : (currentAgent.vrmModel || 'default')}
                      onChange={(e) => {
                        console.log('モデル変更:', e.target.value);
                        if (e.target.value !== 'uploaded') {
                          // アップロードされたVRMをクリア
                          deleteAgentVRMModel(currentAgentId);
                        }
                      }}
                    >
                      <option value="default">デフォルトモデル</option>
                      <option value="business-woman">Alicia (ビジネスウーマン)</option>
                      <option value="engineer-girl">Mai (エンジニア女子)</option>
                      <option value="manager-man">Takeshi (マネージャー)</option>
                      <option value="custom1">カスタム1 (.vrmファイル)</option>
                      <option value="custom2">カスタム2 (.vrmファイル)</option>
                      {isClientMounted && getCurrentAgentVRM() && (
                        <option value="uploaded">アップロード済み (.vrm)</option>
                      )}
                    </select>
                  </div>
                  <div>
                    <label className="label-text text-xs">リップシンク強度</label>
                    <input 
                      type="range" 
                      className="range range-xs" 
                      min="0" 
                      max="100" 
                      value={80}
                      onChange={(e) => {
                        const value = parseInt(e.target.value);
                        console.log('リップシンク:', value);
                      }}
                    />
                    <div className="text-xs text-base-content/50 mt-1">80%</div>
                  </div>
                  <div>
                    <label className="label-text text-xs">表情の豊かさ</label>
                    <input 
                      type="range" 
                      className="range range-xs" 
                      min="0" 
                      max="100" 
                      value={90}
                      onChange={(e) => {
                        const value = parseInt(e.target.value);
                        console.log('表情:', value);
                      }}
                    />
                    <div className="text-xs text-base-content/50 mt-1">90%</div>
                  </div>
                  <div className="border-t border-base-content/10 pt-3">
                    <label className="label-text text-xs font-semibold text-primary">🎨 {currentAgent.name}専用VRMファイル</label>
                    
                    {/* VRMアップロード状態で表示を分岐 */}
                    {!getCurrentAgentVRM() ? (
                      <div className="bg-primary/10 border border-primary/20 rounded-lg p-3 mt-2">
                        <div className="text-center mb-3">
                          <div className="text-2xl mb-2">{currentAgent.emoji}</div>
                          <p className="text-xs text-base-content/80">
                            {currentAgent.name}の3Dモデルを設定しましょう
                          </p>
                        </div>
                        <input 
                          type="file" 
                          accept=".vrm"
                          className="file-input file-input-bordered file-input-primary file-input-sm w-full"
                          onChange={async (e) => {
                            if (e.target.files?.[0]) {
                              const file = e.target.files[0];
                              console.log('🎨 VRMアップロード:', file.name, '→', currentAgent.name);
                              
                              try {
                                await uploadVRMModelForAgent(currentAgentId, file);
                                saveAgentSettings(currentAgentId);
                                
                                console.log(`✨ ${currentAgent.name}用VRMモデルアップロード完了: ${file.name}`);
                              } catch (error) {
                                console.error('VRMアップロードエラー:', error);
                              }
                            }
                          }}
                        />
                        <div className="text-xs text-base-content/60 mt-2 text-center">
                          .vrmファイルをドラッグ&ドロップまたはクリックして選択
                        </div>
                      </div>
                    ) : (
                      <div className="bg-success/10 border border-success/20 rounded-lg p-3 mt-2">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-xs font-medium text-success">✓ VRMモデル設定済み</div>
                            <div className="text-xs text-base-content/70">{getCurrentAgentVRM()?.name}</div>
                          </div>
                          <div className="flex gap-2">
                            <label className="btn btn-xs btn-primary">
                              🔄 変更
                              <input 
                                type="file" 
                                accept=".vrm"
                                className="hidden"
                                onChange={async (e) => {
                                  if (e.target.files?.[0]) {
                                    const file = e.target.files[0];
                                    console.log('🎨 VRM変更:', file.name, '→', currentAgent.name);
                                    
                                    try {
                                      await uploadVRMModelForAgent(currentAgentId, file);
                                      saveAgentSettings(currentAgentId);
                                      
                                      console.log(`✨ ${currentAgent.name}用VRMモデル変更完了: ${file.name}`);
                                    } catch (error) {
                                      console.error('VRM変更エラー:', error);
                                    }
                                  }
                                }}
                              />
                            </label>
                            <button 
                              className="btn btn-xs btn-error btn-outline"
                              onClick={() => {
                                deleteAgentVRMModel(currentAgentId);
                                console.log(`🗑️ ${currentAgent.name}のVRMモデルを削除`);
                              }}
                            >
                              🗑️
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* 詳細設定（展開時のみ表示） */}
                {expandedAvatarSettings && (
                  <div className="mt-4 p-3 bg-base-200/50 rounded-lg border border-base-content/10 space-y-3">
                    <h4 className="text-xs font-semibold text-base-content/80">詳細設定</h4>
                    
                    {/* モデル一覧 */}
                    <div>
                      <label className="label-text text-xs">プリセットモデル一覧</label>
                      <div className="grid grid-cols-2 gap-2 mt-1">
                        {[
                          { id: 'alicia', name: 'Alicia', desc: 'ビジネス系' },
                          { id: 'mai', name: 'Mai', desc: 'エンジニア系' },
                          { id: 'takeshi', name: 'Takeshi', desc: 'マネージャー系' },
                          { id: 'default', name: 'デフォルト', desc: '標準モデル' }
                        ].map(model => (
                          <button 
                            key={model.id}
                            className="btn btn-xs btn-outline text-left flex-col h-auto py-2"
                            onClick={() => {
                              console.log(`モデル切り替え: ${model.name}`);
                              // 実際の実装では、プリセットモデルのURLを設定
                            }}
                          >
                            <span className="font-medium">{model.name}</span>
                            <span className="text-xs opacity-70">{model.desc}</span>
                          </button>
                        ))}
                      </div>
                    </div>
                    
                    {/* ポーズ・アニメーション制御 */}
                    <div>
                      <label className="label-text text-xs">ポーズ・アニメーション</label>
                      <div className="flex gap-2 mt-1">
                        <button 
                          className="btn btn-xs btn-outline"
                          onClick={() => {
                            console.log('ポーズリセット実行');
                            // VRMモデルのポーズをリセット
                          }}
                        >
                          🔄 リセット
                        </button>
                        <button 
                          className="btn btn-xs btn-outline"
                          onClick={() => {
                            console.log('待機アニメーション開始');
                          }}
                        >
                          😐 待機
                        </button>
                        <button 
                          className="btn btn-xs btn-outline"
                          onClick={() => {
                            console.log('挨拶アニメーション実行');
                          }}
                        >
                          👋 挨拶
                        </button>
                      </div>
                    </div>
                    
                    {/* カメラ制御 */}
                    <div>
                      <label className="label-text text-xs">カメラアングル</label>
                      <div className="flex gap-2 mt-1">
                        <button className="btn btn-xs btn-outline">📷 正面</button>
                        <button className="btn btn-xs btn-outline">📷 斜め</button>
                        <button className="btn btn-xs btn-outline">📷 全身</button>
                      </div>
                    </div>
                    
                    {/* VRM情報表示 */}
                    {isClientMounted && getCurrentAgentVRM() && (
                      <div className="border-t border-base-content/10 pt-2">
                        <label className="label-text text-xs">{currentAgent.name}VRM情報</label>
                        <div className="text-xs text-base-content/60 space-y-1">
                          <div>✓ モデル読み込み済み</div>
                          <div>📁 ファイル名: {getCurrentAgentVRM()?.name}</div>
                          <div>🆔 モデルID: {getCurrentAgentVRM()?.id}</div>
                          <div>😊 エージェント: {currentAgent.name} ({currentAgent.role})</div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
                </div>
              )}

              {activeSettingsTab === 'soul' && (
                <div className="space-y-3">
                <h3 className="text-sm font-semibold">内面設定</h3>
                
                {/* ペルソナ選択システム */}
                <div>
                  <label className="label-text text-xs mb-2 block">エージェントペルソナ選択</label>
                  <div className="h-64 border border-base-content/20 rounded-lg overflow-hidden">
                    <PersonaSelector 
                      onPersonaSelect={(persona) => {
                        console.log('選択されたペルソナ:', persona);
                        // 実際の実装では、選択されたペルソナをエージェントに適用
                      }}
                      selectedPersona={null}
                    />
                  </div>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div>
                    <label className="label-text text-xs">カスタムペルソナ</label>
                    <textarea 
                      className="textarea textarea-bordered textarea-xs w-full h-16 text-xs" 
                      defaultValue={currentAgent.persona}
                      placeholder="カスタムペルソナの詳細設定..."
                    />
                  </div>
                  <div>
                    <label className="label-text text-xs">LLMモデル</label>
                    <select className="select select-bordered select-xs w-full" defaultValue={currentAgent.llmModel}>
                      <option value="claude-3-haiku">Claude 3 Haiku (高速)</option>
                      <option value="claude-3-sonnet">Claude 3 Sonnet (バランス)</option>
                      <option value="claude-3-opus">Claude 3 Opus (高品質)</option>
                      <option value="gpt-4">GPT-4</option>
                      <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    </select>
                  </div>
                </div>
                </div>
              )}

              {activeSettingsTab === 'capability' && (
                <div className="space-y-3">
                <h3 className="text-sm font-semibold">機能設定</h3>
                <div className="space-y-2">
                  <div className="text-xs font-medium">専門能力</div>
                  <div className="flex flex-wrap gap-1">
                    {currentAgent.capabilities.map((cap, index) => (
                      <span key={index} className={`badge badge-${currentAgent.color} badge-xs`}>
                        {cap}
                      </span>
                    ))}
                  </div>
                  <div className="grid grid-cols-2 gap-2 mt-3">
                    <button className="btn btn-xs btn-outline">
                      <BarChart3 size={10} />
                      学習統計
                    </button>
                    <button className="btn btn-xs btn-outline">
                      <Brain size={10} />
                      記憶管理
                    </button>
                  </div>
                  
                  <div className="border-t border-base-content/10 pt-3 mt-3">
                    <div className="text-xs font-medium mb-2">セッション管理</div>
                    <div className="grid grid-cols-1 gap-2">
                      <button 
                        className="btn btn-xs btn-warning"
                        onClick={() => {
                          const updatedMessages = [];
                          setMessages(updatedMessages);
                          saveToStorage(currentAgent.id, updatedMessages);
                        }}
                      >
                        現在のエージェントをリセット
                      </button>
                      <button 
                        className="btn btn-xs btn-error"
                        onClick={() => {
                          if (confirm('全てのエージェントの会話履歴を削除しますか？')) {
                            localStorage.removeItem('is-system-sessions');
                            setSessionStorage({});
                            setMessages([]);
                          }
                        }}
                      >
                        全セッションを削除
                      </button>
                      <div className="text-xs text-base-content/50 mt-1">
                        保存されたエージェント数: {Object.keys(sessionStorage).length}
                      </div>
                    </div>
                  </div>
                </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* メッセージエリア（最適化レイアウト） */}
      <div className="flex-1 flex flex-col min-h-0">
        
        {/* VRMビューワー（上部） */}
        <div className="h-80 p-3 bg-base-200/20 border-b border-base-content/10">
          <div className="h-full">
            <VRMViewer 
              vrmUrl={isClientMounted ? getCurrentAgentVRM()?.url : undefined}
              vrmArrayBuffer={isClientMounted ? getCurrentAgentVRM()?.arrayBuffer : undefined}
              agentId={currentAgent.id}
              lipSyncValue={characterState.agentVRMSettings[currentAgentId]?.customSettings?.lipSyncEnabled ? 80 : 0}
              expressionValue={90}
              isActive={characterState.globalSettings.isActive}
              className="h-full w-full"
              showControls={true}
              onVRMUpload={async (file) => {
                console.log('🎨 VRMViewerからのアップロード:', file.name, '→', currentAgent.name);
                console.log('📋 getCurrentAgentVRM():', getCurrentAgentVRM());
                console.log('📋 characterState:', characterState);
                
                try {
                  await uploadVRMModelForAgent(currentAgentId, file);
                  saveAgentSettings(currentAgentId);
                  
                  console.log(`✨ ${currentAgent.name}用VRMモデルアップロード完了: ${file.name}`);
                  
                  // アップロード成功の通知
                  const notification = document.createElement('div');
                  notification.className = 'fixed bottom-4 left-4 z-50 bg-success text-success-content px-4 py-2 rounded-lg shadow-lg flex items-center gap-2';
                  notification.innerHTML = `
                    <span>✅</span>
                    VRMファイル「${file.name}」を${currentAgent.name}にアップロードしました
                  `;
                  document.body.appendChild(notification);
                  setTimeout(() => notification.remove(), 3000);
                } catch (error) {
                  console.error('VRMアップロードエラー:', error);
                  
                  // エラー通知
                  const notification = document.createElement('div');
                  notification.className = 'fixed bottom-4 left-4 z-50 bg-error text-error-content px-4 py-2 rounded-lg shadow-lg flex items-center gap-2';
                  notification.innerHTML = `
                    <span>❌</span>
                    VRMファイル「${file.name}」のアップロードに失敗しました
                  `;
                  document.body.appendChild(notification);
                  setTimeout(() => notification.remove(), 3000);
                }
              }}
            />
          </div>
        </div>
        
        {/* チャットメッセージエリア（下部） */}
        <div className="flex-1 overflow-y-auto p-4 space-y-3 min-h-0">
          {messages.filter(msg => msg.agentId === currentAgent.id).map((msg) => (
            <div key={msg.id} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-[80%] p-3 rounded-2xl ${
                msg.sender === 'user' 
                  ? 'bg-primary text-primary-content' 
                  : 'bg-base-200 text-base-content border border-base-content/20'
              }`}>
                <div className="text-sm">{msg.text}</div>
                {isClientMounted && (
                  <div className="text-xs opacity-70 mt-1">
                    {msg.timestamp instanceof Date 
                      ? msg.timestamp.toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' })
                      : new Date(msg.timestamp).toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' })
                    }
                  </div>
                )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* リサイズハンドル */}
      <div 
        className={`h-1 bg-base-content/10 cursor-ns-resize hover:bg-primary/50 transition-colors ${
          isResizing ? 'bg-primary' : ''
        }`}
        onMouseDown={handleMouseDown}
      />
      
      {/* 入力エリア（シンプル版） */}
      <div className="p-4 border-t border-base-content/10 bg-base-200/30" style={{ minHeight: inputHeight + 32 }}>
        <div className="flex gap-2">
          <textarea
            className="textarea textarea-bordered flex-1 resize-none"
            style={{ height: inputHeight }}
            placeholder={`${currentAgent.name}に話しかけてみてください...`}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          <div className="flex flex-col gap-1">
            <button
              className="btn btn-primary btn-sm"
              onClick={sendMessage}
              disabled={!message.trim()}
            >
              <Send size={14} />
            </button>
            <button
              className={`btn btn-sm ${isListening ? 'btn-error' : 'btn-accent'}`}
              onClick={isListening ? stopListening : startListening}
              disabled={!isSupported}
            >
              {isListening ? <MicOff size={14} /> : <Mic size={14} />}
            </button>
          </div>
        </div>
        
        {transcript && (
          <div className="text-xs text-base-content/60 bg-base-200 p-2 rounded mt-2">
            音声認識: {transcript}
          </div>
        )}
      </div>
    </div>
  );
}

export default ISSystem;

