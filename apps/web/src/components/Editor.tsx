'use client';

import { useState, useRef, useEffect } from 'react';
import { Bold, Italic, Underline, List, Quote, Code, Image, Link, Save, FileText, GripVertical, Edit3 } from 'lucide-react';
import YamlVisualizationView from './YamlVisualizationView';

interface EditorProps {
  filePath?: string;
  initialContent?: string;
  projectName?: string;
  onProjectNameChange?: (newName: string) => void;
}

export default function Editor({ filePath, initialContent, projectName, onProjectNameChange }: EditorProps) {
  const [content, setContent] = useState(initialContent || '');
  const [isEditing, setIsEditing] = useState(true); // デフォルトでリアルタイム編集モードを有効
  const [showYamlView, setShowYamlView] = useState(false);
  const [isEditingProjectName, setIsEditingProjectName] = useState(false);
  const [projectNameValue, setProjectNameValue] = useState(projectName || '');
  const [draggedLine, setDraggedLine] = useState<number | null>(null);
  const editorRef = useRef<HTMLDivElement>(null);
  const projectNameRef = useRef<HTMLInputElement>(null);

  // ファイルパスからYAMLかどうかを判定
  const isYamlFile = filePath && (filePath.endsWith('.yaml') || filePath.endsWith('.yml'));

  useEffect(() => {
    if (isYamlFile && content) {
      setShowYamlView(true);
    }
  }, [isYamlFile, content]);

  useEffect(() => {
    setProjectNameValue(projectName || '');
  }, [projectName]);

  // プロジェクト名編集のハンドラー
  const handleProjectNameDoubleClick = () => {
    setIsEditingProjectName(true);
    setTimeout(() => {
      projectNameRef.current?.focus();
      projectNameRef.current?.select();
    }, 0);
  };

  const handleProjectNameSave = () => {
    setIsEditingProjectName(false);
    if (onProjectNameChange && projectNameValue !== projectName) {
      onProjectNameChange(projectNameValue);
    }
  };

  const handleProjectNameKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleProjectNameSave();
    } else if (e.key === 'Escape') {
      setProjectNameValue(projectName || '');
      setIsEditingProjectName(false);
    }
  };

  const handleCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      setContent(editorRef.current.innerHTML);
    }
  };

  const handleContentChange = () => {
    if (editorRef.current) {
      setContent(editorRef.current.innerHTML);
    }
  };

  // Craft風の行ドラッグ&ドロップ機能（改善版）
  const [isDragging, setIsDragging] = useState(false);
  const [dragOverLine, setDragOverLine] = useState<number | null>(null);
  
  const getTextLines = () => {
    if (!editorRef.current) {
      // 初期コンテンツがない場合のデフォルト行
      if (!content) {
        return [
          "💡 アイデアをここに書き始めてください",
          "要件定義やプロジェクト計画を自由に記述できます",
          "行をドラッグして並び替えることができます"
        ];
      }
      return [];
    }
    const text = editorRef.current.innerText || '';
    const lines = text.split('\n').filter(line => line.trim().length > 0);
    
    // 空の場合はデフォルトコンテンツを表示
    if (lines.length === 0) {
      return [
        "💡 アイデアをここに書き始めてください",
        "要件定義やプロジェクト計画を自由に記述できます",
        "行をドラッグして並び替えることができます"
      ];
    }
    
    return lines;
  };

  const setTextLines = (lines: string[]) => {
    if (!editorRef.current) return;
    const htmlContent = lines.map(line => `<p>${line}</p>`).join('');
    editorRef.current.innerHTML = htmlContent;
    setContent(htmlContent);
  };

  const handleLineMouseDown = (e: React.MouseEvent, lineIndex: number) => {
    if (e.button === 0) { // 左クリックのみ
      setDraggedLine(lineIndex);
      setIsDragging(true);
      e.preventDefault();
    }
  };

  const handleLineDragOver = (e: React.DragEvent, lineIndex: number) => {
    e.preventDefault();
    setDragOverLine(lineIndex);
  };

  const handleLineDrop = (targetLineIndex: number) => {
    if (draggedLine !== null && draggedLine !== targetLineIndex) {
      const lines = getTextLines();
      if (lines.length > draggedLine && lines.length > targetLineIndex) {
        const draggedContent = lines[draggedLine];
        
        // 配列から要素を移動
        lines.splice(draggedLine, 1);
        const insertIndex = targetLineIndex > draggedLine ? targetLineIndex - 1 : targetLineIndex;
        lines.splice(insertIndex, 0, draggedContent);
        
        setTextLines(lines);
      }
    }
    setDraggedLine(null);
    setIsDragging(false);
    setDragOverLine(null);
  };

  const toolbarButtons = [
    { command: 'bold', icon: Bold, label: '太字' },
    { command: 'italic', icon: Italic, label: '斜体' },
    { command: 'underline', icon: Underline, label: '下線' },
    { command: 'insertUnorderedList', icon: List, label: 'リスト' },
    { command: 'formatBlock-quote', icon: Quote, label: '引用', value: 'blockquote' },
    { command: 'formatBlock-code', icon: Code, label: 'コード', value: 'pre' },
  ];

  // YAMLファイルの場合は視覚化ビューを表示
  if (isYamlFile && showYamlView) {
    return (
      <div className="h-full flex flex-col">
        <div className="p-3 border-b border-base-content/10 bg-base-300/80">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="text-lg font-semibold">📊 YAML構造ビュー</div>
              <div className="text-xs text-base-content/70">{filePath}</div>
              {projectName && (
                <div className="ml-4 flex items-center gap-2">
                  <span className="text-sm text-base-content/50">プロジェクト:</span>
                  {isEditingProjectName ? (
                    <input
                      ref={projectNameRef}
                      type="text"
                      value={projectNameValue}
                      onChange={(e) => setProjectNameValue(e.target.value)}
                      onBlur={handleProjectNameSave}
                      onKeyDown={handleProjectNameKeyPress}
                      className="input input-xs input-bordered"
                    />
                  ) : (
                    <span
                      className="text-sm font-semibold cursor-pointer hover:text-primary"
                      onDoubleClick={handleProjectNameDoubleClick}
                      title="ダブルクリックで編集"
                    >
                      {projectNameValue}
                    </span>
                  )}
                </div>
              )}
            </div>
            <button
              onClick={() => setShowYamlView(false)}
              className="btn btn-sm btn-outline"
            >
              <Code size={16} />
              エディターに戻る
            </button>
          </div>
        </div>
        <YamlVisualizationView 
          yamlContent={content} 
          filePath={filePath}
        />
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col neo-glass">
      {/* ツールバー */}
      <div className="p-3 border-b border-base-content/10 bg-base-300/80">
        <div className="flex items-center gap-2 mb-2">
          <div className="text-lg font-semibold">📝 Editor</div>
          <div className="text-xs text-base-content/70">
            {filePath ? `編集中: ${filePath}` : 'リアルタイム編集'}
          </div>
          {projectName && (
            <div className="ml-4 flex items-center gap-2">
              <span className="text-sm text-base-content/50">プロジェクト:</span>
              {isEditingProjectName ? (
                <input
                  ref={projectNameRef}
                  type="text"
                  value={projectNameValue}
                  onChange={(e) => setProjectNameValue(e.target.value)}
                  onBlur={handleProjectNameSave}
                  onKeyDown={handleProjectNameKeyPress}
                  className="input input-xs input-bordered"
                />
              ) : (
                <span
                  className="text-sm font-semibold cursor-pointer hover:text-primary flex items-center gap-1"
                  onDoubleClick={handleProjectNameDoubleClick}
                  title="ダブルクリックで編集"
                >
                  {projectNameValue}
                  <Edit3 size={10} className="opacity-50" />
                </span>
              )}
            </div>
          )}
          {isYamlFile && (
            <button
              onClick={() => setShowYamlView(true)}
              className="btn btn-xs btn-primary ml-auto"
            >
              <FileText size={12} />
              YAML構造ビュー
            </button>
          )}
        </div>
        
        <div className="flex items-center gap-1 flex-wrap">
          {toolbarButtons.map((btn) => (
            <button
              key={btn.label}
              onClick={() => handleCommand(btn.command.startsWith('formatBlock') ? 'formatBlock' : btn.command, btn.value)}
              className="btn btn-xs btn-ghost neo-hover"
              title={btn.label}
            >
              <btn.icon size={14} />
            </button>
          ))}
          
          <div className="divider divider-horizontal"></div>
          
          <button
            onClick={() => handleCommand('createLink', prompt('リンクURL:') || '')}
            className="btn btn-xs btn-ghost neo-hover"
            title="リンク"
          >
            <Link size={14} />
          </button>
          
          <button
            onClick={() => handleCommand('insertImage', prompt('画像URL:') || '')}
            className="btn btn-xs btn-ghost neo-hover"
            title="画像"
          >
            <Image size={14} />
          </button>
          
          <div className="ml-auto">
            <button className="btn btn-xs btn-primary neo-hover">
              <Save size={14} />
              保存
            </button>
          </div>
        </div>
      </div>

      {/* エディターエリア */}
      <div className="flex-1 p-4 overflow-y-auto">
        <div
          ref={editorRef}
          contentEditable
          suppressContentEditableWarning
          onInput={handleContentChange}
          onFocus={() => setIsEditing(true)}
          onBlur={() => setIsEditing(false)}
          className={`min-h-full w-full p-4 rounded-lg border-2 neo-smooth focus:outline-none ${
            isEditing 
              ? 'border-primary/50 bg-base-100/50' 
              : 'border-transparent bg-base-100/20'
          }`}
          style={{
            fontFamily: 'var(--font-geist-sans)',
            fontSize: '16px',
            lineHeight: '1.6',
            minHeight: '400px',
            whiteSpace: 'pre-wrap',
            wordWrap: 'break-word',
          }}
          placeholder="ここに書き始めてください..."
          dangerouslySetInnerHTML={{ __html: content || `
            <h1>メタスタジオ プロジェクト計画</h1>
            <p>このエディターで要件定義やアイデアを自由に記述できます。</p>
            <ul>
              <li>リアルタイム編集</li>
              <li>WYSIWYG操作</li>
              <li>MarkdownとHTMLの相互変換</li>
            </ul>
            <blockquote>
              「脳内現実化ツール」として、思考を即座に形にする
            </blockquote>
          ` }}
        />
      </div>

      {/* ステータスバー */}
      <div className="p-2 border-t border-base-content/10 bg-base-200/50">
        <div className="flex items-center justify-between text-xs text-base-content/70">
          <div className="flex items-center gap-4">
            <span>文字数: {content.replace(/<[^>]*>/g, '').length}</span>
            <span>単語数: {content.replace(/<[^>]*>/g, '').split(/\s+/).filter(w => w).length}</span>
          </div>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${isEditing ? 'bg-success animate-pulse' : 'bg-base-content/30'}`}></div>
            <span>{isEditing ? '編集中' : '待機中'}</span>
          </div>
        </div>
      </div>
    </div>
  );
}