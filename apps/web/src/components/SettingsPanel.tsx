'use client';

import { useState, useEffect } from 'react';
import { Moon, Sun, Bell, Shield, Zap, Database, Download, Upload, Trash2, Save, Clock, GitBranch, Volume2, VolumeX, Play } from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';

export default function SettingsPanel() {
  const { 
    permission, 
    requestPermission, 
    sendTaskCompleteNotification,
    sendClaudeCodeCompleteNotification,
    settings: notificationSettings, 
    updateSettings: updateNotificationSettings,
    playNotificationSound: playSound
  } = useNotifications();
  const [settings, setSettings] = useState({
    theme: 'light',
    notifications: true,
    autoSave: true,
    aiAssistance: true,
    dataCollection: false,
    language: 'ja',
    agentLimit: 50,
    projectBackup: true,
    voiceRecording: true,
    performanceMode: false
  });
  const [activeTab, setActiveTab] = useState<'appearance' | 'notifications' | 'ai' | 'privacy' | 'history' | 'system'>('appearance');

  // ローカルストレージから基本設定を読み込み
  useEffect(() => {
    const savedSettings = localStorage.getItem('metastudio-settings');
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('設定の読み込みエラー:', error);
      }
    }
  }, []);

  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  // 通知設定の更新ヘルパー
  const updateNotificationSetting = (key: string, value: any) => {
    updateNotificationSettings({ [key]: value });
  };

  const saveSettings = () => {
    // 実際の実装では、ここでAPIにPOST
    console.log('設定を保存:', settings);
    console.log('通知設定を保存:', notificationSettings);
    
    // ローカルストレージに基本設定を保存
    localStorage.setItem('metastudio-settings', JSON.stringify(settings));
    
    // 完了通知を送信（通知音込み）
    sendTaskCompleteNotification('設定の保存');
    
    // フォールバックアラート（通知が無効な場合）
    if (permission !== 'granted') {
      alert('設定が保存されました！');
    }
  };

  const handleNotificationToggle = async (checked: boolean) => {
    if (checked && permission === 'default') {
      const granted = await requestPermission();
      if (!granted) {
        return; // 許可されなかった場合は設定を変更しない
      }
    }
    updateSetting('notifications', checked);
  };

  const exportData = () => {
    // データエクスポート機能
    const data = {
      settings,
      exportDate: new Date().toISOString(),
      version: '1.0.0'
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'meta-studio-settings.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  const resetSettings = () => {
    if (confirm('すべての設定をリセットしますか？この操作は元に戻せません。')) {
      setSettings({
        theme: 'light',
        notifications: true,
        autoSave: true,
        aiAssistance: true,
        dataCollection: false,
        language: 'ja',
        agentLimit: 50,
        projectBackup: true,
        voiceRecording: true,
        performanceMode: false
      });
      
      // 通知設定もリセット
      updateNotificationSettings({
        notificationSound: true,
        soundType: 'chime',
        soundVolume: 0.7,
        taskCompleteSound: true,
        projectUpdateSound: true,
        agentReportSound: true,
        systemAlertSound: true
      });
    }
  };

  return (
    <div className="h-full flex flex-col neo-glass">
      {/* ヘッダー */}
      <div className="p-4 border-b border-base-content/10 bg-base-300/80 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">設定</h1>
            <p className="text-sm text-base-content/70">メタスタジオの動作設定</p>
          </div>
          <div className="flex gap-2">
            <button 
              className="btn btn-outline btn-sm neo-hover"
              onClick={exportData}
            >
              <Download size={16} />
              エクスポート
            </button>
            <button 
              className="btn btn-primary btn-sm neo-hover"
              onClick={saveSettings}
            >
              <Save size={16} />
              保存
            </button>
          </div>
        </div>
      </div>

      {/* タブナビゲーション */}
      <div className="flex overflow-x-auto border-b border-base-content/10 bg-base-200/50 flex-shrink-0">
        {[
          { id: 'appearance', label: '外観', icon: Sun },
          { id: 'notifications', label: '通知', icon: Bell },
          { id: 'ai', label: 'AI・エージェント', icon: Zap },
          { id: 'privacy', label: 'データ・プライバシー', icon: Shield },
          { id: 'history', label: '変更履歴', icon: Clock },
          { id: 'system', label: 'システム', icon: Database }
        ].map((tab) => {
          const IconComponent = tab.icon;
          return (
            <button
              key={tab.id}
              className={`flex items-center gap-2 px-4 py-3 border-b-2 transition-all flex-shrink-0 ${
                activeTab === tab.id
                  ? 'border-primary bg-primary/10 text-primary'
                  : 'border-transparent hover:bg-base-100/50 text-base-content/70'
              }`}
              onClick={() => setActiveTab(tab.id as any)}
            >
              <IconComponent size={16} />
              <span className="text-sm font-medium">{tab.label}</span>
            </button>
          );
        })}
      </div>

      {/* タブコンテンツ */}
      <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-primary/40 scrollbar-track-base-300/20 hover:scrollbar-thumb-primary/60 p-4 min-h-0">
        <div className="max-w-4xl mx-auto">
          
          {/* 外観設定 */}
          {activeTab === 'appearance' && (
            <div className="card bg-base-200 shadow-xl neo-depth">
            <div className="card-body">
              <h2 className="card-title flex items-center gap-2">
                <Sun size={20} />
                外観
              </h2>
              
              <div className="space-y-4">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium">テーマ</span>
                  </label>
                  <div className="flex gap-2">
                    {['light', 'dark', 'metastudio'].map((theme) => (
                      <button
                        key={theme}
                        className={`btn btn-sm neo-hover ${
                          settings.theme === theme ? 'btn-primary' : 'btn-outline'
                        }`}
                        onClick={() => updateSetting('theme', theme)}
                      >
                        {theme === 'light' ? '🌞 ライト' : 
                         theme === 'dark' ? '🌙 ダーク' : 
                         '🤖 メタスタジオ'}
                      </button>
                    ))}
                  </div>
                </div>

                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium">言語</span>
                  </label>
                  <select 
                    className="select select-bordered max-w-xs"
                    value={settings.language}
                    onChange={(e) => updateSetting('language', e.target.value)}
                  >
                    <option value="ja">日本語</option>
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                  </select>
                </div>
              </div>
            </div>
            </div>
          )}

          {/* 通知設定 */}
          {activeTab === 'notifications' && (
            <div className="card bg-base-200 shadow-xl neo-depth">
            <div className="card-body">
              <h2 className="card-title flex items-center gap-2">
                <Bell size={20} />
                通知
              </h2>
              
              <div className="space-y-4">
                <div className="form-control">
                  <label className="label cursor-pointer">
                    <span className="label-text">デスクトップ通知を有効にする</span>
                    <input 
                      type="checkbox" 
                      className="toggle toggle-primary"
                      checked={settings.notifications}
                      onChange={(e) => handleNotificationToggle(e.target.checked)}
                    />
                  </label>
                </div>

                {/* 通知音設定 */}
                <div className="form-control">
                  <label className="label cursor-pointer">
                    <span className="label-text flex items-center gap-2">
                      {notificationSettings.notificationSound ? <Volume2 size={16} /> : <VolumeX size={16} />}
                      通知音を有効にする
                    </span>
                    <input 
                      type="checkbox" 
                      className="toggle toggle-primary"
                      checked={notificationSettings.notificationSound}
                      onChange={(e) => updateNotificationSetting('notificationSound', e.target.checked)}
                    />
                  </label>
                </div>

                {notificationSettings.notificationSound && (
                  <div className="ml-4 space-y-3 p-3 bg-base-100 rounded-lg border border-base-content/10">
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text font-medium">通知音の種類</span>
                      </label>
                      <div className="flex flex-wrap gap-2">
                        {[
                          { value: 'chime', label: '🔔 チャイム', desc: '優しい音色' },
                          { value: 'bell', label: '🔔 ベル', desc: '明るい音色' },
                          { value: 'success', label: '✅ 成功音', desc: '達成感のある音' },
                          { value: 'alert', label: '⚠️ アラート', desc: '注意を引く音' },
                          { value: 'subtle', label: '🔇 控えめ', desc: '邪魔にならない音' }
                        ].map((sound) => (
                          <div key={sound.value} className="flex flex-col">
                            <button
                              className={`btn btn-sm neo-hover ${
                                notificationSettings.soundType === sound.value ? 'btn-primary' : 'btn-outline'
                              }`}
                              onClick={() => updateNotificationSetting('soundType', sound.value)}
                            >
                              {sound.label}
                            </button>
                            <span className="text-xs text-base-content/60 mt-1 text-center">
                              {sound.desc}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="form-control">
                      <label className="label">
                        <span className="label-text font-medium">音量</span>
                        <button 
                          className="btn btn-xs btn-outline neo-hover"
                          onClick={() => playSound()}
                        >
                          <Play size={12} />
                          テスト再生
                        </button>
                      </label>
                      <input 
                        type="range" 
                        min="0" 
                        max="1" 
                        step="0.1"
                        value={notificationSettings.soundVolume}
                        onChange={(e) => updateNotificationSetting('soundVolume', parseFloat(e.target.value))}
                        className="range range-primary range-sm" 
                      />
                      <div className="flex justify-between text-xs px-2 mt-1">
                        <span>無音</span>
                        <span>音量: {Math.round(notificationSettings.soundVolume * 100)}%</span>
                        <span>最大</span>
                      </div>
                    </div>

                    <div className="divider text-xs">通知タイプ別設定</div>

                    <div className="space-y-2">
                      <div className="form-control">
                        <label className="label cursor-pointer">
                          <span className="label-text">✅ タスク完了通知音</span>
                          <input 
                            type="checkbox" 
                            className="toggle toggle-success toggle-sm"
                            checked={notificationSettings.taskCompleteSound}
                            onChange={(e) => updateNotificationSetting('taskCompleteSound', e.target.checked)}
                          />
                        </label>
                      </div>

                      <div className="form-control">
                        <label className="label cursor-pointer">
                          <span className="label-text">📁 プロジェクト更新通知音</span>
                          <input 
                            type="checkbox" 
                            className="toggle toggle-info toggle-sm"
                            checked={notificationSettings.projectUpdateSound}
                            onChange={(e) => updateNotificationSetting('projectUpdateSound', e.target.checked)}
                          />
                        </label>
                      </div>

                      <div className="form-control">
                        <label className="label cursor-pointer">
                          <span className="label-text">🤖 エージェントレポート通知音</span>
                          <input 
                            type="checkbox" 
                            className="toggle toggle-secondary toggle-sm"
                            checked={notificationSettings.agentReportSound}
                            onChange={(e) => updateNotificationSetting('agentReportSound', e.target.checked)}
                          />
                        </label>
                      </div>

                      {/* Claude Code 専用通知設定 */}
                      <div className="bg-primary/5 p-3 rounded-lg border border-primary/20">
                        <div className="flex items-center justify-between mb-2">
                          <span className="label-text font-medium text-primary">🔧 Claude Code 完了通知</span>
                          <button
                            className="btn btn-xs btn-primary"
                            onClick={() => sendClaudeCodeCompleteNotification('テスト通知')}
                            title="Claude Code 完了通知をテスト"
                          >
                            <Play size={12} />
                            テスト
                          </button>
                        </div>
                        <p className="text-xs text-base-content/60 mb-2">
                          Claude Code がタスクを完了した時の通知音設定
                        </p>
                        <div className="form-control">
                          <label className="label cursor-pointer">
                            <span className="label-text">Claude Code 完了通知音</span>
                            <input 
                              type="checkbox" 
                              className="toggle toggle-primary toggle-sm"
                              checked={notificationSettings.taskCompleteSound}
                              onChange={(e) => updateNotificationSetting('taskCompleteSound', e.target.checked)}
                            />
                          </label>
                        </div>
                      </div>

                      <div className="form-control">
                        <label className="label cursor-pointer">
                          <span className="label-text">⚠️ システムアラート通知音</span>
                          <input 
                            type="checkbox" 
                            className="toggle toggle-warning toggle-sm"
                            checked={notificationSettings.systemAlertSound}
                            onChange={(e) => updateNotificationSetting('systemAlertSound', e.target.checked)}
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                )}

                <div className="form-control">
                  <label className="label cursor-pointer">
                    <span className="label-text">プロジェクト完了時に通知</span>
                    <input type="checkbox" className="toggle toggle-primary" defaultChecked />
                  </label>
                </div>

                <div className="form-control">
                  <label className="label cursor-pointer">
                    <span className="label-text">エージェントからのレポート通知</span>
                    <input type="checkbox" className="toggle toggle-primary" defaultChecked />
                  </label>
                </div>
              </div>
            </div>
            </div>
          )}

          {/* AI・エージェント設定 */}
          {activeTab === 'ai' && (
            <div className="card bg-base-200 shadow-xl neo-depth">
            <div className="card-body">
              <h2 className="card-title flex items-center gap-2">
                <Zap size={20} />
                AI・エージェント
              </h2>
              
              <div className="space-y-4">
                <div className="form-control">
                  <label className="label cursor-pointer">
                    <span className="label-text">AI支援機能を有効にする</span>
                    <input 
                      type="checkbox" 
                      className="toggle toggle-primary"
                      checked={settings.aiAssistance}
                      onChange={(e) => updateSetting('aiAssistance', e.target.checked)}
                    />
                  </label>
                </div>

                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium">最大エージェント数</span>
                  </label>
                  <input 
                    type="range" 
                    min="10" 
                    max="100" 
                    value={settings.agentLimit}
                    onChange={(e) => updateSetting('agentLimit', parseInt(e.target.value))}
                    className="range range-primary" 
                  />
                  <div className="flex justify-between text-xs px-2">
                    <span>10</span>
                    <span>現在: {settings.agentLimit}</span>
                    <span>100</span>
                  </div>
                </div>

                <div className="form-control">
                  <label className="label cursor-pointer">
                    <span className="label-text">パフォーマンスモード（軽量化）</span>
                    <input 
                      type="checkbox" 
                      className="toggle toggle-warning"
                      checked={settings.performanceMode}
                      onChange={(e) => updateSetting('performanceMode', e.target.checked)}
                    />
                  </label>
                </div>
              </div>
            </div>
            </div>
          )}

          {/* データ・プライバシー設定 */}
          {activeTab === 'privacy' && (
            <div className="card bg-base-200 shadow-xl neo-depth">
            <div className="card-body">
              <h2 className="card-title flex items-center gap-2">
                <Shield size={20} />
                データ・プライバシー
              </h2>
              
              <div className="space-y-4">
                <div className="form-control">
                  <label className="label cursor-pointer">
                    <span className="label-text">自動保存を有効にする</span>
                    <input 
                      type="checkbox" 
                      className="toggle toggle-primary"
                      checked={settings.autoSave}
                      onChange={(e) => updateSetting('autoSave', e.target.checked)}
                    />
                  </label>
                </div>

                <div className="form-control">
                  <label className="label cursor-pointer">
                    <span className="label-text">プロジェクトの自動バックアップ</span>
                    <input 
                      type="checkbox" 
                      className="toggle toggle-primary"
                      checked={settings.projectBackup}
                      onChange={(e) => updateSetting('projectBackup', e.target.checked)}
                    />
                  </label>
                </div>

                <div className="form-control">
                  <label className="label cursor-pointer">
                    <span className="label-text">音声録音データの保存</span>
                    <input 
                      type="checkbox" 
                      className="toggle toggle-primary"
                      checked={settings.voiceRecording}
                      onChange={(e) => updateSetting('voiceRecording', e.target.checked)}
                    />
                  </label>
                </div>

                <div className="form-control">
                  <label className="label cursor-pointer">
                    <span className="label-text">使用統計の収集（匿名）</span>
                    <input 
                      type="checkbox" 
                      className="toggle toggle-error"
                      checked={settings.dataCollection}
                      onChange={(e) => updateSetting('dataCollection', e.target.checked)}
                    />
                  </label>
                </div>
              </div>
            </div>
            </div>
          )}

          {/* メタスタジオ変更履歴 */}
          {activeTab === 'history' && (
            <div className="card bg-base-200 shadow-xl neo-depth">
            <div className="card-body">
              <h2 className="card-title flex items-center gap-2">
                <Clock size={20} />
                メタスタジオ変更履歴
              </h2>
              
              <div className="space-y-3 max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-primary/20 scrollbar-track-transparent">
                {[
                  {
                    version: 'v0.0.7',
                    date: '2025-06-15',
                    time: '14:30',
                    changes: [
                      '設定画面に変更履歴機能を追加',
                      'プロジェクトリストの機能拡張（重要度、進捗、ソート）',
                      'ダッシュボードドラッグ制限の改善'
                    ]
                  },
                  {
                    version: 'v0.0.6',
                    date: '2025-06-15',
                    time: '13:45',
                    changes: [
                      '統計・プラグイン・エージェント管理の実装済みコンポーネント統合',
                      'サイドバーのプロジェクト表示強化'
                    ]
                  },
                  {
                    version: 'v0.0.5',
                    date: '2025-06-15',
                    time: '12:20',
                    changes: [
                      'DashboardGridのウィジェット機能強化',
                      'TimeBlockウィジェットの編集機能追加',
                      'HealthWidgetのデータ編集機能実装'
                    ]
                  },
                  {
                    version: 'v0.0.4',
                    date: '2025-06-14',
                    time: '16:15',
                    changes: [
                      'レスポンシブ対応の改善',
                      'モバイル用ボトムナビゲーション追加'
                    ]
                  },
                  {
                    version: 'v0.0.3',
                    date: '2025-06-14',
                    time: '11:30',
                    changes: [
                      'MetaStudioLayoutの基本構造完成',
                      'TabManager機能の実装',
                      'ResizablePanel導入'
                    ]
                  },
                  {
                    version: 'v0.0.2',
                    date: '2025-06-13',
                    time: '15:45',
                    changes: [
                      'Sidebarコンポーネント実装',
                      'iSシステムの基本機能追加',
                      'Launcher機能（Cmd+K）実装'
                    ]
                  },
                  {
                    version: 'v0.0.1',
                    date: '2025-06-13',
                    time: '09:00',
                    changes: [
                      'プロジェクト初期設定',
                      'Next.js 15.3.3 + DaisyUI v5 beta導入',
                      '基本レイアウト構造の作成'
                    ]
                  }
                ].map((release) => (
                  <div key={release.version} className="border border-base-content/10 rounded-lg p-3 bg-base-100/50">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <GitBranch size={14} className="text-primary" />
                        <span className="font-semibold text-primary">{release.version}</span>
                      </div>
                      <div className="text-xs text-base-content/60">
                        {release.date} {release.time}
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      {release.changes.map((change, index) => (
                        <div key={index} className="text-sm text-base-content/80 pl-4 relative">
                          <span className="absolute left-0 top-1.5 w-1 h-1 bg-base-content/40 rounded-full"></span>
                          {change}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="divider text-sm">システム情報</div>
              
              <div className="grid grid-cols-2 gap-4 text-xs">
                <div>
                  <div className="text-base-content/60">現在のバージョン</div>
                  <div className="font-semibold text-primary">v0.0.7</div>
                </div>
                <div>
                  <div className="text-base-content/60">最終更新</div>
                  <div className="font-semibold">2025-06-15 14:30</div>
                </div>
                <div>
                  <div className="text-base-content/60">ビルド</div>
                  <div className="font-semibold">Next.js 15.3.3</div>
                </div>
                <div>
                  <div className="text-base-content/60">環境</div>
                  <div className="font-semibold">Development</div>
                </div>
              </div>
            </div>
            </div>
          )}

          {/* システム設定 */}
          {activeTab === 'system' && (
            <div className="card bg-base-200 shadow-xl neo-depth">
            <div className="card-body">
              <h2 className="card-title flex items-center gap-2">
                <Database size={20} />
                システム
              </h2>
              
              <div className="space-y-4">
                <div className="stats shadow w-full">
                  <div className="stat">
                    <div className="stat-title">ストレージ使用量</div>
                    <div className="stat-value text-primary">2.4GB</div>
                    <div className="stat-desc">残り容量: 7.6GB</div>
                  </div>
                  
                  <div className="stat">
                    <div className="stat-title">キャッシュサイズ</div>
                    <div className="stat-value text-secondary">456MB</div>
                    <div className="stat-desc">
                      <button className="btn btn-xs btn-outline">クリア</button>
                    </div>
                  </div>
                </div>

                <div className="divider">危険な操作</div>

                <div className="flex gap-2">
                  <button 
                    className="btn btn-warning btn-sm neo-hover"
                    onClick={resetSettings}
                  >
                    <Trash2 size={16} />
                    設定をリセット
                  </button>
                  
                  <button className="btn btn-error btn-sm neo-hover">
                    <Trash2 size={16} />
                    全データを削除
                  </button>
                </div>
              </div>
            </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}