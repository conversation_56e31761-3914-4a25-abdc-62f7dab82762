'use client';

import { useState, useRef, useEffect } from 'react';
import { Globe, Eye, Play, Pause, Square, Settings, Camera, Mic, Bot, MonitorSpeaker, RefreshCw, ArrowLeft, ArrowRight, Home } from 'lucide-react';

interface BrowserSession {
  id: string;
  url: string;
  title: string;
  status: 'idle' | 'running' | 'paused' | 'error';
  screenshot?: string;
  createdAt: Date;
}

interface AutomationCommand {
  id: string;
  type: 'click' | 'type' | 'scroll' | 'wait' | 'navigate' | 'screenshot';
  target: string;
  value?: string;
  description: string;
}

export default function BrowserAutomationPanel() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [currentSession, setCurrentSession] = useState<BrowserSession | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [aiEnabled, setAiEnabled] = useState(true);
  const [sessions, setSessions] = useState<BrowserSession[]>([]);
  const [commands, setCommands] = useState<AutomationCommand[]>([]);
  const [currentUrl, setCurrentUrl] = useState('https://www.google.com');
  const [displayUrl, setDisplayUrl] = useState('https://www.google.com');
  const [isLoading, setIsLoading] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [history, setHistory] = useState<string[]>(['https://www.google.com']);
  const [historyIndex, setHistoryIndex] = useState(0);

  // デフォルトセッションを開始
  useEffect(() => {
    if (!currentSession) {
      startNewSession();
    }
  }, []);

  const startNewSession = () => {
    const newSession: BrowserSession = {
      id: Date.now().toString(),
      url: currentUrl,
      title: 'Googleセッション',
      status: 'running',
      createdAt: new Date()
    };
    setSessions(prev => [newSession, ...prev]);
    setCurrentSession(newSession);
    navigateToUrl(currentUrl);
  };

  const navigateToUrl = (url: string) => {
    setIsLoading(true);
    setDisplayUrl(url);
    setCurrentUrl(url);
    
    // 履歴更新
    if (historyIndex < history.length - 1) {
      setHistory(prev => [...prev.slice(0, historyIndex + 1), url]);
    } else {
      setHistory(prev => [...prev, url]);
    }
    setHistoryIndex(prev => prev + 1);
    
    // セッション更新
    if (currentSession) {
      setCurrentSession({
        ...currentSession,
        url: url,
        status: 'running'
      });
    }
    
    setTimeout(() => setIsLoading(false), 1000);
  };

  const goBack = () => {
    if (historyIndex > 0) {
      const prevIndex = historyIndex - 1;
      const prevUrl = history[prevIndex];
      setHistoryIndex(prevIndex);
      setDisplayUrl(prevUrl);
      setCurrentUrl(prevUrl);
    }
  };

  const goForward = () => {
    if (historyIndex < history.length - 1) {
      const nextIndex = historyIndex + 1;
      const nextUrl = history[nextIndex];
      setHistoryIndex(nextIndex);
      setDisplayUrl(nextUrl);
      setCurrentUrl(nextUrl);
    }
  };

  const goHome = () => {
    navigateToUrl('https://www.google.com');
  };

  const refreshPage = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 500);
  };

  const toggleRecording = () => {
    setIsRecording(!isRecording);
    if (currentSession) {
      setCurrentSession({
        ...currentSession,
        status: isRecording ? 'idle' : 'running'
      });
    }
  };

  const takeScreenshot = () => {
    if (currentSession) {
      // 実際のスクリーンショット実装をここに
      console.log('スクリーンショットを撮影中...');
    }
  };

  const executeAIAction = (action: string) => {
    if (!aiEnabled || !currentSession) return;
    
    const newCommand: AutomationCommand = {
      id: Date.now().toString(),
      type: 'click',
      target: 'AI選択要素',
      description: `AI実行: ${action}`,
    };
    
    setCommands(prev => [...prev, newCommand]);
    console.log(`AI動作実行: ${action}`);
  };

  return (
    <div className="border-b border-base-content/10 bg-base-200/40">
      {/* ヘッダー */}
      <div className="p-3 border-b border-base-content/10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-info/20 rounded-full flex items-center justify-center text-xs">
              🌐
            </div>
            <h3 className="font-medium text-sm">ブラウザ自動化</h3>
          </div>
          <div className="flex items-center gap-1">
            <button
              className={`btn btn-ghost btn-xs ${aiEnabled ? 'text-secondary' : 'text-base-content/40'}`}
              onClick={() => setAiEnabled(!aiEnabled)}
              title={aiEnabled ? 'AI連携をオフ' : 'AI連携をオン'}
            >
              <Bot size={12} />
            </button>
            <button
              className="btn btn-ghost btn-xs"
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              {isCollapsed ? '▼' : '▲'}
            </button>
          </div>
        </div>
      </div>

      {/* コンテンツ */}
      {!isCollapsed && (
        <div className="p-3 space-y-3">
          {/* ブラウザナビゲーション */}
          <div className="space-y-2">
            <div className="flex gap-1">
              <button 
                className={`btn btn-xs ${historyIndex <= 0 ? 'btn-disabled' : 'btn-outline'}`}
                onClick={goBack}
                disabled={historyIndex <= 0}
              >
                <ArrowLeft size={10} />
              </button>
              <button 
                className={`btn btn-xs ${historyIndex >= history.length - 1 ? 'btn-disabled' : 'btn-outline'}`}
                onClick={goForward}
                disabled={historyIndex >= history.length - 1}
              >
                <ArrowRight size={10} />
              </button>
              <button className="btn btn-xs btn-outline" onClick={refreshPage}>
                <RefreshCw size={10} />
              </button>
              <button className="btn btn-xs btn-outline" onClick={goHome}>
                <Home size={10} />
              </button>
            </div>
            
            <div className="flex gap-2">
              <div className="flex-1">
                <input
                  type="url"
                  className="input input-xs w-full"
                  placeholder="https://www.google.com"
                  value={currentUrl}
                  onChange={(e) => setCurrentUrl(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && navigateToUrl(currentUrl)}
                />
              </div>
              <button 
                className="btn btn-xs btn-primary"
                onClick={() => navigateToUrl(currentUrl)}
              >
                <Globe size={10} />
                移動
              </button>
            </div>
          </div>

          {/* 現在のセッション */}
          {currentSession && (
            <div className="bg-base-100 rounded-lg p-3 space-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-xs font-medium">{currentSession.title}</div>
                  <div className="text-xs text-base-content/60 truncate">{currentSession.url}</div>
                </div>
                <div className={`badge badge-xs ${
                  currentSession.status === 'running' ? 'badge-success' : 
                  currentSession.status === 'error' ? 'badge-error' : 'badge-neutral'
                }`}>
                  {currentSession.status === 'running' ? '実行中' : 
                   currentSession.status === 'error' ? 'エラー' : '待機中'}
                </div>
              </div>

              {/* 制御ボタン */}
              <div className="flex gap-1">
                <button
                  className={`btn btn-xs ${isRecording ? 'btn-error' : 'btn-success'}`}
                  onClick={toggleRecording}
                >
                  {isRecording ? <Pause size={8} /> : <Play size={8} />}
                  {isRecording ? '停止' : '録画'}
                </button>
                <button className="btn btn-xs btn-outline" onClick={takeScreenshot}>
                  <Camera size={8} />
                  撮影
                </button>
                <button className="btn btn-xs btn-outline">
                  <Settings size={8} />
                  設定
                </button>
              </div>

              {/* ブラウザ表示エリア */}
              <div className="aspect-video bg-base-300/50 rounded border border-base-content/20 relative overflow-hidden">
                {isLoading && (
                  <div className="absolute inset-0 bg-base-100/80 flex items-center justify-center z-10">
                    <div className="flex items-center gap-2 text-sm">
                      <span className="loading loading-spinner loading-sm"></span>
                      読み込み中...
                    </div>
                  </div>
                )}
                
                {currentSession && displayUrl ? (
                  <div className="w-full h-full bg-white">
                    {displayUrl.includes('google.com') ? (
                      // Googleデモ画面
                      <div className="h-full flex flex-col">
                        {/* Googleヘッダー */}
                        <div className="px-4 py-2 flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <a href="#" className="text-sm text-gray-700 hover:underline">Gmail</a>
                            <a href="#" className="text-sm text-gray-700 hover:underline">画像</a>
                          </div>
                          <div className="flex items-center gap-2">
                            <button className="p-2 hover:bg-gray-100 rounded-full">
                              <svg className="w-4 h-4 text-gray-600" viewBox="0 0 24 24">
                                <path fill="currentColor" d="M12 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 12c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                              </svg>
                            </button>
                            <button className="btn btn-sm btn-primary rounded-lg">ログイン</button>
                          </div>
                        </div>
                        
                        {/* Google検索部分 */}
                        <div className="flex-1 flex flex-col items-center justify-center px-4">
                          {/* Googleロゴ */}
                          <div className="mb-6">
                            <svg className="w-32 h-12" viewBox="0 0 272 92">
                              <path fill="#4285F4" d="M115.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18C71.25 34.32 81.24 25 93.5 25s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44S80.99 39.2 80.99 47.18c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z"/>
                              <path fill="#EA4335" d="M163.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18c0-12.85 9.99-22.18 22.25-22.18s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44s-12.51 5.46-12.51 13.44c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z"/>
                              <path fill="#FBBC05" d="M209.75 26.34v39.82c0 16.38-9.66 23.07-21.08 23.07-10.75 0-17.22-7.19-19.66-13.07l8.48-3.53c1.51 3.61 5.21 7.87 11.17 7.87 7.31 0 11.84-4.51 11.84-13v-3.19h-.34c-2.18 2.69-6.38 5.04-11.68 5.04-11.09 0-21.25-9.66-21.25-22.09 0-12.52 10.16-22.26 21.25-22.26 5.29 0 9.49 2.35 11.68 4.96h.34v-3.61h9.25zm-8.56 20.92c0-7.81-5.21-13.52-11.84-13.52-6.72 0-12.35 5.71-12.35 13.52 0 7.73 5.63 13.36 12.35 13.36 6.63 0 11.84-5.63 11.84-13.36z"/>
                              <path fill="#34A853" d="M225 3v65h-9.5V3h9.5z"/>
                              <path fill="#EA4335" d="M262.02 54.48l7.56 5.04c-2.44 3.61-8.32 9.83-18.48 9.83-12.6 0-22.01-9.74-22.01-22.18 0-13.19 9.49-22.18 20.92-22.18 11.51 0 17.14 9.16 18.98 14.11l1.01 2.52-29.65 12.28c2.27 4.45 5.8 6.72 10.75 6.72 4.96 0 8.4-2.44 10.92-6.14zm-23.27-7.98l19.82-8.23c-1.09-2.77-4.37-4.7-8.23-4.7-4.95 0-11.84 4.37-11.59 12.93z"/>
                              <path fill="#4285F4" d="M35.29 41.41V32H67c.31 1.64.47 3.58.47 5.68 0 7.06-1.93 15.79-8.15 22.01-6.05 6.3-13.78 9.66-24.02 9.66C16.32 69.35.36 53.89.36 34.91.36 15.93 16.32.47 35.3.47c10.5 0 17.98 4.12 23.6 9.49l-6.64 6.64c-4.03-3.78-9.49-6.72-16.97-6.72-13.86 0-24.7 11.17-24.7 25.03 0 13.86 10.84 25.03 24.7 25.03 8.99 0 14.11-3.61 17.39-6.89 2.66-2.66 4.41-6.46 5.1-11.65l-22.49.01z"/>
                            </svg>
                          </div>
                          
                          {/* 検索ボックス */}
                          <div className="w-full max-w-md">
                            <div className="relative">
                              <input
                                type="text"
                                className="w-full px-4 py-3 pr-12 text-sm border border-gray-300 rounded-full hover:shadow-md focus:shadow-md focus:outline-none"
                                placeholder="Google 検索または URL を入力"
                              />
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                                <button className="p-1 hover:bg-gray-100 rounded">
                                  <svg className="w-5 h-5 text-blue-500" viewBox="0 0 24 24">
                                    <path fill="currentColor" d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                                    <path fill="currentColor" d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                                  </svg>
                                </button>
                              </div>
                            </div>
                            
                            {/* 検索ボタン */}
                            <div className="flex justify-center gap-3 mt-4">
                              <button className="px-4 py-2 text-sm bg-gray-50 hover:bg-gray-100 border border-gray-100 rounded">
                                Google 検索
                              </button>
                              <button className="px-4 py-2 text-sm bg-gray-50 hover:bg-gray-100 border border-gray-100 rounded">
                                I'm Feeling Lucky
                              </button>
                            </div>
                          </div>
                        </div>
                        
                        {/* フッター */}
                        <div className="px-4 py-2 bg-gray-100 text-xs text-gray-600">
                          <div className="text-center">日本</div>
                        </div>
                      </div>
                    ) : (
                      // 他のサイトの場合のプレースホルダー
                      <div className="h-full flex items-center justify-center bg-gradient-to-br from-base-200 to-base-300">
                        <div className="text-center">
                          <Globe size={48} className="mx-auto mb-3 text-base-content/30" />
                          <div className="text-sm font-medium mb-1">ブラウザプレビュー</div>
                          <div className="text-xs text-base-content/60">
                            {displayUrl}
                          </div>
                          <div className="mt-4 text-xs text-base-content/50 max-w-xs mx-auto">
                            デモ表示中。実際のブラウザ操作はPuppeteer統合で可能になります。
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="h-full flex items-center justify-center text-xs text-base-content/60">
                    <div className="text-center">
                      <MonitorSpeaker size={20} className="mx-auto mb-1 opacity-50" />
                      <div>ブラウザ画面</div>
                      <div className="text-xs mt-1">URLを入力して開始してください</div>
                    </div>
                  </div>
                )}
              </div>
              
            </div>
          )}

          {/* AI音声コマンド（洗練版） */}
          {aiEnabled && (
            <div className="bg-gradient-to-r from-secondary/10 to-secondary/5 rounded-lg p-3 border border-secondary/20">
              <div className="flex items-center justify-between mb-2">
                <div className="text-sm font-semibold flex items-center gap-2">
                  <Bot size={14} className="text-secondary" />
                  AI音声アシスタント
                </div>
                <div className="flex items-center gap-1">
                  <button className="btn btn-xs btn-circle btn-secondary btn-outline">
                    <Mic size={10} />
                  </button>
                  <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                </div>
              </div>
              
              <div className="text-xs text-base-content/70 mb-2">
                画面を見ながら音声で操作指示
              </div>
              
              {/* コンパクトな操作ボタン */}
              <div className="flex gap-1 mb-2">
                <button 
                  className="btn btn-xs btn-outline flex-1"
                  onClick={() => executeAIAction('ページをスクロール')}
                  title="画面を見てスクロール"
                >
                  <Eye size={10} />
                  スクロール
                </button>
                <button 
                  className="btn btn-xs btn-outline flex-1"
                  onClick={() => executeAIAction('リンクをクリック')}
                  title="要素を探してクリック"
                >
                  <Bot size={10} />
                  クリック
                </button>
                <button 
                  className="btn btn-xs btn-outline flex-1"
                  onClick={() => executeAIAction('フォーム入力')}
                  title="フォームに自動入力"
                >
                  ✏️
                  入力
                </button>
                <button 
                  className="btn btn-xs btn-secondary flex-1"
                  onClick={() => executeAIAction('音声認識開始')}
                  title="音声で指示"
                >
                  <Mic size={10} />
                  音声
                </button>
              </div>
              
              <div className="text-xs text-base-content/60 bg-base-100/50 p-2 rounded text-center">
                💡 音声例：「検索欄に〇〇と入力」「このリンクをクリック」
              </div>
            </div>
          )}

          {/* 実行コマンド履歴 */}
          {commands.length > 0 && (
            <div className="space-y-1">
              <div className="text-xs font-medium">実行履歴</div>
              <div className="max-h-20 overflow-y-auto space-y-1">
                {commands.slice(-3).map((command) => (
                  <div key={command.id} className="text-xs bg-base-100 rounded p-1">
                    <span className="font-mono text-accent">{command.type}</span>: {command.description}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* セッション履歴 */}
          {sessions.length > 1 && (
            <div className="space-y-1">
              <div className="text-xs font-medium">セッション履歴</div>
              <div className="max-h-16 overflow-y-auto space-y-1">
                {sessions.slice(1, 3).map((session) => (
                  <button
                    key={session.id}
                    className="w-full text-left text-xs bg-base-100 rounded p-1 hover:bg-base-200 transition-colors"
                    onClick={() => setCurrentSession(session)}
                  >
                    <div className="truncate">{session.url}</div>
                    <div className="text-base-content/60">{session.createdAt.toLocaleTimeString()}</div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}