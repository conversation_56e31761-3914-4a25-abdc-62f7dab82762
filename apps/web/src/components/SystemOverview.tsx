'use client';

interface SystemOverviewProps {
  onLauncherClick?: () => void;
  onDashboardClick?: () => void;
  onEditorClick?: () => void;
  onChatClick?: () => void;
}

export default function SystemOverview({ 
  onLauncherClick, 
  onDashboardClick, 
  onEditorClick, 
  onChatClick 
}: SystemOverviewProps) {
  return (
    <div className="flex-1 flex flex-col neo-glass">
      {/* メインコンテンツエリア */}
      <div className="flex-1 p-6">
        <div className="max-w-4xl mx-auto">
          {/* ヘッダーセクション */}
          <div className="text-center mb-8">
            <h1 className="text-6xl font-bold mb-4 text-base-content animate-float">
              メタスタジオ
            </h1>
            <p className="text-xl text-base-content/70 mb-8">
              脳内現実化ツール進化版 + Projext統合開発フレームワーク
            </p>
          </div>

          {/* ナビゲーション・ランチャー */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <button 
              className="card bg-gradient-to-br from-primary/20 to-primary/5 shadow-xl neo-depth neo-hover border border-primary/20 cursor-pointer"
              onClick={onLauncherClick}
            >
              <div className="card-body text-center p-4">
                <div className="text-2xl mb-2">🚀</div>
                <h3 className="text-sm font-semibold text-primary">新プロジェクト</h3>
                <p className="text-xs text-base-content/60">Cmd+K で起動</p>
              </div>
            </button>
            
            <button 
              className="card bg-gradient-to-br from-secondary/20 to-secondary/5 shadow-xl neo-depth neo-hover border border-secondary/20 cursor-pointer"
              onClick={onDashboardClick}
            >
              <div className="card-body text-center p-4">
                <div className="text-2xl mb-2">📊</div>
                <h3 className="text-sm font-semibold text-secondary">ダッシュボード</h3>
                <p className="text-xs text-base-content/60">ウィジェット管理</p>
              </div>
            </button>
            
            <button 
              className="card bg-gradient-to-br from-accent/20 to-accent/5 shadow-xl neo-depth neo-hover border border-accent/20 cursor-pointer"
              onClick={onEditorClick}
            >
              <div className="card-body text-center p-4">
                <div className="text-2xl mb-2">✍️</div>
                <h3 className="text-sm font-semibold text-accent">エディター</h3>
                <p className="text-xs text-base-content/60">Craft風編集</p>
              </div>
            </button>
            
            <button 
              className="card bg-gradient-to-br from-info/20 to-info/5 shadow-xl neo-depth neo-hover border border-info/20 cursor-pointer"
              onClick={onChatClick}
            >
              <div className="card-body text-center p-4">
                <div className="text-2xl mb-2">💬</div>
                <h3 className="text-sm font-semibold text-info">チャット</h3>
                <p className="text-xs text-base-content/60">母（CTO）</p>
              </div>
            </button>
          </div>

          {/* システム概要統計 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div className="stat bg-base-200 rounded-lg neo-depth">
              <div className="stat-title text-base-content/70">完成プロジェクト</div>
              <div className="stat-value text-primary">127</div>
              <div className="stat-desc text-success">+12% 今月</div>
            </div>
            
            <div className="stat bg-base-200 rounded-lg neo-depth">
              <div className="stat-title text-base-content/70">実現率</div>
              <div className="stat-value text-secondary">92%</div>
              <div className="stat-desc text-success">+5% 向上</div>
            </div>
            
            <div className="stat bg-base-200 rounded-lg neo-depth">
              <div className="stat-title text-base-content/70">アクティブエージェント</div>
              <div className="stat-value text-accent">15</div>
              <div className="stat-desc text-info">3将 + 12兵</div>
            </div>
            
            <div className="stat bg-base-200 rounded-lg neo-depth">
              <div className="stat-title text-base-content/70">学習効率</div>
              <div className="stat-value text-warning">+18%</div>
              <div className="stat-desc text-success">継続改善中</div>
            </div>
          </div>

          {/* 最近のアクティビティ */}
          <div className="card bg-base-200 shadow-xl neo-depth">
            <div className="card-body">
              <h3 className="card-title text-lg">最近のアクティビティ</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-2 rounded hover:bg-base-300/50">
                  <div className="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
                    ✅
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-sm">チャット・キャラクター同期機能</div>
                    <div className="text-xs text-base-content/60">タスク13 完了 - 2分前</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-2 rounded hover:bg-base-300/50">
                  <div className="w-8 h-8 bg-success/20 rounded-full flex items-center justify-center">
                    📌
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-sm">タブ固定ピン機能</div>
                    <div className="text-xs text-base-content/60">タスク12 完了 - 15分前</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-2 rounded hover:bg-base-300/50">
                  <div className="w-8 h-8 bg-info/20 rounded-full flex items-center justify-center">
                    🏗️
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-sm">プレースホルダー要素の実装</div>
                    <div className="text-xs text-base-content/60">タスク8 完了 - 1時間前</div>
                  </div>
                </div>
              </div>
              
              <div className="card-actions justify-end mt-4">
                <button className="btn btn-sm btn-ghost">すべて表示</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}