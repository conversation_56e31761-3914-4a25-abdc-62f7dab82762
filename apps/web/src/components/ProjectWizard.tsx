import React, { useState, useCallback } from 'react';
import { X, ChevronLeft, ChevronRight, CheckCircle, Circle, FileText, Users, Cog, Target, Package } from 'lucide-react';

interface ProjectWizardProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (projextData: ProjectxtData) => void;
}

interface ProjectxtData {
  name: string;
  type: string;
  vision: {
    goal: string;
    stakeholders: string;
    constraints: string;
  };
  requirements: {
    functional: string[];
    nonFunctional: string[];
    userStories: string[];
  };
  design: {
    architecture: string;
    sequence: string;
  };
  agents: {
    systemPrompt: string;
    tasks: string[];
  };
  artifacts: {
    docs: string[];
    tests: string[];
  };
}

const WIZARD_STEPS = [
  { id: 'overview', title: 'プロジェクト概要', icon: Target, phase: 'vision' },
  { id: 'vision', title: 'ビジョン・目標', icon: Target, phase: 'vision' },
  { id: 'stakeholders', title: 'ステークホルダー', icon: Users, phase: 'vision' },
  { id: 'requirements', title: '機能要件', icon: FileText, phase: 'requirements' },
  { id: 'user-stories', title: 'ユーザーストーリー', icon: FileText, phase: 'requirements' },
  { id: 'design', title: '設計・アーキテクチャ', icon: Cog, phase: 'design' },
  { id: 'agents', title: 'エージェント設定', icon: Users, phase: 'agents' },
  { id: 'artifacts', title: '成果物定義', icon: Package, phase: 'artifacts' },
  { id: 'summary', title: '確認・完了', icon: CheckCircle, phase: 'summary' },
];

export const ProjectWizard: React.FC<ProjectWizardProps> = ({
  isOpen,
  onClose,
  onComplete
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [projextData, setProjectxtData] = useState<ProjectxtData>({
    name: '',
    type: '',
    vision: { goal: '', stakeholders: '', constraints: '' },
    requirements: { functional: [], nonFunctional: [], userStories: [] },
    design: { architecture: '', sequence: '' },
    agents: { systemPrompt: '', tasks: [] },
    artifacts: { docs: [], tests: [] }
  });

  const currentStepData = WIZARD_STEPS[currentStep];
  const progress = ((currentStep + 1) / WIZARD_STEPS.length) * 100;

  const handleNext = useCallback(() => {
    if (currentStep < WIZARD_STEPS.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  }, [currentStep]);

  const handlePrevious = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  }, [currentStep]);

  const handleComplete = useCallback(() => {
    onComplete(projextData);
    onClose();
  }, [projextData, onComplete, onClose]);

  const updateProjectxtData = useCallback((updates: Partial<ProjectxtData>) => {
    setProjectxtData(prev => ({ ...prev, ...updates }));
  }, []);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-base-100 rounded-2xl shadow-2xl w-full max-w-4xl h-[90vh] flex flex-col overflow-hidden">
        
        {/* Header */}
        <div className="bg-gradient-to-r from-primary to-secondary p-6 text-primary-content">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold">📋 Projext作成ウィザード</h2>
            <button 
              onClick={onClose}
              className="btn btn-ghost btn-circle btn-sm text-primary-content hover:bg-primary-content/20"
            >
              <X size={20} />
            </button>
          </div>
          
          {/* Progress Indicator */}
          <div className="space-y-3">
            <div className="flex justify-between items-center text-sm">
              <span>Step {currentStep + 1} of {WIZARD_STEPS.length}</span>
              <span>{Math.round(progress)}% 完了</span>
            </div>
            <div className="w-full bg-primary-content/20 rounded-full h-2">
              <div 
                className="bg-primary-content h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
          
          {/* Step Navigation */}
          <div className="flex items-center gap-2 mt-4 overflow-x-auto pb-2">
            {WIZARD_STEPS.map((step, index) => {
              const Icon = step.icon;
              const isCompleted = index < currentStep;
              const isCurrent = index === currentStep;
              
              return (
                <div 
                  key={step.id}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm whitespace-nowrap transition-all ${
                    isCurrent 
                      ? 'bg-primary-content/20 text-primary-content font-semibold' 
                      : isCompleted 
                        ? 'bg-primary-content/10 text-primary-content/80'
                        : 'text-primary-content/60'
                  }`}
                >
                  {isCompleted ? (
                    <CheckCircle size={16} className="text-success" />
                  ) : isCurrent ? (
                    <Icon size={16} className="animate-pulse" />
                  ) : (
                    <Circle size={16} />
                  )}
                  <span>{step.title}</span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          <ProjectWizardStep
            step={currentStepData}
            data={projextData}
            onUpdate={updateProjectxtData}
          />
        </div>

        {/* Footer */}
        <div className="bg-base-200 p-6 flex justify-between items-center">
          <div className="flex items-center gap-2 text-base-content/70">
            <span className="text-lg">📍</span>
            <span className="text-sm">
              現在: {currentStepData.phase} フェーズ → {currentStepData.title}
            </span>
          </div>
          
          <div className="flex gap-3">
            <button 
              onClick={handlePrevious}
              disabled={currentStep === 0}
              className="btn btn-outline gap-2"
            >
              <ChevronLeft size={16} />
              戻る
            </button>
            
            {currentStep === WIZARD_STEPS.length - 1 ? (
              <button 
                onClick={handleComplete}
                className="btn btn-primary gap-2"
              >
                <CheckCircle size={16} />
                Projext作成完了
              </button>
            ) : (
              <button 
                onClick={handleNext}
                className="btn btn-primary gap-2"
              >
                次へ
                <ChevronRight size={16} />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// ステップ別コンテンツコンポーネント
interface ProjectWizardStepProps {
  step: typeof WIZARD_STEPS[0];
  data: ProjectxtData;
  onUpdate: (updates: Partial<ProjectxtData>) => void;
}

const ProjectWizardStep: React.FC<ProjectWizardStepProps> = ({ step, data, onUpdate }) => {
  switch (step.id) {
    case 'overview':
      return (
        <div className="space-y-6">
          <div className="text-center space-y-4">
            <div className="text-6xl">🚀</div>
            <h3 className="text-2xl font-bold">新しいProjectを作成しましょう</h3>
            <p className="text-base-content/70 max-w-2xl mx-auto">
              このウィザードでは、vision（ビジョン）→ requirements（要件）→ design（設計）→ agents（エージェント）→ artifacts（成果物）の
              5つのフェーズを順番に進めて、完全な要件定義群を作成します。
            </p>
          </div>
          
          <div className="bg-base-200 rounded-xl p-6 space-y-4">
            <h4 className="font-semibold text-lg">プロジェクト基本情報</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="label">
                  <span className="label-text font-medium">プロジェクト名 *</span>
                </label>
                <input 
                  type="text"
                  placeholder="例: 瞑想タイマーアプリ"
                  className="input input-bordered w-full"
                  value={data.name}
                  onChange={(e) => onUpdate({ name: e.target.value })}
                />
              </div>
              
              <div>
                <label className="label">
                  <span className="label-text font-medium">プロジェクトタイプ *</span>
                </label>
                <select 
                  className="select select-bordered w-full"
                  value={data.type}
                  onChange={(e) => onUpdate({ type: e.target.value })}
                >
                  <option value="">選択してください</option>
                  <option value="mobile-app">📱 モバイルアプリ</option>
                  <option value="web-app">🌐 Webアプリケーション</option>
                  <option value="desktop-app">💻 デスクトップアプリ</option>
                  <option value="api-service">🔌 API・サービス</option>
                  <option value="ai-tool">🤖 AIツール</option>
                  <option value="game">🎮 ゲーム</option>
                  <option value="content">📝 コンテンツ・メディア</option>
                  <option value="other">🔧 その他</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      );
      
    case 'vision':
      return (
        <div className="space-y-6">
          <div className="text-center space-y-2">
            <div className="text-4xl">🎯</div>
            <h3 className="text-xl font-bold">プロジェクトのビジョンと目標</h3>
            <p className="text-base-content/70">何を作るか、なぜ作るかを明確にしましょう</p>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="label">
                <span className="label-text font-medium">プロジェクトの目標・ゴール</span>
              </label>
              <textarea 
                placeholder="このプロジェクトで何を実現したいか、どんな問題を解決したいかを記述してください..."
                className="textarea textarea-bordered w-full h-32"
                value={data.vision.goal}
                onChange={(e) => onUpdate({ 
                  vision: { ...data.vision, goal: e.target.value }
                })}
              />
            </div>
            
            <div>
              <label className="label">
                <span className="label-text font-medium">成功指標・KPI</span>
              </label>
              <textarea 
                placeholder="どのような状態になれば成功と言えるか、測定可能な指標があれば記述してください..."
                className="textarea textarea-bordered w-full h-24"
                value={data.vision.constraints}
                onChange={(e) => onUpdate({ 
                  vision: { ...data.vision, constraints: e.target.value }
                })}
              />
            </div>
          </div>
        </div>
      );
      
    case 'stakeholders':
      return (
        <div className="space-y-6">
          <div className="text-center space-y-2">
            <div className="text-4xl">👥</div>
            <h3 className="text-xl font-bold">ステークホルダー</h3>
            <p className="text-base-content/70">誰のために作るか、関係者を整理しましょう</p>
          </div>
          
          <div>
            <label className="label">
              <span className="label-text font-medium">対象ユーザー・関係者</span>
            </label>
            <textarea 
              placeholder="プライマリユーザー、セカンダリユーザー、その他の関係者について記述してください..."
              className="textarea textarea-bordered w-full h-40"
              value={data.vision.stakeholders}
              onChange={(e) => onUpdate({ 
                vision: { ...data.vision, stakeholders: e.target.value }
              })}
            />
          </div>
        </div>
      );
      
    case 'requirements':
      return (
        <div className="space-y-6">
          <div className="text-center space-y-2">
            <div className="text-4xl">📋</div>
            <h3 className="text-xl font-bold">機能要件</h3>
            <p className="text-base-content/70">どんな機能が必要かを整理しましょう</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="label">
                <span className="label-text font-medium">必須機能（Must Have）</span>
              </label>
              <textarea 
                placeholder="このプロジェクトに絶対に必要な機能を列挙してください..."
                className="textarea textarea-bordered w-full h-32"
                value={data.requirements.functional.join('\n')}
                onChange={(e) => onUpdate({ 
                  requirements: { 
                    ...data.requirements, 
                    functional: e.target.value.split('\n').filter(item => item.trim())
                  }
                })}
              />
            </div>
            
            <div>
              <label className="label">
                <span className="label-text font-medium">非機能要件</span>
              </label>
              <textarea 
                placeholder="パフォーマンス、セキュリティ、使いやすさなどの要件..."
                className="textarea textarea-bordered w-full h-32"
                value={data.requirements.nonFunctional.join('\n')}
                onChange={(e) => onUpdate({ 
                  requirements: { 
                    ...data.requirements, 
                    nonFunctional: e.target.value.split('\n').filter(item => item.trim())
                  }
                })}
              />
            </div>
          </div>
        </div>
      );
      
    case 'user-stories':
      return (
        <div className="space-y-6">
          <div className="text-center space-y-2">
            <div className="text-4xl">👤</div>
            <h3 className="text-xl font-bold">ユーザーストーリー</h3>
            <p className="text-base-content/70">ユーザーの視点で機能を整理しましょう</p>
          </div>
          
          <div className="space-y-4">
            <div className="bg-info/10 border border-info/20 rounded-lg p-4">
              <h4 className="font-semibold text-info mb-2">📝 ユーザーストーリーの書き方</h4>
              <p className="text-sm text-base-content/70">
                「〜として、〜したい、なぜなら〜だから」の形式で記述してください
              </p>
              <p className="text-xs text-base-content/60 mt-1">
                例: 瞑想初心者として、ガイド付きの瞑想セッションを利用したい、なぜなら正しい瞑想方法を学びたいから
              </p>
            </div>
            
            <div>
              <label className="label">
                <span className="label-text font-medium">ユーザーストーリー</span>
              </label>
              <textarea 
                placeholder="一行に一つずつユーザーストーリーを記述してください..."
                className="textarea textarea-bordered w-full h-40"
                value={data.requirements.userStories.join('\n')}
                onChange={(e) => onUpdate({ 
                  requirements: { 
                    ...data.requirements, 
                    userStories: e.target.value.split('\n').filter(item => item.trim())
                  }
                })}
              />
            </div>
          </div>
        </div>
      );
      
    case 'design':
      return (
        <div className="space-y-6">
          <div className="text-center space-y-2">
            <div className="text-4xl">🏗️</div>
            <h3 className="text-xl font-bold">設計・アーキテクチャ</h3>
            <p className="text-base-content/70">技術構成とシステム設計を考えましょう</p>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="label">
                <span className="label-text font-medium">アーキテクチャ概要</span>
              </label>
              <textarea 
                placeholder="使用技術、システム構成、データフローなどを記述してください..."
                className="textarea textarea-bordered w-full h-32"
                value={data.design.architecture}
                onChange={(e) => onUpdate({ 
                  design: { ...data.design, architecture: e.target.value }
                })}
              />
            </div>
            
            <div>
              <label className="label">
                <span className="label-text font-medium">主要なフロー・シーケンス</span>
              </label>
              <textarea 
                placeholder="ユーザーの主要な操作フローや処理シーケンスを記述してください..."
                className="textarea textarea-bordered w-full h-32"
                value={data.design.sequence}
                onChange={(e) => onUpdate({ 
                  design: { ...data.design, sequence: e.target.value }
                })}
              />
            </div>
          </div>
        </div>
      );
      
    case 'agents':
      return (
        <div className="space-y-6">
          <div className="text-center space-y-2">
            <div className="text-4xl">🤖</div>
            <h3 className="text-xl font-bold">エージェント設定</h3>
            <p className="text-base-content/70">AIエージェントの役割とタスクを定義しましょう</p>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="label">
                <span className="label-text font-medium">システムプロンプト</span>
              </label>
              <textarea 
                placeholder="AIエージェントの役割、性格、専門分野を記述してください..."
                className="textarea textarea-bordered w-full h-32"
                value={data.agents.systemPrompt}
                onChange={(e) => onUpdate({ 
                  agents: { ...data.agents, systemPrompt: e.target.value }
                })}
              />
            </div>
            
            <div>
              <label className="label">
                <span className="label-text font-medium">実行タスク</span>
              </label>
              <textarea 
                placeholder="エージェントが実行すべきタスクを一行に一つずつ記述してください..."
                className="textarea textarea-bordered w-full h-32"
                value={data.agents.tasks.join('\n')}
                onChange={(e) => onUpdate({ 
                  agents: { 
                    ...data.agents, 
                    tasks: e.target.value.split('\n').filter(item => item.trim())
                  }
                })}
              />
            </div>
          </div>
        </div>
      );
      
    case 'artifacts':
      return (
        <div className="space-y-6">
          <div className="text-center space-y-2">
            <div className="text-4xl">📦</div>
            <h3 className="text-xl font-bold">成果物定義</h3>
            <p className="text-base-content/70">最終的に作成する成果物を定義しましょう</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="label">
                <span className="label-text font-medium">ドキュメント類</span>
              </label>
              <textarea 
                placeholder="作成すべきドキュメント（README、API仕様書など）..."
                className="textarea textarea-bordered w-full h-32"
                value={data.artifacts.docs.join('\n')}
                onChange={(e) => onUpdate({ 
                  artifacts: { 
                    ...data.artifacts, 
                    docs: e.target.value.split('\n').filter(item => item.trim())
                  }
                })}
              />
            </div>
            
            <div>
              <label className="label">
                <span className="label-text font-medium">テスト・検証</span>
              </label>
              <textarea 
                placeholder="実装すべきテスト（単体テスト、E2Eテストなど）..."
                className="textarea textarea-bordered w-full h-32"
                value={data.artifacts.tests.join('\n')}
                onChange={(e) => onUpdate({ 
                  artifacts: { 
                    ...data.artifacts, 
                    tests: e.target.value.split('\n').filter(item => item.trim())
                  }
                })}
              />
            </div>
          </div>
        </div>
      );
      
    case 'summary':
      return (
        <div className="space-y-6">
          <div className="text-center space-y-2">
            <div className="text-4xl">✨</div>
            <h3 className="text-xl font-bold">Projext作成完了</h3>
            <p className="text-base-content/70">入力内容を確認して、Projextを作成しましょう</p>
          </div>
          
          <div className="bg-base-200 rounded-xl p-6 space-y-4 max-h-96 overflow-y-auto">
            <div>
              <h4 className="font-semibold text-lg mb-2">📋 {data.name}</h4>
              <p className="text-sm text-base-content/70 mb-4">タイプ: {data.type}</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <h5 className="font-medium">🎯 ビジョン</h5>
                <p className="text-base-content/70 text-xs">{data.vision.goal || '未設定'}</p>
              </div>
              
              <div className="space-y-2">
                <h5 className="font-medium">👥 ステークホルダー</h5>
                <p className="text-base-content/70 text-xs">{data.vision.stakeholders || '未設定'}</p>
              </div>
              
              <div className="space-y-2">
                <h5 className="font-medium">📋 機能要件</h5>
                <ul className="text-base-content/70 text-xs space-y-1">
                  {data.requirements.functional && data.requirements.functional.slice(0, 3).map((item, i) => (
                    <li key={`func-${i}`}>• {item}</li>
                  ))}
                  {data.requirements.functional && data.requirements.functional.length > 3 && 
                    <li key="func-more">...他 {data.requirements.functional.length - 3}件</li>
                  }
                </ul>
              </div>
              
              <div className="space-y-2">
                <h5 className="font-medium">👤 ユーザーストーリー</h5>
                <ul className="text-base-content/70 text-xs space-y-1">
                  {data.requirements.userStories && data.requirements.userStories.slice(0, 2).map((item, i) => (
                    <li key={`story-${i}`}>• {item}</li>
                  ))}
                  {data.requirements.userStories && data.requirements.userStories.length > 2 && 
                    <li key="story-more">...他 {data.requirements.userStories.length - 2}件</li>
                  }
                </ul>
              </div>
            </div>
            
            <div className="pt-4 border-t border-base-content/10">
              <div className="flex justify-between items-center text-sm">
                <span>📊 完成度</span>
                <span className="font-medium">
                  {Math.round((
                    (data.name ? 1 : 0) +
                    (data.vision.goal ? 1 : 0) +
                    (data.requirements.functional.length > 0 ? 1 : 0) +
                    (data.requirements.userStories.length > 0 ? 1 : 0) +
                    (data.design.architecture ? 1 : 0)
                  ) / 5 * 100)}%
                </span>
              </div>
            </div>
          </div>
        </div>
      );
      
    default:
      return (
        <div className="text-center space-y-4">
          <div className="text-4xl">🚧</div>
          <h3 className="text-xl font-bold">このステップは準備中です</h3>
          <p className="text-base-content/70">
            {step.title} の詳細フォームを実装中です
          </p>
        </div>
      );
  }
};

export default ProjectWizard;