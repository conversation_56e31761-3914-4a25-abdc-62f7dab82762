'use client';

import { useState, useEffect } from 'react';
import { FileText, Mic, Lightbulb, CheckSquare, Archive, Search, Filter, Plus, ArrowRight, Sparkles, FolderPlus, Brain, Zap } from 'lucide-react';

interface InboxItem {
  id: string;
  type: 'voice' | 'note' | 'idea' | 'task' | 'link' | 'projext_request';
  title: string;
  content: string;
  category: string;
  priority: 'high' | 'medium' | 'low';
  status: 'unprocessed' | 'processing' | 'processed' | 'archived' | 'projext_generating';
  createdAt: Date;
  tags: string[];
  aiSuggestion?: string;
  projextData?: {
    name: string;
    type: string;
    vision: string;
    requirements: string[];
  };
}

export default function InboxSystem() {
  const [items, setItems] = useState<InboxItem[]>([
    {
      id: '1',
      type: 'voice',
      title: '瞑想アプリのUI改善案',
      content: '音声入力：瞑想アプリのタイマー画面をもっと直感的にして、背景色をカスタマイズできるようにしたい',
      category: 'プロジェクト',
      priority: 'high',
      status: 'unprocessed',
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      tags: ['UI', '瞑想アプリ', '改善'],
      aiSuggestion: 'プロジェクト「瞑想アプリ」のタスクとして追加することをお勧めします'
    },
    {
      id: '2',
      type: 'idea',
      title: 'AIペアプログラミング機能',
      content: 'ChatGPTとリアルタイムでコードを書けるペアプログラミング環境。音声でコード説明、AIが提案',
      category: '新機能',
      priority: 'medium',
      status: 'unprocessed',
      createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
      tags: ['AI', 'プログラミング', '新機能'],
      aiSuggestion: 'フィーチャーリクエストとして登録し、技術調査を開始しましょう'
    },
    {
      id: '3',
      type: 'task',
      title: 'Expo QRコードの表示改善',
      content: 'QRコードが小さくて読み取りにくい。サイズを大きくして、手動IP入力オプションも追加',
      category: 'バグ修正',
      priority: 'high',
      status: 'processing',
      createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
      tags: ['Expo', 'QR', 'モバイル'],
      aiSuggestion: '緊急度高：ExpoQRViewコンポーネントの修正が必要です'
    },
    {
      id: '4',
      type: 'note',
      title: 'Next.js 15の新機能メモ',
      content: 'Turbopack、React 19対応、新しいcaching戦略について調査した内容をまとめる',
      category: '学習',
      priority: 'low',
      status: 'processed',
      createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      tags: ['Next.js', '技術調査', '学習'],
      aiSuggestion: 'ナレッジベースに追加して、チーム共有しましょう'
    },
    {
      id: '5',
      type: 'link',
      title: 'shadcn/ui導入ガイド',
      content: 'https://ui.shadcn.com/docs/installation - DaisyUIからの移行検討材料',
      category: '技術調査',
      priority: 'medium',
      status: 'unprocessed',
      createdAt: new Date(Date.now() - 30 * 60 * 1000),
      tags: ['UI', 'ライブラリ', '移行'],
      aiSuggestion: 'UI_LIBRARY_COMPARISON.mdに追加情報として記録しましょう'
    }
  ]);

  const [filter, setFilter] = useState<'all' | InboxItem['status']>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isAddingItem, setIsAddingItem] = useState(false);
  const [newItem, setNewItem] = useState({
    type: 'note' as InboxItem['type'],
    title: '',
    content: '',
    category: '',
    priority: 'medium' as InboxItem['priority']
  });

  const filteredItems = items.filter(item => {
    const matchesFilter = filter === 'all' || item.status === filter;
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    
    return matchesFilter && matchesSearch && matchesCategory;
  });

  const categories = Array.from(new Set(items.map(item => item.category)));
  const statusCounts = {
    unprocessed: items.filter(i => i.status === 'unprocessed').length,
    processing: items.filter(i => i.status === 'processing').length,
    processed: items.filter(i => i.status === 'processed').length,
    archived: items.filter(i => i.status === 'archived').length,
    projext_generating: items.filter(i => i.status === 'projext_generating').length
  };

  const getTypeIcon = (type: InboxItem['type']) => {
    switch (type) {
      case 'voice': return <Mic size={16} className="text-info" />;
      case 'note': return <FileText size={16} className="text-primary" />;
      case 'idea': return <Lightbulb size={16} className="text-warning" />;
      case 'task': return <CheckSquare size={16} className="text-success" />;
      case 'link': return <ArrowRight size={16} className="text-secondary" />;
      case 'projext_request': return <Brain size={16} className="text-accent" />;
      default: return <FileText size={16} />;
    }
  };

  const getPriorityColor = (priority: InboxItem['priority']) => {
    switch (priority) {
      case 'high': return 'border-l-error bg-error/5';
      case 'medium': return 'border-l-warning bg-warning/5';
      case 'low': return 'border-l-info bg-info/5';
      default: return 'border-l-base-content/20';
    }
  };

  const getStatusBadge = (status: InboxItem['status']) => {
    switch (status) {
      case 'unprocessed': return 'badge-ghost';
      case 'processing': return 'badge-warning';
      case 'processed': return 'badge-success';
      case 'archived': return 'badge-neutral';
      case 'projext_generating': return 'badge-info animate-pulse';
      default: return 'badge-ghost';
    }
  };

  const updateItemStatus = (id: string, status: InboxItem['status']) => {
    setItems(prev => prev.map(item => 
      item.id === id ? { ...item, status } : item
    ));
  };

  const addNewItem = () => {
    if (!newItem.title.trim() || !newItem.content.trim()) return;

    const item: InboxItem = {
      id: Date.now().toString(),
      type: newItem.type,
      title: newItem.title,
      content: newItem.content,
      category: newItem.category || 'その他',
      priority: newItem.priority,
      status: 'unprocessed',
      createdAt: new Date(),
      tags: [],
      aiSuggestion: 'AI分析中...'
    };

    setItems(prev => [item, ...prev]);
    setNewItem({
      type: 'note',
      title: '',
      content: '',
      category: '',
      priority: 'medium'
    });
    setIsAddingItem(false);
  };

  const generateProjectFromIdea = async (item: InboxItem) => {
    try {
      updateItemStatus(item.id, 'projext_generating');
      
      // Projext生成ロジック
      const projextData = {
        name: item.title.replace(/のアイデア|案|要求/g, '').trim(),
        type: detectProjectType(item.content),
        vision: item.content,
        requirements: extractRequirements(item.content)
      };
      
      // アイテムにprojextDataを追加
      setItems(prev => prev.map(i => 
        i.id === item.id 
          ? { ...i, status: 'processed' as InboxItem['status'], projextData }
          : i
      ));
      
      // 成功通知
      console.log(`Projext "${projextData.name}" generated successfully!`);
      
    } catch (error) {
      console.error('Projext generation failed:', error);
      updateItemStatus(item.id, 'unprocessed');
    }
  };
  
  const detectProjectType = (content: string): string => {
    const lowerContent = content.toLowerCase();
    if (lowerContent.includes('アプリ') || lowerContent.includes('モバイル')) return 'mobile';
    if (lowerContent.includes('ai') || lowerContent.includes('bot') || lowerContent.includes('機械学習')) return 'ai';
    if (lowerContent.includes('web') || lowerContent.includes('サイト')) return 'web';
    if (lowerContent.includes('ゲーム')) return 'game';
    return 'general';
  };
  
  const extractRequirements = (content: string): string[] => {
    const requirements = [];
    if (content.includes('UI')) requirements.push('UI設計・実装');
    if (content.includes('カスタマイズ')) requirements.push('カスタマイゼーション機能');
    if (content.includes('タイマー')) requirements.push('タイマー機能');
    if (content.includes('音声')) requirements.push('音声機能');
    if (content.includes('リアルタイム')) requirements.push('リアルタイム処理');
    return requirements.length > 0 ? requirements : ['基本機能実装', 'UI設計', 'テスト実装'];
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 60) return `${diffMins}分前`;
    if (diffHours < 24) return `${diffHours}時間前`;
    return `${diffDays}日前`;
  };

  return (
    <div className="h-full flex flex-col neo-glass">
      {/* ヘッダー */}
      <div className="p-4 border-b border-base-content/10 bg-base-300/80">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold">📥 インボックス</h1>
            <p className="text-sm text-base-content/70">音声・ノート・アイデア・タスクの統合管理</p>
          </div>
          <button 
            className="btn btn-primary btn-sm neo-hover"
            onClick={() => setIsAddingItem(true)}
          >
            <Plus size={16} />
            新規追加
          </button>
        </div>

        {/* 統計バー */}
        <div className="grid grid-cols-4 gap-2 mb-4">
          {Object.entries(statusCounts).map(([status, count]) => (
            <div 
              key={status}
              className={`stats shadow-sm cursor-pointer transition-all hover:scale-105 ${
                filter === status ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => setFilter(status as InboxItem['status'])}
            >
              <div className="stat p-2">
                <div className="stat-title text-xs">
                  {status === 'unprocessed' ? '未処理' :
                   status === 'processing' ? '処理中' :
                   status === 'processed' ? '完了' : 'アーカイブ'}
                </div>
                <div className="stat-value text-sm">{count}</div>
              </div>
            </div>
          ))}
        </div>

        {/* 洗練された検索・フィルターシステム */}
        <div className="space-y-3">
          {/* メイン検索バー */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-base-content/50" />
            </div>
            <input
              type="text"
              placeholder="タイトル、内容、タグで検索... (Ctrl+K)"
              className="input input-bordered w-full pl-11 pr-4 bg-base-100/80 backdrop-blur-sm border-base-content/20 focus:border-primary focus:bg-base-100 transition-all duration-200"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Escape') setSearchQuery('');
              }}
            />
            {searchQuery && (
              <button
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-base-content/50 hover:text-base-content"
                onClick={() => setSearchQuery('')}
              >
                ✕
              </button>
            )}
          </div>

          {/* フィルタータグ */}
          <div className="flex flex-wrap gap-2 items-center">
            {/* ステータスフィルター */}
            <div className="flex gap-1">
              <button 
                className={`btn btn-xs ${filter === 'all' ? 'btn-primary' : 'btn-ghost'} rounded-full`}
                onClick={() => setFilter('all')}
              >
                全て
              </button>
              <button 
                className={`btn btn-xs ${filter === 'unprocessed' ? 'btn-warning' : 'btn-ghost'} rounded-full`}
                onClick={() => setFilter('unprocessed')}
              >
                未処理 {statusCounts.unprocessed > 0 && `(${statusCounts.unprocessed})`}
              </button>
              <button 
                className={`btn btn-xs ${filter === 'processing' ? 'btn-info' : 'btn-ghost'} rounded-full`}
                onClick={() => setFilter('processing')}
              >
                処理中 {statusCounts.processing > 0 && `(${statusCounts.processing})`}
              </button>
              <button 
                className={`btn btn-xs ${filter === 'processed' ? 'btn-success' : 'btn-ghost'} rounded-full`}
                onClick={() => setFilter('processed')}
              >
                完了 {statusCounts.processed > 0 && `(${statusCounts.processed})`}
              </button>
            </div>

            <div className="divider divider-horizontal"></div>

            {/* カテゴリフィルター */}
            <div className="dropdown">
              <div tabIndex={0} role="button" className="btn btn-ghost btn-xs rounded-full">
                <Filter size={14} />
                {selectedCategory === 'all' ? 'カテゴリ' : selectedCategory}
                {selectedCategory !== 'all' && (
                  <button 
                    className="ml-1 hover:bg-base-content/10 rounded-full w-4 h-4 flex items-center justify-center text-xs"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedCategory('all');
                    }}
                  >
                    ✕
                  </button>
                )}
              </div>
              <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52 border border-base-content/10">
                <li>
                  <a onClick={() => setSelectedCategory('all')} className={selectedCategory === 'all' ? 'active' : ''}>
                    📁 全カテゴリ
                  </a>
                </li>
                <div className="divider my-1"></div>
                {categories.map(category => (
                  <li key={category}>
                    <a 
                      onClick={() => setSelectedCategory(category)}
                      className={selectedCategory === category ? 'active' : ''}
                    >
                      📂 {category}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* 結果カウント */}
            {(searchQuery || selectedCategory !== 'all' || filter !== 'all') && (
              <>
                <div className="divider divider-horizontal"></div>
                <div className="text-xs text-base-content/60 bg-base-200/50 px-2 py-1 rounded-full">
                  {filteredItems.length}件見つかりました
                </div>
                <button 
                  className="btn btn-ghost btn-xs rounded-full text-base-content/60 hover:text-base-content"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('all');
                    setFilter('all');
                  }}
                >
                  リセット
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* アイテムリスト */}
      <div className="flex-1 overflow-y-auto p-4">
        {isAddingItem && (
          <div className="card bg-base-200 shadow-xl mb-4 neo-depth">
            <div className="card-body p-4">
              <h3 className="font-semibold mb-3">新しいアイテムを追加</h3>
              
              <div className="grid grid-cols-2 gap-3 mb-3">
                <select 
                  className="select select-bordered select-sm"
                  value={newItem.type}
                  onChange={(e) => setNewItem(prev => ({ ...prev, type: e.target.value as InboxItem['type'] }))}
                >
                  <option value="note">📝 ノート</option>
                  <option value="voice">🎤 音声</option>
                  <option value="idea">💡 アイデア</option>
                  <option value="task">✅ タスク</option>
                  <option value="link">🔗 リンク</option>
                  <option value="projext_request">🧠 Projext要求</option>
                </select>
                
                <select 
                  className="select select-bordered select-sm"
                  value={newItem.priority}
                  onChange={(e) => setNewItem(prev => ({ ...prev, priority: e.target.value as InboxItem['priority'] }))}
                >
                  <option value="high">🔴 高優先度</option>
                  <option value="medium">🟡 中優先度</option>
                  <option value="low">🔵 低優先度</option>
                </select>
              </div>

              <input
                type="text"
                placeholder="タイトル"
                className="input input-bordered w-full mb-3"
                value={newItem.title}
                onChange={(e) => setNewItem(prev => ({ ...prev, title: e.target.value }))}
              />

              <textarea
                placeholder="内容"
                className="textarea textarea-bordered w-full mb-3"
                rows={3}
                value={newItem.content}
                onChange={(e) => setNewItem(prev => ({ ...prev, content: e.target.value }))}
              />

              <input
                type="text"
                placeholder="カテゴリ（オプション）"
                className="input input-bordered w-full mb-3"
                value={newItem.category}
                onChange={(e) => setNewItem(prev => ({ ...prev, category: e.target.value }))}
              />

              <div className="flex gap-2">
                <button className="btn btn-primary btn-sm" onClick={addNewItem}>
                  追加
                </button>
                <button 
                  className="btn btn-ghost btn-sm" 
                  onClick={() => setIsAddingItem(false)}
                >
                  キャンセル
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-3">
          {filteredItems.map(item => (
            <div 
              key={item.id}
              className={`card bg-base-100 shadow-lg neo-depth border-l-4 ${getPriorityColor(item.priority)}`}
            >
              <div className="card-body p-4">
                {/* ヘッダー */}
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getTypeIcon(item.type)}
                    <h3 className="font-semibold text-sm">{item.title}</h3>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className={`badge ${getStatusBadge(item.status)} badge-sm`}>
                      {item.status === 'unprocessed' ? '未処理' :
                       item.status === 'processing' ? '処理中' :
                       item.status === 'processed' ? '完了' :
                       item.status === 'projext_generating' ? 'Projext生成中' : 'アーカイブ'}
                    </div>
                    <div className="text-xs text-base-content/60">
                      {formatTimeAgo(item.createdAt)}
                    </div>
                  </div>
                </div>

                {/* コンテンツ */}
                <p className="text-sm text-base-content/80 mb-3">{item.content}</p>

                {/* タグ */}
                {item.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-3">
                    {item.tags.map(tag => (
                      <span key={tag} className="badge badge-outline badge-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                )}

                {/* AI提案 */}
                {item.aiSuggestion && (
                  <div className="bg-info/10 rounded-lg p-2 mb-3">
                    <div className="flex items-center gap-2 text-xs text-info">
                      <div className="w-2 h-2 bg-info rounded-full animate-pulse"></div>
                      AI提案: {item.aiSuggestion}
                    </div>
                  </div>
                )}
                
                {/* Projextデータ表示 */}
                {item.projextData && (
                  <div className="bg-accent/10 rounded-lg p-3 mb-3 border border-accent/20">
                    <div className="flex items-center gap-2 mb-2">
                      <FolderPlus size={16} className="text-accent" />
                      <span className="text-sm font-semibold text-accent">生成されたProjectext</span>
                    </div>
                    <div className="space-y-1 text-xs">
                      <div><span className="font-medium">プロジェクト名:</span> {item.projextData.name}</div>
                      <div><span className="font-medium">タイプ:</span> {item.projextData.type}</div>
                      <div><span className="font-medium">要件:</span> {item.projextData.requirements.join(', ')}</div>
                    </div>
                  </div>
                )}

                {/* アクション */}
                <div className="flex items-center justify-between">
                  <div className="text-xs text-base-content/60">
                    📁 {item.category}
                  </div>
                  <div className="flex gap-1">
                    {item.status === 'unprocessed' && (
                      <>
                        <button 
                          className="btn btn-xs btn-warning"
                          onClick={() => updateItemStatus(item.id, 'processing')}
                        >
                          処理開始
                        </button>
                        {item.type === 'projext_request' && (
                          <button 
                            className="btn btn-xs btn-accent"
                            onClick={() => generateProjectFromIdea(item)}
                          >
                            <Sparkles size={12} />
                            Projext生成
                          </button>
                        )}
                      </>
                    )}
                    {item.status === 'processing' && (
                      <button 
                        className="btn btn-xs btn-success"
                        onClick={() => updateItemStatus(item.id, 'processed')}
                      >
                        完了
                      </button>
                    )}
                    {item.status === 'processed' && (
                      <button 
                        className="btn btn-xs btn-neutral"
                        onClick={() => updateItemStatus(item.id, 'archived')}
                      >
                        <Archive size={12} />
                        アーカイブ
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">📭</div>
            <div className="text-base-content/60">
              {searchQuery || selectedCategory !== 'all' || filter !== 'all' 
                ? 'フィルター条件に一致するアイテムがありません'
                : 'インボックスは空です'
              }
            </div>
          </div>
        )}
      </div>
    </div>
  );
}