'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>Text, <PERSON>, <PERSON><PERSON>s, BarChart3, <PERSON><PERSON>, Star, ArrowUpDown, Calendar, Bot, Copy, Trash2, Edit3, FolderOpen, GitBranch, Archive, FileCode, Search, Plus, X } from 'lucide-react';
import { useState, useEffect, memo, useCallback, useMemo } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useContextMenu, ContextMenuItem } from './ContextMenu';
import { useFileSystem } from '../hooks/useFileSystem';

interface SidebarProps {
  onDashboardClick?: () => void;
  onProjectClick?: (project: string) => void;
  onLauncherClick?: () => void;
  onSystemClick?: (system: string) => void;
  onFileClick?: (fileName: string, filePath?: string) => void;
  onHomeClick?: () => void;
  projectListVersion?: number;
  lastCreatedProject?: { name: string; template: any } | null;
  onProjectNameUpdate?: (oldName: string, newName: string) => void;
  projectNameUpdate?: {oldName: string, newName: string, timestamp: number} | null;
}

interface Project {
  id: string;
  name: string;
  type: 'mobile' | 'web' | 'ai' | 'game' | 'desktop';
  priority: 1 | 2 | 3 | 4 | 5;
  progress: number;
  updatedAt: Date;
  folder?: string;
}

export default function Sidebar({ onDashboardClick, onProjectClick, onLauncherClick, onSystemClick, onFileClick, onHomeClick, projectListVersion, lastCreatedProject, onProjectNameUpdate, projectNameUpdate }: SidebarProps) {
  const [activeProject, setActiveProject] = useState(null);
  const [activeSection, setActiveSection] = useState('projects');
  const [sortBy, setSortBy] = useState<'name' | 'priority' | 'progress' | 'updatedAt'>('priority');
  const [editingProject, setEditingProject] = useState<string | null>(null);
  const [collapsedSections, setCollapsedSections] = useState<Set<string>>(new Set());
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [showFileActions, setShowFileActions] = useState(false);
  const [newItemName, setNewItemName] = useState('');
  const [newItemType, setNewItemType] = useState<'file' | 'folder'>('file');
  const [editingFileName, setEditingFileName] = useState<string | null>(null);
  const [dragOverPath, setDragOverPath] = useState<string | null>(null);
  const [showDirectoryPicker, setShowDirectoryPicker] = useState(false);
  const [currentRootPath, setCurrentRootPath] = useState('/Users/<USER>/Dev/meta-studio');
  const router = useRouter();
  const { showContextMenu, ContextMenuComponent } = useContextMenu();
  const { 
    items: fileItems, 
    loading: filesLoading, 
    error: filesError, 
    navigateToPath, 
    currentPath,
    createFile,
    createFolder,
    deleteItem,
    renameItem,
    searchFiles,
    refreshCurrentDirectory
  } = useFileSystem('/', currentRootPath);

  // デフォルトプロジェクト（ハイドレーションエラー回避のため固定値）
  // ツリー構造の型定義
  interface TreeNode {
    name: string;
    path: string;
    type: 'file' | 'directory';
    size?: number;
    children?: TreeNode[];
    level: number;
  }

  // 展開されたディレクトリの子ファイルを管理する状態
  const [expandedDirectoryFiles, setExpandedDirectoryFiles] = useState<Map<string, any[]>>(new Map());

  // ディレクトリが展開されたときに子ファイルを取得
  const loadDirectoryFiles = async (directoryPath: string) => {
    try {
      console.log(`Loading directory files for: ${directoryPath}`);
      const response = await fetch(`/api/files?path=${encodeURIComponent(directoryPath)}`);
      const result = await response.json();
      console.log(`API response for ${directoryPath}:`, result);
      
      if (result.success !== false && result.items) {
        // APIレスポンスの構造に合わせて修正
        const files = result.items || [];
        console.log(`Setting ${files.length} files for ${directoryPath}:`, files);
        setExpandedDirectoryFiles(prev => {
          const newMap = new Map(prev);
          newMap.set(directoryPath, files);
          console.log('Updated expandedDirectoryFiles:', newMap);
          return newMap;
        });
      }
    } catch (error) {
      console.error('Failed to load directory files:', error);
    }
  };

  // ファイルリストからツリー構造を構築（メモ化で最適化・再帰対応）
  const buildFileTree = useCallback((files: any[]): TreeNode[] => {
    if (!files || !Array.isArray(files)) {
      console.error('buildFileTree: files is not an array', files);
      return [];
    }

    // 再帰的にツリーノードを構築
    const buildTreeNode = (item: any, level: number = 0): TreeNode => {
      const childFiles = item.type === 'directory' ? expandedDirectoryFiles.get(item.path) || [] : [];
      console.log(`Building tree for ${item.name} (level ${level}): ${childFiles.length} children`, childFiles);
      
      return {
        name: item.name,
        path: item.path,
        type: item.type,
        size: item.size,
        children: item.type === 'directory' ? childFiles.map(child => buildTreeNode(child, level + 1)) : undefined,
        level
      };
    };

    const tree: TreeNode[] = [];
    
    // ディレクトリを先に処理してツリー構造を作る
    const sortedFiles = [...files].sort((a, b) => {
      if (a.type === b.type) return a.name.localeCompare(b.name);
      return a.type === 'directory' ? -1 : 1;
    });

    sortedFiles.forEach(item => {
      tree.push(buildTreeNode(item));
    });

    return tree;
  }, [expandedDirectoryFiles]);

  // ツリーノードを再帰的にレンダリング
  const renderTreeNode = (node: TreeNode, depth: number = 0): JSX.Element => {
    const isExpanded = expandedFolders.has(node.path);
    const indent = depth * 16; // インデント幅
    
    return (
      <div key={node.path}>
        <div
          className={`flex items-center gap-1 p-1 rounded hover:bg-base-200/50 cursor-pointer group transition-colors ${
            dragOverPath === node.path && node.type === 'directory' ? 'bg-primary/20 border border-primary/50' : ''
          }`}
          style={{ paddingLeft: `${8 + indent}px` }}
          onClick={() => {
            if (node.type === 'directory') {
              // ディレクトリの展開/折りたたみのみ（移動しない）
              if (isExpanded) {
                setExpandedFolders(prev => {
                  const newSet = new Set(prev);
                  newSet.delete(node.path);
                  return newSet;
                });
              } else {
                setExpandedFolders(prev => new Set([...prev, node.path]));
                // ディレクトリ内容を読み込む（移動はしない）
                loadDirectoryFiles(node.path);
              }
            } else {
              onFileClick?.(node.name, node.path);
            }
          }}
          onContextMenu={(e) => showContextMenu(e, getFileContextMenu(node.path, node.name, node.type === 'directory'))}
          draggable
          onDragStart={(e) => {
            e.dataTransfer.setData('text/plain', JSON.stringify({
              type: 'file',
              path: node.path,
              name: node.name,
              isDirectory: node.type === 'directory'
            }));
          }}
          onDragOver={(e) => node.type === 'directory' ? handleDragOver(e, node.path) : undefined}
          onDragLeave={node.type === 'directory' ? handleDragLeave : undefined}
          onDrop={(e) => node.type === 'directory' ? handleDrop(e, node.path) : undefined}
        >
          {node.type === 'directory' && (
            <span className={`transform transition-transform ${isExpanded ? 'rotate-90' : ''}`}>
              ▶
            </span>
          )}
          {getFileIcon(node.name, node.type === 'directory')}
          <span className="font-medium flex-1 truncate" title={node.name}>
            {node.name}
          </span>
          {node.size && (
            <span className="text-xs text-base-content/50 opacity-0 group-hover:opacity-100 transition-opacity">
              {node.size < 1024 ? `${node.size}B` : 
               node.size < 1024 * 1024 ? `${Math.round(node.size / 1024)}KB` :
               `${Math.round(node.size / (1024 * 1024))}MB`}
            </span>
          )}
        </div>
        
        {/* 子ノードを表示（ディレクトリが展開されている場合のみ） */}
        {node.type === 'directory' && isExpanded && node.children && (
          <div>
            {console.log(`Rendering ${node.children.length} children for ${node.name}:`, node.children)}
            {node.children.map(child => renderTreeNode(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  const getDefaultProjects = (): Project[] => [
    {
      id: '1',
      name: '瞑想アプリ_projext',
      type: 'mobile',
      priority: 5,
      progress: 75,
      updatedAt: new Date('2024-01-01T10:00:00Z'), // 固定日時
      folder: '/Users/<USER>/Dev/meditation-app'
    },
    {
      id: '2',
      name: '投資bot_projext',
      type: 'ai',
      priority: 4,
      progress: 90,
      updatedAt: new Date('2024-01-01T08:00:00Z'), // 固定日時
      folder: '/Users/<USER>/Dev/investment-bot'
    },
    {
      id: '3',
      name: 'iS_streamer_projext',
      type: 'web',
      priority: 3,
      progress: 25,
      updatedAt: new Date('2023-12-31T10:00:00Z'), // 固定日時
      folder: '/Users/<USER>/Dev/is-streamer'
    }
  ];

  const [projects, setProjects] = useState<Project[]>(getDefaultProjects());
  const [isProjectsLoaded, setIsProjectsLoaded] = useState(false);

  const getTypeColor = (type: Project['type']) => {
    switch (type) {
      case 'mobile': return 'text-primary';
      case 'ai': return 'text-secondary';
      case 'web': return 'text-accent';
      case 'game': return 'text-error';
      case 'desktop': return 'text-warning';
      default: return 'text-neutral';
    }
  };

  const getTypeGradient = (type: Project['type']) => {
    switch (type) {
      case 'mobile': return 'bg-gradient-to-r from-primary/20 to-primary/5';
      case 'ai': return 'bg-gradient-to-r from-secondary/20 to-secondary/5';
      case 'web': return 'bg-gradient-to-r from-accent/20 to-accent/5';
      case 'game': return 'bg-gradient-to-r from-error/20 to-error/5';
      case 'desktop': return 'bg-gradient-to-r from-warning/20 to-warning/5';
      default: return 'bg-gradient-to-r from-neutral/20 to-neutral/5';
    }
  };

  const renderStars = (priority: number, projectId: string) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map(star => (
          <Star
            key={star}
            size={10}
            className={`cursor-pointer transition-colors ${star <= priority ? 'text-yellow-400 fill-current' : 'text-base-content/20 hover:text-yellow-300'}`}
            onClick={(e) => {
              e.stopPropagation();
              handlePriorityChange(projectId, star);
            }}
          />
        ))}
      </div>
    );
  };

  const formatTimeAgo = (date: Date) => {
    if (typeof window === 'undefined') {
      // サーバーサイドでは固定値を返す
      return '更新済み';
    }
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 60) return `${diffMins}分前`;
    if (diffHours < 24) return `${diffHours}時間前`;
    return `${diffDays}日前`;
  };

  // ローカルストレージからプロジェクトを読み込み（ハイドレーション後）
  useEffect(() => {
    if (typeof window !== 'undefined' && !isProjectsLoaded) {
      try {
        const saved = localStorage.getItem('meta-studio-projects');
        if (saved) {
          const parsedProjects = JSON.parse(saved);
          // Date オブジェクトを復元
          const restoredProjects = parsedProjects.map((p: any) => ({
            ...p,
            updatedAt: new Date(p.updatedAt)
          }));
          setProjects(restoredProjects);
        }
      } catch (error) {
        console.error('Failed to load projects from localStorage:', error);
      }
      setIsProjectsLoaded(true);
    }
  }, [isProjectsLoaded]);

  // useMemoでソート処理を最適化
  const sortedProjects = useMemo(() => {
    return [...projects].sort((a, b) => {
      switch (sortBy) {
        case 'name': return a.name.localeCompare(b.name);
        case 'priority': return b.priority - a.priority;
        case 'progress': return b.progress - a.progress;
        case 'updatedAt': return b.updatedAt.getTime() - a.updatedAt.getTime();
        default: return 0;
      }
    });
  }, [projects, sortBy]);

  const handleProjectNameEdit = useCallback((projectId: string, newName: string) => {
    setProjects(prev => prev.map(p => 
      p.id === projectId ? { ...p, name: newName } : p
    ));
    setEditingProject(null);
  }, []);

  const handlePriorityChange = useCallback((projectId: string, newPriority: number) => {
    setProjects(prev => prev.map(p => 
      p.id === projectId ? { ...p, priority: newPriority as 1 | 2 | 3 | 4 | 5 } : p
    ));
  }, []);

  const handleDeleteProject = useCallback((projectId: string) => {
    if (confirm('このプロジェクトを削除しますか？')) {
      setProjects(prev => prev.filter(p => p.id !== projectId));
    }
  }, []);

  const handleDuplicateProject = useCallback((project: Project) => {
    const newProject: Project = {
      ...project,
      id: Date.now().toString(),
      name: `${project.name}_copy`,
      progress: 0,
      updatedAt: new Date()
    };
    setProjects(prev => [newProject, ...prev]);
  }, []);

  const handleArchiveProject = useCallback((projectId: string) => {
    const project = projects.find(p => p.id === projectId);
    if (project && confirm(`プロジェクト "${project.name}" をアーカイブしますか？\nアーカイブされたプロジェクトは一覧から非表示になります。`)) {
      setProjects(prev => prev.map(p => 
        p.id === projectId 
          ? { ...p, archived: true, updatedAt: new Date() }
          : p
      ));
      alert(`プロジェクト "${project.name}" をアーカイブしました。`);
    }
  }, [projects]);

  const getProjectContextMenu = (project: Project): ContextMenuItem[] => [
    {
      id: 'open',
      label: 'プロジェクトを開く',
      icon: '📂',
      action: () => {
        setActiveProject(project.name);
        onProjectClick?.(project.name);
      }
    },
    {
      id: 'open-folder',
      label: 'フォルダで開く',
      icon: '📁',
      action: () => {
        // フォルダを開く
        const folderPath = project.folder || `/Users/<USER>/Dev/meta-studio/${project.name}`;
        if (confirm(`フォルダを開きますか？\nパス: ${folderPath}`)) {
          // ファイルエクスプローラーをタブで開く
          onSystemClick?.('ファイルエクスプローラー');
          alert(`フォルダを開きました: ${folderPath}`);
        }
      }
    },
    { id: 'separator1', label: '', separator: true, action: () => {} },
    {
      id: 'rename',
      label: '名前を変更',
      icon: '✏️',
      action: () => setEditingProject(project.id)
    },
    {
      id: 'duplicate',
      label: '複製',
      icon: '📄',
      action: () => handleDuplicateProject(project)
    },
    { id: 'separator2', label: '', separator: true, action: () => {} },
    {
      id: 'git',
      label: 'Git操作',
      icon: '🌿',
      submenu: [
        {
          id: 'git-status',
          label: 'ステータス確認',
          icon: '📊',
          action: () => {
            // Git ステータスを確認
            const projectPath = project.folder || `/Users/<USER>/Dev/meta-studio`;
            alert(`Gitステータス確認中...\nプロジェクト: ${project.name}\nパス: ${projectPath}\n\n※ターミナルで詳細確認してください`);
          }
        },
        {
          id: 'git-commit',
          label: 'コミット',
          icon: '💾',
          action: () => {
            const message = prompt('コミットメッセージを入力してください:', `update: ${project.name}の変更`);
            if (message) {
              alert(`コミット作成中...\nメッセージ: ${message}\nプロジェクト: ${project.name}\n\n※ターミナルで実行状況を確認してください`);
            }
          }
        },
        {
          id: 'git-push',
          label: 'プッシュ',
          icon: '⬆️',
          action: () => {
            if (confirm(`${project.name} の変更をリモートリポジトリにプッシュしますか？`)) {
              alert(`プッシュ実行中...\nプロジェクト: ${project.name}\n\n※ターミナルで実行状況を確認してください`);
            }
          }
        }
      ]
    },
    {
      id: 'archive',
      label: 'アーカイブ',
      icon: '📦',
      action: () => handleArchiveProject(project.id)
    },
    { id: 'separator3', label: '', separator: true, action: () => {} },
    {
      id: 'delete',
      label: '削除',
      icon: '🗑️',
      action: () => handleDeleteProject(project.id),
      danger: true
    }
  ];

  const getFileIcon = (fileName: string, isDirectory: boolean) => {
    if (isDirectory) {
      return <Folder size={12} className="text-blue-400" />;
    }
    
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'md':
        return <FileText size={12} className="text-blue-500" />;
      case 'ts':
      case 'tsx':
        return <FileCode size={12} className="text-blue-600" />;
      case 'js':
      case 'jsx':
        return <FileCode size={12} className="text-yellow-500" />;
      case 'json':
        return <FileCode size={12} className="text-green-500" />;
      case 'yaml':
      case 'yml':
        return <FileCode size={12} className="text-orange-500" />;
      case 'css':
        return <FileCode size={12} className="text-purple-500" />;
      case 'html':
        return <FileCode size={12} className="text-red-500" />;
      case 'py':
        return <FileCode size={12} className="text-green-600" />;
      case 'java':
        return <FileCode size={12} className="text-red-600" />;
      case 'cpp':
      case 'c':
        return <FileCode size={12} className="text-blue-700" />;
      case 'php':
        return <FileCode size={12} className="text-purple-600" />;
      case 'svg':
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
        return <FileText size={12} className="text-pink-500" />;
      case 'pdf':
        return <FileText size={12} className="text-red-400" />;
      case 'txt':
        return <FileText size={12} className="text-gray-600" />;
      default:
        return <FileText size={12} className="text-gray-500" />;
    }
  };

  // ファイル操作関数
  const handleCreateItem = async () => {
    if (!newItemName.trim()) return;
    
    const success = newItemType === 'file' 
      ? await createFile(newItemName)
      : await createFolder(newItemName);
      
    if (success) {
      setNewItemName('');
      setShowFileActions(false);
    }
  };

  const handleDeleteFile = async (filePath: string, fileName: string) => {
    if (confirm(`"${fileName}"を削除しますか？この操作は取り消せません。`)) {
      await deleteItem(filePath);
    }
  };

  const handleRenameFile = async (oldPath: string, oldName: string) => {
    const newName = prompt('新しい名前を入力してください:', oldName);
    if (newName && newName !== oldName) {
      await renameItem(oldPath, newName);
    }
  };

  // 検索されたファイルリスト
  const filteredFiles = useMemo(() => {
    return searchQuery ? searchFiles(searchQuery) : fileItems;
  }, [searchQuery, fileItems, searchFiles]);

  // ツリー構造を構築（展開状態の変更を反映）
  const fileTree = useMemo(() => {
    return buildFileTree(filteredFiles);
  }, [buildFileTree, filteredFiles]);

  // ドラッグ&ドロップ処理
  const handleDragOver = (e: React.DragEvent, targetPath: string) => {
    e.preventDefault();
    setDragOverPath(targetPath);
  };

  const handleDragLeave = () => {
    setDragOverPath(null);
  };

  const handleDrop = async (e: React.DragEvent, targetPath: string) => {
    e.preventDefault();
    setDragOverPath(null);
    
    try {
      const data = JSON.parse(e.dataTransfer.getData('text/plain'));
      if (data.type === 'file' && data.path !== targetPath) {
        // ファイル/フォルダ移動の実装（今後拡張可能）
        alert(`ファイル移動機能: ${data.name} → ${targetPath}\n\n※この機能は今後実装予定です`);
      }
    } catch (error) {
      console.error('Drop error:', error);
    }
  };

  // 状態の永続化
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // 展開フォルダ状態を保存
      localStorage.setItem('file-explorer-expanded-folders', JSON.stringify([...expandedFolders]));
      // 折りたたみセクション状態を保存
      localStorage.setItem('file-explorer-collapsed-sections', JSON.stringify([...collapsedSections]));
    }
  }, [expandedFolders, collapsedSections]);

  // 状態の復元
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedExpandedFolders = localStorage.getItem('file-explorer-expanded-folders');
        if (savedExpandedFolders) {
          setExpandedFolders(new Set(JSON.parse(savedExpandedFolders)));
        }
        
        const savedCollapsedSections = localStorage.getItem('file-explorer-collapsed-sections');
        if (savedCollapsedSections) {
          setCollapsedSections(new Set(JSON.parse(savedCollapsedSections)));
        }
      } catch (error) {
        console.error('Failed to restore file explorer state:', error);
      }
    }
  }, []);

  const getFileContextMenu = (fileName: string, isFolder: boolean): ContextMenuItem[] => [
    {
      id: 'open',
      label: isFolder ? 'フォルダを開く' : 'ファイルを開く',
      icon: isFolder ? '📂' : '📄',
      action: () => {
        if (isFolder) {
          onSystemClick?.('ファイルエクスプローラー');
        } else {
          onProjectClick?.(fileName);
        }
      }
    },
    {
      id: 'reveal',
      label: 'ファインダーで表示',
      icon: '👁️',
      action: () => {
        const filePath = `/Users/<USER>/Dev/meta-studio/${fileName}`;
        alert(`ファインダーで表示:\n${filePath}\n\n※システムのファインダーで確認してください`);
      }
    },
    { id: 'separator1', label: '', separator: true, action: () => {} },
    {
      id: 'copy-path',
      label: 'パスをコピー',
      icon: '📋',
      action: () => {
        navigator.clipboard.writeText(`/Users/<USER>/Dev/meta-studio/${fileName}`);
      }
    },
    { id: 'separator2', label: '', separator: true, action: () => {} },
    {
      id: 'rename',
      label: '名前を変更',
      icon: '✏️',
      action: () => handleRenameFile(fileName, fileName)
    },
    ...(isFolder ? [
      {
        id: 'new-file',
        label: '新しいファイル',
        icon: '📄',
        action: () => {
          setNewItemType('file');
          setShowFileActions(true);
        }
      },
      {
        id: 'new-folder',
        label: '新しいフォルダ',
        icon: '📁',
        action: () => {
          setNewItemType('folder');
          setShowFileActions(true);
        }
      }
    ] : []),
    { id: 'separator3', label: '', separator: true, action: () => {} },
    {
      id: 'delete',
      label: '削除',
      icon: '🗑️',
      action: () => {
        handleDeleteFile(fileName, fileName)
      },
      danger: true
    }
  ];

  const toggleSection = (sectionName: string) => {
    const newCollapsed = new Set(collapsedSections);
    if (newCollapsed.has(sectionName)) {
      newCollapsed.delete(sectionName);
    } else {
      newCollapsed.add(sectionName);
    }
    setCollapsedSections(newCollapsed);
  };

  const toggleFolderExpansion = (folderName: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderName)) {
      newExpanded.delete(folderName);
    } else {
      newExpanded.add(folderName);
    }
    setExpandedFolders(newExpanded);
  };

  // プロジェクト名更新の反映
  const updateProjectName = (oldName: string, newName: string) => {
    console.log('Sidebar: updateProjectName called', { oldName, newName });
    setProjects(prev => {
      const updated = prev.map(project => 
        project.name === oldName 
          ? { ...project, name: newName, updatedAt: new Date() }
          : project
      );
      console.log('Sidebar: Updated projects list', updated);
      
      // ローカルストレージに保存
      localStorage.setItem('meta-studio-projects', JSON.stringify(updated));
      
      return updated;
    });
  };

  // プロジェクト名更新の監視
  useEffect(() => {
    if (projectNameUpdate) {
      console.log('Sidebar: projectNameUpdate received', projectNameUpdate);
      updateProjectName(projectNameUpdate.oldName, projectNameUpdate.newName);
    }
  }, [projectNameUpdate]);

  // プロジェクトリスト更新の監視
  useEffect(() => {
    console.log('Sidebar: useEffect triggered', { projectListVersion, lastCreatedProject });
    
    // projectListVersionが更新され、かつlastCreatedProjectが存在する場合のみ処理
    if (lastCreatedProject && projectListVersion > 0) {
      console.log('Creating new project in sidebar:', lastCreatedProject);
      
      // 新しいプロジェクトをリストに追加
      const newProject: Project = {
        id: `project-${Date.now()}`,
        name: lastCreatedProject.name,
        type: (lastCreatedProject.template?.type as Project['type']) || 'web',
        priority: 4,
        progress: 5,
        updatedAt: new Date(),
        folder: `/Users/<USER>/Dev/meta-studio/${lastCreatedProject.name.toLowerCase().replace(/\s+/g, '-')}`
      };
      
      console.log('New project object:', newProject);
      
      // 必ず新しいプロジェクトを追加（重複チェックを緩和）
      setProjects(prev => {
        // 同じ名前のプロジェクトが既に存在する場合は、更新
        const existingIndex = prev.findIndex(p => p.name === newProject.name);
        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = { ...updated[existingIndex], updatedAt: new Date() };
          console.log('Updated existing project:', updated[existingIndex].name);
          return updated;
        } else {
          const updatedProjects = [newProject, ...prev];
          console.log('Added new project. Total projects:', updatedProjects.length);
          return updatedProjects;
        }
      });
      
      // 新しく作成されたプロジェクトをアクティブにする
      setTimeout(() => {
        setActiveProject(lastCreatedProject.name);
      }, 100);
    }
  }, [projectListVersion, lastCreatedProject]);
  return (
    <div className="h-full flex flex-col p-4 neo-glass">
      {/* メタスタジオアイコン & タイトル */}
      <div className="mb-6">
        <div 
          className="flex items-center gap-3 mb-2 p-3 rounded-lg neo-depth cursor-pointer hover:neo-hover transition-all"
          onClick={onHomeClick}
          title="ダッシュボードに戻る"
        >
          <div className="relative w-10 h-10">
            <Image
              src="/meta-studio-logo.png"
              alt="Meta Studio Logo"
              width={40}
              height={40}
              className="rounded-full"
            />
          </div>
          <div>
            <h1 className="text-lg font-bold">メタスタジオ</h1>
            <p className="text-xs text-base-content/70">
              脳内現実化ツール
            </p>
          </div>
        </div>
      </div>

      {/* プロジェクト管理エリア */}
      <div className="flex-1">
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <h2 
              className="text-sm font-semibold text-base-content/80 cursor-pointer hover:text-primary transition-colors flex items-center gap-1"
              onClick={() => toggleSection('projects')}
            >
              <span className={`transform transition-transform ${collapsedSections.has('projects') ? '-rotate-90' : ''}`}>
                ▼
              </span>
              プロジェクト
            </h2>
            <div className="flex items-center gap-1">
              <div className="dropdown dropdown-end">
                <div tabIndex={0} role="button" className="btn btn-xs btn-ghost" title="ソート変更">
                  <ArrowUpDown size={12} />
                  <span className="text-xs">
                    {sortBy === 'priority' ? '★' : sortBy === 'updatedAt' ? '🕒' : sortBy === 'progress' ? '📊' : '🔤'}
                  </span>
                </div>
                <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-40">
                  <li>
                    <button onClick={() => setSortBy('priority')} className={sortBy === 'priority' ? 'active' : ''}>
                      ⭐ 重要度順
                    </button>
                  </li>
                  <li>
                    <button onClick={() => setSortBy('updatedAt')} className={sortBy === 'updatedAt' ? 'active' : ''}>
                      🕒 更新日順
                    </button>
                  </li>
                  <li>
                    <button onClick={() => setSortBy('progress')} className={sortBy === 'progress' ? 'active' : ''}>
                      📊 進捗順
                    </button>
                  </li>
                  <li>
                    <button onClick={() => setSortBy('name')} className={sortBy === 'name' ? 'active' : ''}>
                      🔤 名前順
                    </button>
                  </li>
                </ul>
              </div>
              <button
                className="btn btn-xs btn-ghost text-primary neo-hover"
                onClick={() => onSystemClick?.('管理')}
                title="管理ダッシュボード（旧ホーム）"
              >
                管理
              </button>
            </div>
          </div>
          {!collapsedSections.has('projects') && (
            <div className="space-y-1">
              {sortedProjects.map(project => (
              <div 
                key={project.id}
                className={`flex flex-col gap-1 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 ${getTypeGradient(project.type)} ${
                  activeProject === project.name ? 'ring-1 ring-primary' : 'hover:bg-base-300'
                }`}
                onClick={() => {
                  console.log('Project clicked:', project.name);
                  setActiveProject(project.name);
                  onProjectClick?.(project.name);
                }}
                onContextMenu={(e) => {
                  console.log('Project context menu triggered for:', project.name);
                  showContextMenu(e, getProjectContextMenu(project));
                }}
              >
                <div className="flex items-center gap-2">
                  <Folder size={16} className={getTypeColor(project.type)} />
                  {editingProject === project.id ? (
                    <input
                      type="text"
                      className="input input-xs bg-transparent border-none p-0 text-sm font-medium flex-1"
                      defaultValue={project.name}
                      onBlur={(e) => handleProjectNameEdit(project.id, e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleProjectNameEdit(project.id, e.currentTarget.value);
                        }
                        if (e.key === 'Escape') {
                          setEditingProject(null);
                        }
                      }}
                      autoFocus
                    />
                  ) : (
                    <span 
                      className="text-sm font-medium flex-1 truncate"
                      onDoubleClick={() => setEditingProject(project.id)}
                      title={project.name}
                    >
                      {project.name}
                    </span>
                  )}
                  {renderStars(project.priority, project.id)}
                </div>
                
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-1">
                    <span className={`badge badge-xs ${getTypeColor(project.type).replace('text-', 'badge-')}`}>
                      {project.type}
                    </span>
                    <span 
                      className="text-base-content/60" 
                      title={project.updatedAt.toLocaleString()}
                      suppressHydrationWarning={true}
                    >
                      {formatTimeAgo(project.updatedAt)}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <div className="flex-1 bg-base-content/20 rounded-full h-1">
                    <div 
                      className={`h-1 rounded-full ${getTypeColor(project.type).replace('text-', 'bg-')}`}
                      style={{ width: `${project.progress}%` }}
                    />
                  </div>
                  <span className="text-xs text-base-content/60 w-8 text-right">
                    {project.progress}%
                  </span>
                </div>
                
                {project.folder && (
                  <div className="text-xs text-base-content/50 truncate" title={project.folder}>
                    📁 {project.folder}
                  </div>
                )}
              </div>
              ))}
            </div>
          )}
        </div>

        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <h2 
              className="text-sm font-semibold text-base-content/80 cursor-pointer hover:text-primary transition-colors flex items-center gap-1"
              onClick={() => toggleSection('files')}
            >
              <span className={`transform transition-transform ${collapsedSections.has('files') ? '-rotate-90' : ''}`}>
                ▼
              </span>
              ファイルエクスプローラー
            </h2>
            <div className="flex items-center gap-1">
              <button
                className="btn btn-xs btn-ghost text-success"
                onClick={() => setShowFileActions(!showFileActions)}
                title="新規作成"
              >
                <Plus size={12} />
              </button>
              <button
                className="btn btn-xs btn-ghost text-neutral"
                onClick={() => refreshCurrentDirectory()}
                title="更新"
              >
                🔄
              </button>
            </div>
          </div>
          
          {/* 検索バー */}
          {!collapsedSections.has('files') && (
            <div className="mb-2">
              <div className="relative">
                <input
                  type="text"
                  placeholder="ファイル検索..."
                  className="input input-xs w-full bg-base-200 pl-6 pr-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Search size={10} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-base-content/50" />
                {searchQuery && (
                  <button
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-base-content/50 hover:text-base-content"
                    onClick={() => setSearchQuery('')}
                  >
                    <X size={10} />
                  </button>
                )}
              </div>
            </div>
          )}
          
          {/* 新規作成フォーム */}
          {showFileActions && !collapsedSections.has('files') && (
            <div className="mb-2 p-2 bg-base-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <select 
                  className="select select-xs w-16"
                  value={newItemType}
                  onChange={(e) => setNewItemType(e.target.value as 'file' | 'folder')}
                >
                  <option value="file">📄</option>
                  <option value="folder">📁</option>
                </select>
                <input
                  type="text"
                  placeholder={`${newItemType === 'file' ? 'ファイル' : 'フォルダ'}名...`}
                  className="input input-xs flex-1"
                  value={newItemName}
                  onChange={(e) => setNewItemName(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') handleCreateItem();
                    if (e.key === 'Escape') setShowFileActions(false);
                  }}
                  autoFocus
                />
              </div>
              <div className="flex justify-end gap-1">
                <button
                  className="btn btn-xs btn-ghost"
                  onClick={() => setShowFileActions(false)}
                >
                  キャンセル
                </button>
                <button
                  className="btn btn-xs btn-primary"
                  onClick={handleCreateItem}
                  disabled={!newItemName.trim()}
                >
                  作成
                </button>
              </div>
            </div>
          )}
          
          {/* 現在のパス表示 */}
          {!collapsedSections.has('files') && (
            <div className="mb-2">
              <div className="text-xs text-base-content/60 truncate" title={currentPath}>
                📂 {currentPath || '/'}
              </div>
              <div className="flex items-center justify-between mt-1">
                <div className="text-xs text-base-content/40 truncate mr-2" title={currentRootPath}>
                  📁 Root: {currentRootPath}
                </div>
                <button 
                  className="btn btn-xs btn-ghost text-info cursor-pointer hover:bg-base-200/50"
                  onClick={async () => {
                  try {
                    // File System Access APIを使用してディレクトリ選択
                    if ('showDirectoryPicker' in window) {
                      const dirHandle = await (window as any).showDirectoryPicker();
                      // ディレクトリパスを取得（制限がある場合はフォールバック）
                      alert(`選択されたディレクトリ: ${dirHandle.name}\n\n※ブラウザのセキュリティ制限により、\nフルパスの取得ができません。\n現在は手動入力をお使いください。`);
                      
                      // 手動入力フォールバック
                      const customPath = prompt('ディレクトリの完全パスを入力してください:', currentRootPath);
                      if (customPath && customPath !== currentRootPath) {
                        setCurrentRootPath(customPath);
                        navigateToPath('/');
                      }
                    } else {
                      // File System Access API未対応の場合
                      const customPath = prompt('ディレクトリパスを入力してください:', currentRootPath);
                      if (customPath && customPath !== currentRootPath) {
                        setCurrentRootPath(customPath);
                        navigateToPath('/');
                      }
                    }
                  } catch (error) {
                    // ユーザーがキャンセルした場合など
                    console.log('ディレクトリ選択がキャンセルされました');
                  }
                }}
                title="Finderでディレクトリを選択"
              >
                ディレクトリ選択
              </button>
            </div>
          )}
          
          {/* エラー表示 */}
          {filesError && !collapsedSections.has('files') && (
            <div className="text-xs text-error mb-2">
              エラー: {filesError}
            </div>
          )}
          
          {/* ローディング表示 */}
          {filesLoading && !collapsedSections.has('files') && (
            <div className="text-xs text-base-content/60 mb-2">
              読み込み中...
            </div>
          )}
          
          {/* 動的ファイルツリー */}
          {!collapsedSections.has('files') && !filesLoading && (
            <div className="space-y-1 text-xs max-h-80 overflow-y-auto">
              {filteredFiles.length === 0 ? (
                <div className="text-base-content/60 text-center py-4">
                  {searchQuery ? '検索結果がありません' : 'ファイルがありません'}
                </div>
              ) : (
                // ツリー構造表示
                fileTree.map(node => renderTreeNode(node, 0))
              )}
            </div>
          )}
          
          {/* ノート管理はシステムセクションに移動 */}
        </div>

        <div className="mb-4">
          <h2 
            className="text-sm font-semibold mb-2 text-base-content/80 cursor-pointer hover:text-primary transition-colors flex items-center gap-1"
            onClick={() => toggleSection('apps')}
          >
            <span className={`transform transition-transform ${collapsedSections.has('apps') ? '-rotate-90' : ''}`}>
              ▼
            </span>
            アプリドック
          </h2>
          {!collapsedSections.has('apps') && (
            <div className="space-y-1">
              <div 
                className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
                onClick={() => onLauncherClick?.()}
              >
                <Folder size={16} className="text-primary brightness-110" />
                <span className="text-sm font-medium">アプリドック</span>
              </div>
              <div 
                className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
                onClick={() => onSystemClick?.('エージェント管理')}
              >
                <Bot size={16} className="text-secondary brightness-125" />
                <span className="text-sm font-medium">エージェント管理</span>
              </div>
              <div 
                className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
                onClick={() => onSystemClick?.('プラグイン')}
              >
                <Wrench size={16} className="text-warning brightness-125" />
                <span className="text-sm font-medium">プラグイン</span>
              </div>
              <div 
                className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
                onClick={() => onSystemClick?.('統計')}
              >
                <BarChart3 size={16} className="text-accent brightness-125" />
                <span className="text-sm font-medium">統計</span>
              </div>
              <div 
                className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
                onClick={() => onSystemClick?.('Expo QR')}
              >
                <Wrench size={16} className="text-success brightness-125" />
                <span className="text-sm font-medium">モバイル</span>
              </div>
            </div>
          )}
        </div>

        <div className="mb-4">
          <h2 
            className="text-sm font-semibold mb-2 text-base-content/80 cursor-pointer hover:text-primary transition-colors flex items-center gap-1"
            onClick={() => toggleSection('system')}
          >
            <span className={`transform transition-transform ${collapsedSections.has('system') ? '-rotate-90' : ''}`}>
              ▼
            </span>
            システムツール
          </h2>
          {!collapsedSections.has('system') && (
            <div className="space-y-1">
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('エディター')}
            >
              <FileText size={16} className="text-info" />
              <span className="text-sm font-medium">エディター</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('インボックス')}
            >
              <FileText size={16} className="text-success" />
              <span className="text-sm font-medium">インボックス</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('AIキャラクター')}
            >
              <Bot size={16} className="text-primary" />
              <span className="text-sm font-medium">AIキャラクター</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('ブラウザ自動化')}
            >
              <FileText size={16} className="text-info" />
              <span className="text-sm font-medium">ブラウザ自動化</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('対談スタジオ')}
            >
              <FileText size={16} className="text-accent" />
              <span className="text-sm font-medium">対談スタジオ</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('バックアップ')}
            >
              <FileText size={16} className="text-warning" />
              <span className="text-sm font-medium">バックアップ</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('タスクマネージャー')}
            >
              <Calendar size={16} className="text-secondary brightness-125" />
              <span className="text-sm font-medium">タスクマネージャー</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('システム概要')}
            >
              <BarChart3 size={16} className="text-primary brightness-150" />
              <span className="text-sm font-medium">システム概要</span>
            </div>
            </div>
          )}
        </div>
      </div>

      {/* 設定ボタンを最下部に右寄せで配置 */}
      <div className="border-t pt-4 pb-6">
        <div className="flex justify-end">
          <div 
            className="flex items-center gap-2 px-3 py-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
            onClick={() => onSystemClick?.('設定')}
            title="設定"
          >
            <Wrench size={16} className="text-info brightness-150" />
            <span className="text-sm font-medium">設定</span>
          </div>
        </div>
      </div>
      <ContextMenuComponent />
    </div>
  );
}