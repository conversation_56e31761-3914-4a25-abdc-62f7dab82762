'use client';

import { Plus, Folder } from 'lucide-react';
import { useState } from 'react';
import ProjectTemplateModal from './ProjectTemplateModal';

interface LauncherViewProps {
  onProjectClick?: (projectId: string, projectTitle: string) => void;
  onProjectCreate?: (template: any, projectName: string) => void;
}

export default function LauncherView({ onProjectClick, onProjectCreate }: LauncherViewProps) {
  const [showTemplateModal, setShowTemplateModal] = useState(false);

  // プロジェクトデータ（実際のLauncherコンポーネントのデータ）
  const projects = [
    {
      id: 'meditation',
      title: '瞑想アプリ',
      description: '心を落ち着かせる瞑想タイマー',
      status: 'active',
      progress: 65,
      color: 'primary',
      borderColorClass: 'border-primary',
      bgGradientClasses: 'from-primary to-primary/60',
      icon: '🧘'
    },
    {
      id: 'investbot',
      title: '投資bot',
      description: 'AI駆動の自動投資システム',
      status: 'active',
      progress: 80,
      color: 'secondary',
      borderColorClass: 'border-secondary',
      bgGradientClasses: 'from-secondary to-secondary/60',
      icon: '📈'
    },
    {
      id: 'streamer',
      title: 'iS_streamer',
      description: 'インタラクティブ配信プラットフォーム',
      status: 'planning',
      progress: 25,
      color: 'accent',
      borderColorClass: 'border-accent',
      bgGradientClasses: 'from-accent to-accent/60',
      icon: '🎬'
    },
    {
      id: 'voice-notes',
      title: '音声メモ',
      description: '思考を即座にキャプチャ',
      status: 'completed',
      progress: 100,
      color: 'success',
      borderColorClass: 'border-success',
      bgGradientClasses: 'from-success to-success/60',
      icon: '🎙️'
    }
  ];


  const handleCreateProject = (template: any, projectName: string) => {
    console.log('Creating project:', projectName, template);
    setShowTemplateModal(false);
    
    // 親コンポーネントのプロジェクト作成処理を呼び出し
    if (onProjectCreate) {
      onProjectCreate(template, projectName);
    }
    
    // 新規作成されたプロジェクトを即座に開く
    const projectId = projectName.toLowerCase().replace(/\s+/g, '-');
    if (onProjectClick) {
      onProjectClick(projectId, projectName);
    }
  };


  return (
    <div className="flex-1 p-8 overflow-y-auto">
      <div className="max-w-6xl mx-auto">
        {/* ヘッダー */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4 flex items-center justify-center gap-3">
            <Folder size={40} className="text-primary" />
            アプリドック
          </h1>
          <p className="text-xl text-base-content/70">
            開発中・完成したアプリが並ぶメタスタジオの中核
          </p>
        </div>


        {/* アプリドック（プロジェクト一覧） */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-6 text-base-content/80">開発中のアプリ</h2>
          <div className="grid grid-cols-4 md:grid-cols-8 lg:grid-cols-10 gap-6 justify-items-center">
            {projects.map((project) => (
              <div
                key={project.id}
                className="group cursor-pointer flex flex-col items-center"
                onClick={() => onProjectClick?.(project.id, project.title)}
              >
                {/* アプリアイコン - iPhoneサイズ、統一されたボーダー */}
                <div className={`relative w-20 h-20 rounded-2xl bg-gradient-to-br ${project.bgGradientClasses} shadow-xl neo-depth neo-hover mb-3 flex items-center justify-center overflow-hidden border-3 ${project.borderColorClass} hover:scale-105 transition-all duration-200`}>
                  <span className="text-3xl drop-shadow-lg">{project.icon}</span>
                  
                  {/* ステータスバッジ */}
                  <div className="absolute -top-1 -right-1 z-10">
                    {project.status === 'active' && (
                      <div className="w-3 h-3 bg-success rounded-full animate-pulse border border-white shadow-sm" />
                    )}
                    {project.status === 'planning' && (
                      <div className="w-3 h-3 bg-warning rounded-full border border-white shadow-sm" />
                    )}
                    {project.status === 'completed' && (
                      <div className="w-3 h-3 bg-info rounded-full border border-white shadow-sm" />
                    )}
                  </div>
                </div>
                
                {/* 進捗バー（アイコン外） */}
                <div className="w-20 h-1.5 bg-base-content/20 rounded-full mb-2 overflow-hidden">
                  <div 
                    className={`h-full bg-gradient-to-r ${project.bgGradientClasses} transition-all duration-300`}
                    style={{ width: `${project.progress}%` }}
                  />
                </div>
                
                {/* アプリ名 - 中央揃え、適切な幅 */}
                <div className="w-20 text-center">
                  <h3 className="text-sm font-medium text-base-content truncate leading-tight">
                    {project.title}
                  </h3>
                  <p className="text-xs text-base-content/60 mt-1">
                    {project.progress}%
                  </p>
                </div>
              </div>
            ))}
            
            {/* 新規プロジェクト追加ボタン */}
            <div
              className="group cursor-pointer flex flex-col items-center"
              onClick={() => setShowTemplateModal(true)}
            >
              <div className="relative w-20 h-20 rounded-2xl bg-base-200 border-3 border-dashed border-base-content/30 hover:border-primary hover:bg-primary/10 shadow-xl neo-depth neo-hover mb-3 flex items-center justify-center overflow-hidden transition-all duration-200 hover:scale-105">
                <Plus size={32} className="text-base-content/40 group-hover:text-primary" />
              </div>
              
              <div className="w-20 text-center">
                <h3 className="text-sm font-medium text-base-content truncate leading-tight">
                  新規作成
                </h3>
                <p className="text-xs text-base-content/60 mt-1">
                  アプリ
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* テンプレートモーダル */}
      <ProjectTemplateModal
        isOpen={showTemplateModal}
        onClose={() => setShowTemplateModal(false)}
        onCreateProject={handleCreateProject}
      />
    </div>
  );
}