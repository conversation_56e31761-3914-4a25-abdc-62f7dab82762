import React, { useState, useEffect, useRef } from 'react'
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'

interface Task {
  id: string
  content: string
  status: 'pending' | 'in_progress' | 'completed' | 'archived'
  priority: 'high' | 'medium' | 'low'
  difficulty: 'high' | 'medium' | 'low'
  tags: string[]
  assignee?: string
  dueDate?: string
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  estimatedTime?: number
  actualTime?: number
  parentId?: string
  subtasks?: Task[]
}

interface TaskManagerProps {
  onTaskUpdate?: (task: Task) => void
  onTaskCreate?: (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => void
  onTaskDelete?: (taskId: string) => void
  onTaskMarkdown?: (taskId: string) => void
}

const TaskManager: React.FC<TaskManagerProps> = ({
  onTaskUpdate,
  onTaskCreate,
  onTaskDelete,
  onTaskMarkdown
}) => {
  const [tasks, setTasks] = useState<Task[]>([])
  const [viewMode, setViewMode] = useState<'list' | 'kanban' | 'calendar'>('kanban')
  const [filterPriority, setFilterPriority] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [showCompleted, setShowCompleted] = useState(false)
  const [editingTask, setEditingTask] = useState<string | null>(null)
  const [newTask, setNewTask] = useState<Partial<Task>>({})
  const [isAddingTask, setIsAddingTask] = useState(false)
  const [duplicateWarnings, setDuplicateWarnings] = useState<string[]>([])
  const [progressStats, setProgressStats] = useState({
    total: 0,
    completed: 0,
    inProgress: 0,
    pending: 0,
    completionRate: 0
  })

  // タスクの読み込み（LocalStorageまたはAPI）
  useEffect(() => {
    const loadTasks = () => {
      const savedTasks = localStorage.getItem('metastudio-tasks')
      if (savedTasks) {
        try {
          const parsedTasks = JSON.parse(savedTasks).map((task: any) => ({
            ...task,
            createdAt: new Date(task.createdAt),
            updatedAt: new Date(task.updatedAt),
            completedAt: task.completedAt ? new Date(task.completedAt) : undefined
          }))
          setTasks(parsedTasks)
          updateProgressStats(parsedTasks)
        } catch (error) {
          console.error('タスクの読み込みに失敗:', error)
        }
      }
    }
    loadTasks()
  }, [])

  // タスクの保存
  useEffect(() => {
    if (tasks.length > 0) {
      localStorage.setItem('metastudio-tasks', JSON.stringify(tasks))
      updateProgressStats(tasks)
      detectDuplicates(tasks)
    }
  }, [tasks])

  // 進捗統計の更新
  const updateProgressStats = (taskList: Task[]) => {
    const total = taskList.length
    const completed = taskList.filter(t => t.status === 'completed').length
    const inProgress = taskList.filter(t => t.status === 'in_progress').length
    const pending = taskList.filter(t => t.status === 'pending').length
    const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0

    setProgressStats({
      total,
      completed,
      inProgress,
      pending,
      completionRate
    })
  }

  // 重複検出
  const detectDuplicates = (taskList: Task[]) => {
    const similarities: string[] = []
    const contentMap = new Map<string, Task[]>()

    taskList.forEach(task => {
      const normalizedContent = task.content.toLowerCase().trim()
      if (!contentMap.has(normalizedContent)) {
        contentMap.set(normalizedContent, [])
      }
      contentMap.get(normalizedContent)!.push(task)
    })

    contentMap.forEach((tasks, content) => {
      if (tasks.length > 1) {
        similarities.push(`重複: "${content}" (${tasks.length}個)`)
      }
    })

    setDuplicateWarnings(similarities)
  }

  // タスクの作成
  const createTask = (taskData: Partial<Task>) => {
    const newTaskId = `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const task: Task = {
      id: newTaskId,
      content: taskData.content || '',
      status: taskData.status || 'pending',
      priority: taskData.priority || 'medium',
      difficulty: taskData.difficulty || 'medium',
      tags: taskData.tags || [],
      assignee: taskData.assignee,
      dueDate: taskData.dueDate,
      createdAt: new Date(),
      updatedAt: new Date(),
      estimatedTime: taskData.estimatedTime,
      parentId: taskData.parentId,
      subtasks: []
    }

    setTasks(prev => [...prev, task])
    onTaskCreate?.(task)
    setIsAddingTask(false)
    setNewTask({})
  }

  // タスクの更新
  const updateTask = (taskId: string, updates: Partial<Task>) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { 
            ...task, 
            ...updates, 
            updatedAt: new Date(),
            completedAt: updates.status === 'completed' ? new Date() : task.completedAt
          }
        : task
    ))
    
    const updatedTask = tasks.find(t => t.id === taskId)
    if (updatedTask) {
      onTaskUpdate?.({ ...updatedTask, ...updates })
    }
  }

  // タスクの削除
  const deleteTask = (taskId: string) => {
    setTasks(prev => prev.filter(task => task.id !== taskId))
    onTaskDelete?.(taskId)
  }

  // ドラッグ&ドロップ処理
  const onDragEnd = (result: any) => {
    if (!result.destination) return

    const { source, destination } = result
    const newStatus = destination.droppableId as Task['status']
    const taskId = result.draggableId

    updateTask(taskId, { status: newStatus })
  }

  // フィルタリング
  const filteredTasks = tasks.filter(task => {
    if (filterPriority !== 'all' && task.priority !== filterPriority) return false
    if (filterStatus !== 'all' && task.status !== filterStatus) return false
    if (!showCompleted && task.status === 'completed') return false
    if (searchQuery && !task.content.toLowerCase().includes(searchQuery.toLowerCase())) return false
    return true
  })

  // 優先度アイコン
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return '🔥'
      case 'medium': return '⚡'
      case 'low': return '💡'
      default: return '📋'
    }
  }

  // 難易度アイコン
  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty) {
      case 'high': return '🔴'
      case 'medium': return '🟡'
      case 'low': return '🟢'
      default: return '⚪'
    }
  }

  // ステータスアイコン
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '☑️'
      case 'in_progress': return '🔄'
      case 'pending': return '📋'
      case 'archived': return '📦'
      default: return '📋'
    }
  }

  // task.mdとの同期
  const syncWithTaskMd = async () => {
    try {
      // task.mdの読み込みと解析
      const response = await fetch('/api/task-md', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      })
      
      if (response.ok) {
        const taskMdContent = await response.text()
        // TODO: task.mdの解析とタスクの統合
        console.log('task.md同期中...', taskMdContent)
      }
    } catch (error) {
      console.error('task.md同期エラー:', error)
    }
  }

  // task.mdへの書き込み
  const writeToTaskMd = async () => {
    try {
      const completedTasks = tasks.filter(t => t.status === 'completed')
      const pendingTasks = tasks.filter(t => t.status !== 'completed')
      
      const response = await fetch('/api/task-md', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          completedTasks,
          pendingTasks,
          progressStats
        })
      })
      
      if (response.ok) {
        console.log('task.md更新完了')
      }
    } catch (error) {
      console.error('task.md更新エラー:', error)
    }
  }

  // カンバン表示
  const renderKanbanView = () => (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="grid grid-cols-4 gap-4 h-full">
        {['pending', 'in_progress', 'completed', 'archived'].map(status => (
          <div key={status} className="bg-base-200 rounded-lg p-4">
            <h3 className="font-semibold mb-4 flex items-center gap-2">
              {getStatusIcon(status)}
              {status === 'pending' && '未着手'}
              {status === 'in_progress' && '進行中'}
              {status === 'completed' && '完了'}
              {status === 'archived' && 'アーカイブ'}
              <span className="badge badge-sm">
                {filteredTasks.filter(t => t.status === status).length}
              </span>
            </h3>
            
            <Droppable droppableId={status} isDropDisabled={false} isCombineEnabled={false} ignoreContainerClipping={false}>
              {(provided) => (
                <div
                  ref={provided.innerRef}
                  {...provided.droppableProps}
                  className="space-y-2 min-h-32"
                >
                  {filteredTasks
                    .filter(task => task.status === status)
                    .map((task, index) => (
                      <Draggable key={task.id} draggableId={task.id} index={index}>
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="bg-base-100 p-3 rounded-lg shadow-sm border border-base-300 hover:shadow-md transition-shadow"
                          >
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex items-center gap-1">
                                {getPriorityIcon(task.priority)}
                                {getDifficultyIcon(task.difficulty)}
                              </div>
                              <div className="dropdown dropdown-end">
                                <label tabIndex={0} className="btn btn-ghost btn-xs">⋮</label>
                                <ul className="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-32">
                                  <li><a onClick={() => setEditingTask(task.id)}>編集</a></li>
                                  <li><a onClick={() => deleteTask(task.id)}>削除</a></li>
                                  <li><a onClick={() => onTaskMarkdown?.(task.id)}>MD出力</a></li>
                                </ul>
                              </div>
                            </div>
                            
                            {editingTask === task.id ? (
                              <div className="space-y-2">
                                <textarea
                                  className="textarea textarea-bordered w-full text-sm"
                                  value={task.content}
                                  onChange={(e) => updateTask(task.id, { content: e.target.value })}
                                  onBlur={() => setEditingTask(null)}
                                  autoFocus
                                />
                              </div>
                            ) : (
                              <p className="text-sm text-base-content">{task.content}</p>
                            )}
                            
                            {task.tags.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-2">
                                {task.tags.map(tag => (
                                  <span key={tag} className="badge badge-xs badge-outline">
                                    {tag}
                                  </span>
                                ))}
                              </div>
                            )}
                            
                            {task.dueDate && (
                              <div className="text-xs text-warning mt-2">
                                期限: {new Date(task.dueDate).toLocaleDateString()}
                              </div>
                            )}
                          </div>
                        )}
                      </Draggable>
                    ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </div>
        ))}
      </div>
    </DragDropContext>
  )

  // リスト表示
  const renderListView = () => (
    <div className="space-y-2">
      {filteredTasks.map(task => (
        <div key={task.id} className="bg-base-100 p-4 rounded-lg border border-base-300">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                className="checkbox checkbox-sm"
                checked={task.status === 'completed'}
                onChange={(e) => updateTask(task.id, { 
                  status: e.target.checked ? 'completed' : 'pending' 
                })}
              />
              <div className="flex items-center gap-1">
                {getPriorityIcon(task.priority)}
                {getDifficultyIcon(task.difficulty)}
              </div>
              <span className="text-sm font-medium">{task.content}</span>
            </div>
            
            <div className="flex items-center gap-2">
              <select
                className="select select-xs select-bordered"
                value={task.status}
                onChange={(e) => updateTask(task.id, { status: e.target.value as Task['status'] })}
              >
                <option value="pending">未着手</option>
                <option value="in_progress">進行中</option>
                <option value="completed">完了</option>
                <option value="archived">アーカイブ</option>
              </select>
              
              <button
                className="btn btn-ghost btn-xs"
                onClick={() => deleteTask(task.id)}
              >
                🗑️
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  )

  return (
    <div className="h-full bg-base-100 p-4">
      {/* ヘッダー */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-bold">📋 タスクマネージャー</h2>
          <div className="stats stats-horizontal shadow-sm">
            <div className="stat">
              <div className="stat-title">全体進捗</div>
              <div className="stat-value text-sm">{progressStats.completionRate}%</div>
              <div className="stat-desc">{progressStats.completed}/{progressStats.total}</div>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            className="btn btn-sm btn-primary"
            onClick={() => setIsAddingTask(true)}
          >
            + 新規タスク
          </button>
          
          <button
            className="btn btn-sm btn-outline"
            onClick={syncWithTaskMd}
          >
            📄 task.md同期
          </button>
          
          <button
            className="btn btn-sm btn-outline"
            onClick={writeToTaskMd}
          >
            💾 task.md更新
          </button>
        </div>
      </div>

      {/* 進捗バー */}
      <div className="w-full bg-base-300 rounded-full h-2 mb-4">
        <div
          className="bg-primary h-2 rounded-full transition-all duration-300"
          style={{ width: `${progressStats.completionRate}%` }}
        />
      </div>

      {/* フィルター */}
      <div className="flex flex-wrap items-center gap-4 mb-6">
        <div className="join">
          <button
            className={`join-item btn btn-sm ${viewMode === 'kanban' ? 'btn-active' : ''}`}
            onClick={() => setViewMode('kanban')}
          >
            📊 カンバン
          </button>
          <button
            className={`join-item btn btn-sm ${viewMode === 'list' ? 'btn-active' : ''}`}
            onClick={() => setViewMode('list')}
          >
            📋 リスト
          </button>
        </div>
        
        <input
          type="text"
          placeholder="検索..."
          className="input input-sm input-bordered"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        
        <select
          className="select select-sm select-bordered"
          value={filterPriority}
          onChange={(e) => setFilterPriority(e.target.value)}
        >
          <option value="all">全優先度</option>
          <option value="high">🔥 高優先度</option>
          <option value="medium">⚡ 中優先度</option>
          <option value="low">💡 低優先度</option>
        </select>
        
        <select
          className="select select-sm select-bordered"
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
        >
          <option value="all">全ステータス</option>
          <option value="pending">未着手</option>
          <option value="in_progress">進行中</option>
          <option value="completed">完了</option>
          <option value="archived">アーカイブ</option>
        </select>
        
        <label className="label cursor-pointer">
          <input
            type="checkbox"
            className="checkbox checkbox-sm"
            checked={showCompleted}
            onChange={(e) => setShowCompleted(e.target.checked)}
          />
          <span className="label-text ml-2">完了済み表示</span>
        </label>
      </div>

      {/* 重複警告 */}
      {duplicateWarnings.length > 0 && (
        <div className="alert alert-warning mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <div>
            <h3 className="font-bold">重複タスクが検出されました</h3>
            <ul className="text-sm">
              {duplicateWarnings.map((warning, index) => (
                <li key={index}>{warning}</li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* 新規タスク追加モーダル */}
      {isAddingTask && (
        <div className="modal modal-open">
          <div className="modal-box">
            <h3 className="font-bold text-lg mb-4">新規タスク作成</h3>
            
            <div className="space-y-4">
              <div>
                <label className="label">タスク内容</label>
                <textarea
                  className="textarea textarea-bordered w-full"
                  placeholder="タスクの詳細を入力..."
                  value={newTask.content || ''}
                  onChange={(e) => setNewTask(prev => ({ ...prev, content: e.target.value }))}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="label">優先度</label>
                  <select
                    className="select select-bordered w-full"
                    value={newTask.priority || 'medium'}
                    onChange={(e) => setNewTask(prev => ({ ...prev, priority: e.target.value as Task['priority'] }))}
                  >
                    <option value="high">🔥 高</option>
                    <option value="medium">⚡ 中</option>
                    <option value="low">💡 低</option>
                  </select>
                </div>
                
                <div>
                  <label className="label">難易度</label>
                  <select
                    className="select select-bordered w-full"
                    value={newTask.difficulty || 'medium'}
                    onChange={(e) => setNewTask(prev => ({ ...prev, difficulty: e.target.value as Task['difficulty'] }))}
                  >
                    <option value="high">🔴 高</option>
                    <option value="medium">🟡 中</option>
                    <option value="low">🟢 低</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="label">期限</label>
                <input
                  type="date"
                  className="input input-bordered w-full"
                  value={newTask.dueDate || ''}
                  onChange={(e) => setNewTask(prev => ({ ...prev, dueDate: e.target.value }))}
                />
              </div>
              
              <div>
                <label className="label">タグ（カンマ区切り）</label>
                <input
                  type="text"
                  className="input input-bordered w-full"
                  placeholder="タグ1,タグ2,タグ3"
                  onChange={(e) => setNewTask(prev => ({ 
                    ...prev, 
                    tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
                  }))}
                />
              </div>
            </div>
            
            <div className="modal-action">
              <button
                className="btn btn-primary"
                onClick={() => createTask(newTask)}
                disabled={!newTask.content?.trim()}
              >
                作成
              </button>
              <button
                className="btn btn-ghost"
                onClick={() => {
                  setIsAddingTask(false)
                  setNewTask({})
                }}
              >
                キャンセル
              </button>
            </div>
          </div>
        </div>
      )}

      {/* メインコンテンツ */}
      <div className="flex-1 overflow-auto mb-4">
        {viewMode === 'kanban' ? renderKanbanView() : renderListView()}
      </div>

      {/* TaskWidget比較用（下部に表示） */}
      <div className="border-t border-base-content/10 pt-4 bg-base-100">
        <div className="text-sm font-semibold text-base-content/80 mb-2 flex items-center gap-2">
          📥 振り分け前タスク受け皿
          <span className="badge badge-info badge-xs">アイデア・メモ用</span>
        </div>
        <div className="bg-base-200 rounded-lg p-3 max-h-72 overflow-auto border border-base-content/10">
          <TaskWidgetComparison />
        </div>
        <div className="text-xs text-base-content/60 mt-2">
          💡 思いついたタスクを素早く記録 → TaskManagerで本格管理に移行
        </div>
      </div>
    </div>
  )
}

// TaskWidgetの比較用コンポーネント
const TaskWidgetComparison = () => {
  const [personalTasks, setPersonalTasks] = useState([
    { id: 1, task: 'Claude Code適用音調査', completed: false, priority: 'high' },
    { id: 2, task: 'ダッシュボード実装', completed: true, priority: 'high' },
    { id: 3, task: 'プロジェクト要件整理', completed: false, priority: 'medium' },
    { id: 4, task: 'UI/UX改善', completed: false, priority: 'medium' }
  ]);
  const [newTask, setNewTask] = useState('');
  
  const completedCount = personalTasks.filter(t => t.completed).length;
  const progressPercentage = (completedCount / personalTasks.length) * 100;
  
  const toggleTask = (id: number) => {
    setPersonalTasks(prev => 
      prev.map(task => 
        task.id === id ? { ...task, completed: !task.completed } : task
      )
    );
  };
  
  const addTask = () => {
    if (newTask.trim()) {
      const newTaskObj = {
        id: Date.now(),
        task: newTask.trim(),
        completed: false,
        priority: 'medium' as const
      };
      setPersonalTasks(prev => [...prev, newTaskObj]);
      setNewTask('');
    }
  };
  
  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-semibold">個人タスク</span>
        <span className="text-xs text-base-content/60">{completedCount}/{personalTasks.length}</span>
      </div>
      
      {/* 常時表示の入力フィールド */}
      <div className="flex gap-1 mb-2">
        <input
          className="input input-xs flex-1 bg-base-100 border-base-content/20"
          value={newTask}
          onChange={(e) => setNewTask(e.target.value)}
          placeholder="新しいタスクを入力..."
          onKeyPress={(e) => e.key === 'Enter' && addTask()}
        />
        <button 
          className="btn btn-xs btn-primary" 
          onClick={addTask}
          disabled={!newTask.trim()}
        >
          追加
        </button>
      </div>
      
      <div className="space-y-2 flex-1 overflow-y-auto">
        {personalTasks.map(item => (
          <div key={item.id} className="flex items-center gap-2 p-2 bg-base-100 rounded-lg">
            <input 
              type="checkbox" 
              className="checkbox checkbox-xs checkbox-primary"
              checked={item.completed}
              onChange={() => toggleTask(item.id)}
            />
            <span className={`flex-1 text-xs ${item.completed ? 'line-through text-base-content/50' : ''}`}>
              {item.task}
            </span>
            <span className={`badge badge-xs ${
              item.priority === 'high' ? 'badge-error' : 
              item.priority === 'medium' ? 'badge-warning' : 'badge-info'
            }`}>
              {item.priority}
            </span>
          </div>
        ))}
      </div>
      
      <div className="w-full bg-base-300 rounded-full h-1.5 mt-2">
        <div className="bg-primary h-1.5 rounded-full transition-all" style={{width: `${progressPercentage}%`}}></div>
      </div>
    </div>
  );
};

export default TaskManager