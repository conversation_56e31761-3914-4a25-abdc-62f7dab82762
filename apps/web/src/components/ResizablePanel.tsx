'use client';

import { useState, useRef, useEffect, ReactNode } from 'react';

interface ResizablePanelProps {
  children: ReactNode;
  direction: 'horizontal' | 'vertical';
  initialSize: number;
  minSize?: number;
  maxSize?: number;
  className?: string;
  handlePosition?: 'start' | 'end'; // 水平の場合: start=left, end=right | 垂直の場合: start=top, end=bottom
  storageKey?: string; // サイズ保存用のキー
  isFlexible?: boolean; // フレキシブルリサイズ（メインペインと連動）
}

export default function ResizablePanel({
  children,
  direction,
  initialSize,
  minSize = 100,
  maxSize = 1200,
  className = '',
  handlePosition = 'end',
  storageKey,
  isFlexible = false
}: ResizablePanelProps) {
  // Hydration問題を避けるため、初期値はinitialSizeに固定
  const [size, setSize] = useState(initialSize);
  const [isResizing, setIsResizing] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const resizeRef = useRef<HTMLDivElement>(null);

  // localStorageからサイズを復元、なければinitialSizeを使用
  const getStoredSize = () => {
    if (!storageKey || typeof window === 'undefined') return initialSize;
    const stored = localStorage.getItem(`resizable-panel-${storageKey}`);
    if (stored) {
      const parsedSize = parseInt(stored, 10);
      return Math.max(minSize, maxSize ? Math.min(maxSize, parsedSize) : parsedSize);
    }
    return initialSize;
  };

  // サイズをlocalStorageに保存
  const saveSize = (newSize: number) => {
    if (storageKey && typeof window !== 'undefined') {
      localStorage.setItem(`resizable-panel-${storageKey}`, newSize.toString());
    }
  };

  // コンポーネントマウント時にサイズを復元（Hydration後のみ）
  useEffect(() => {
    setIsMounted(true);
    if (storageKey && typeof window !== 'undefined') {
      const stored = localStorage.getItem(`resizable-panel-${storageKey}`);
      if (stored) {
        const parsedSize = parseInt(stored, 10);
        const validSize = Math.max(minSize, maxSize ? Math.min(maxSize, parsedSize) : parsedSize);
        if (validSize !== initialSize) {
          setSize(validSize);
        }
      }
    }
  }, [storageKey, initialSize, minSize, maxSize]);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing || !resizeRef.current) return;

      const rect = resizeRef.current.getBoundingClientRect();
      let newSize: number;

      if (direction === 'horizontal') {
        // 水平リサイズ（幅の調整）
        if (handlePosition === 'start') {
          newSize = rect.right - e.clientX;
        } else {
          newSize = e.clientX - rect.left;
        }
      } else {
        // 垂直リサイズ（高さの調整）
        if (handlePosition === 'start') {
          // ハンドルが上部にある場合（ターミナルペイン）
          if (isFlexible) {
            // フレキシブルモード: メインペインとの連動を重視
            const viewportHeight = window.innerHeight;
            const headerHeight = 60; // ヘッダー高さの概算を調整
            const availableHeight = viewportHeight - headerHeight;
            const mousePosFromTop = e.clientY - headerHeight;
            
            // より制限を緩和し、ビューポートの95%まで拡張可能に
            const maxFlexibleHeight = availableHeight * 0.95;
            newSize = Math.min(
              maxFlexibleHeight,
              Math.max(minSize, availableHeight - mousePosFromTop + 10) // より余裕のあるマージン
            );
          } else {
            // 従来のロジック
            const viewportHeight = window.innerHeight;
            const headerHeight = 80;
            const availableHeight = viewportHeight - headerHeight;
            const mousePosFromTop = e.clientY - headerHeight;
            
            const maxResizeHeight = availableHeight * 0.8;
            newSize = Math.min(
              maxResizeHeight,
              Math.max(minSize, availableHeight - mousePosFromTop)
            );
          }
        } else {
          // ハンドルが下部にある場合、下にドラッグでパネルが大きくなる
          newSize = e.clientY - rect.top;
        }
      }

      newSize = Math.max(minSize, maxSize ? Math.min(maxSize, newSize) : newSize);
      setSize(newSize);
      saveSize(newSize);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, direction, minSize, maxSize]);

  const handleMouseDown = () => {
    setIsResizing(true);
  };

  // Hydrationエラー回避: サーバーとクライアントで一致するスタイル
  // 初期レンダリング時は常にinitialSizeを使用
  const sizeStyle = direction === 'horizontal'
    ? { width: `${isMounted ? size : initialSize}px` }
    : { height: `${isMounted ? size : initialSize}px` };

  const resizerClasses = direction === 'horizontal'
    ? 'w-1 h-full bg-base-content/20 hover:bg-primary/50 cursor-col-resize group-hover:bg-primary/30 transition-colors'
    : 'h-2 w-full bg-base-content/30 hover:bg-primary/60 cursor-row-resize group-hover:bg-primary/40 transition-colors shadow-sm';

  return (
    <div 
      ref={resizeRef}
      className={`relative group ${className}`}
      style={sizeStyle}
    >
      {children}
      
      {/* リサイザーハンドル */}
      <div
        className={`absolute ${
          direction === 'horizontal' 
            ? handlePosition === 'start' 
              ? 'left-0 top-0' 
              : 'right-0 top-0'
            : handlePosition === 'start'
              ? 'top-0 left-0'
              : 'bottom-0 left-0'
        } ${resizerClasses} z-10`}
        onMouseDown={handleMouseDown}
        title={direction === 'horizontal' ? '幅を調整' : '高さを調整'}
      >
        {/* リサイザーの視覚的インジケーター */}
        <div className={`absolute ${
          direction === 'horizontal' 
            ? 'left-1/2 top-1/2 w-0.5 h-8 -translate-x-1/2 -translate-y-1/2' 
            : 'left-1/2 top-1/2 h-0.5 w-8 -translate-x-1/2 -translate-y-1/2'
        } bg-base-content/40 rounded-full opacity-0 group-hover:opacity-100 transition-opacity`} />
      </div>
      
      {/* リサイズ中のオーバーレイ */}
      {isResizing && (
        <div className="fixed inset-0 z-50" style={{ cursor: direction === 'horizontal' ? 'col-resize' : 'row-resize' }} />
      )}
    </div>
  );
}