'use client';

import { QrCode, Smartphone, Download, RefreshCw } from 'lucide-react';
import { useState, useEffect } from 'react';

export default function ExpoQRView() {
  const [qrUrl, setQrUrl] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [mobileAppStatus, setMobileAppStatus] = useState<'not-found' | 'ready' | 'building'>('not-found');

  useEffect(() => {
    checkMobileApp();
  }, []);

  const checkMobileApp = async () => {
    // モバイルアプリの存在チェック（実際にはExpoプロジェクトの確認）
    setIsLoading(true);
    setTimeout(() => {
      setMobileAppStatus('not-found');
      setIsLoading(false);
    }, 1000);
  };

  const initializeExpoProject = async () => {
    setMobileAppStatus('building');
    // 実際にはここでExpoプロジェクトを初期化
    setTimeout(() => {
      setMobileAppStatus('ready');
      setQrUrl('exp://192.168.3.25:8081'); // 仮のURL
    }, 3000);
  };

  return (
    <div className="flex-1 p-8 overflow-y-auto">
      <div className="max-w-4xl mx-auto">
        {/* ヘッダー */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4 flex items-center justify-center gap-3">
            <Smartphone size={40} className="text-primary" />
            Expo モバイルプレビュー
          </h1>
          <p className="text-xl text-base-content/70">
            スマートフォンでアプリをプレビュー
          </p>
        </div>

        {isLoading ? (
          <div className="text-center py-12">
            <div className="loading loading-spinner loading-lg text-primary"></div>
            <p className="mt-4 text-base-content/60">確認中...</p>
          </div>
        ) : (
          <>
            {mobileAppStatus === 'not-found' && (
              <div className="card bg-base-200 shadow-xl neo-depth">
                <div className="card-body text-center">
                  <h2 className="card-title justify-center text-warning mb-4">
                    モバイルアプリが見つかりません
                  </h2>
                  <p className="text-base-content/70 mb-6">
                    Expoモバイルアプリをセットアップして、QRコードでプレビューできるようにしましょう
                  </p>
                  <div className="card-actions justify-center">
                    <button 
                      className="btn btn-primary gap-2"
                      onClick={initializeExpoProject}
                    >
                      <Download size={18} />
                      Expoプロジェクトを作成
                    </button>
                  </div>
                </div>
              </div>
            )}

            {mobileAppStatus === 'building' && (
              <div className="text-center py-12">
                <div className="loading loading-spinner loading-lg text-primary"></div>
                <p className="mt-4 text-lg font-medium">Expoプロジェクトを構築中...</p>
                <p className="text-base-content/60">しばらくお待ちください</p>
              </div>
            )}

            {mobileAppStatus === 'ready' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* QRコード */}
                <div className="card bg-base-200 shadow-xl neo-depth">
                  <div className="card-body items-center text-center">
                    <h2 className="card-title mb-4">QRコード</h2>
                    <div className="bg-white p-8 rounded-lg">
                      <QrCode size={200} className="text-black" />
                    </div>
                    <p className="text-sm text-base-content/70 mt-4">
                      Expo Goアプリでスキャン
                    </p>
                    <code className="text-xs bg-base-300 px-2 py-1 rounded mt-2">
                      {qrUrl}
                    </code>
                  </div>
                </div>

                {/* 手順 */}
                <div className="card bg-base-200 shadow-xl neo-depth">
                  <div className="card-body">
                    <h2 className="card-title mb-4">接続手順</h2>
                    <ol className="space-y-3 text-sm">
                      <li className="flex gap-2">
                        <span className="text-primary font-bold">1.</span>
                        <div>
                          <p className="font-medium">Expo Goアプリをインストール</p>
                          <p className="text-xs text-base-content/60">
                            App Store / Google Playで「Expo Go」を検索
                          </p>
                        </div>
                      </li>
                      <li className="flex gap-2">
                        <span className="text-primary font-bold">2.</span>
                        <div>
                          <p className="font-medium">同じWi-Fiに接続</p>
                          <p className="text-xs text-base-content/60">
                            開発PCと同じネットワークに接続してください
                          </p>
                        </div>
                      </li>
                      <li className="flex gap-2">
                        <span className="text-primary font-bold">3.</span>
                        <div>
                          <p className="font-medium">QRコードをスキャン</p>
                          <p className="text-xs text-base-content/60">
                            Expo Goアプリ内のQRスキャナーを使用
                          </p>
                        </div>
                      </li>
                    </ol>
                    
                    <div className="divider"></div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-success">● 実行中</span>
                      <button className="btn btn-sm btn-ghost gap-1">
                        <RefreshCw size={14} />
                        再起動
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        )}

        {/* ヒント */}
        <div className="mt-8 p-4 bg-info/10 rounded-lg border border-info/20">
          <p className="text-sm text-info">
            💡 ヒント: 開発サーバーとモバイルデバイスが同じネットワーク上にあることを確認してください
          </p>
        </div>
      </div>
    </div>
  );
}