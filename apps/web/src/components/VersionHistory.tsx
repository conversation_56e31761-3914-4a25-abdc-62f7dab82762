'use client';

import { useState, useEffect } from 'react';
import { History, GitBranch, Save, Undo, Plus, Calendar, Tag, FileText, Clock } from 'lucide-react';

interface Version {
  id: string;
  version: string;
  timestamp: Date;
  description: string;
  author: string;
  changes: {
    added: string[];
    modified: string[];
    removed: string[];
  };
  content?: string;
}

interface VersionHistoryProps {
  projectId?: string;
  onVersionRestore?: (version: Version) => void;
  currentContent?: string;
}

export default function VersionHistory({ projectId, onVersionRestore, currentContent }: VersionHistoryProps) {
  const [versions, setVersions] = useState<Version[]>([
    {
      id: '1',
      version: 'v0.0.9',
      timestamp: new Date(Date.now() - 1000 * 60 * 1), // 1分前
      description: 'プロジェクト名永続化・VRMモデル削除・重複アップロード問題修正',
      author: 'Claude',
      changes: {
        added: ['localStorage永続化初期化機能', 'VRMモデル削除ボタン', 'アップロード重複確認'],
        modified: ['Sidebar.tsx', 'AICharacterPanel.tsx'],
        removed: ['重複ファイル入力要素']
      }
    },
    {
      id: '2',
      version: 'v0.0.8',
      timestamp: new Date(Date.now() - 1000 * 60 * 3), // 3分前
      description: 'プロジェクトクリック・名前変更問題とサイドバー管理ボタン修正',
      author: 'Claude',
      changes: {
        added: ['projectNameUpdate state', 'onTabChange debug修正'],
        modified: ['TabManager.tsx', 'MetaStudioLayout.tsx', 'Sidebar.tsx'],
        removed: ['不要なdebugログ']
      }
    },
    {
      id: '3',
      version: 'v0.0.7',
      timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5分前
      description: 'project/projext概念明記とトースト通知位置改善',
      author: 'Claude',
      changes: {
        added: ['CLAUDE.md project/projext概念説明'],
        modified: ['ProjectView.tsx トースト位置修正'],
        removed: []
      }
    },
    {
      id: '3',
      version: 'v0.0.6',
      timestamp: new Date(Date.now() - 1000 * 60 * 10), // 10分前
      description: 'ブラウザ自動化Google表示デモ実装',
      author: 'Claude',
      changes: {
        added: ['BrowserAutomationPanel.tsx Google UI'],
        modified: ['BrowserAutomationPanel.tsx CSP回避対応'],
        removed: []
      }
    },
    {
      id: '4',
      version: 'v0.0.5',
      timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15分前
      description: 'プロジェクト名変更のサイドバー反映機能実装',
      author: 'Claude',
      changes: {
        added: ['onProjectNameChange callback'],
        modified: ['MetaStudioLayout.tsx', 'ProjectView.tsx'],
        removed: []
      }
    },
    {
      id: '5',
      version: 'v0.0.4',
      timestamp: new Date(Date.now() - 1000 * 60 * 20), // 20分前
      description: 'プロジェクトクリック時のメインタブ内容更新修正',
      author: 'Claude',
      changes: {
        added: [],
        modified: ['MetaStudioLayout.tsx content prop修正'],
        removed: []
      }
    },
    {
      id: '6',
      version: 'v0.0.3',
      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30分前
      description: 'UIコンポーネントの追加と音声機能の改善',
      author: 'Claude',
      changes: {
        added: ['VoiceInput.tsx', 'YamlEditor.tsx'],
        modified: ['TabManager.tsx', 'Sidebar.tsx'],
        removed: []
      }
    },
    {
      id: '7',
      version: 'v0.0.2',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2時間前
      description: 'レスポンシブ対応とパフォーマンス最適化',
      author: 'User',
      changes: {
        added: ['ResizablePanel.tsx'],
        modified: ['MetaStudioLayout.tsx', 'ProjectView.tsx'],
        removed: ['OldComponent.tsx']
      }
    },
    {
      id: '8',
      version: 'v0.0.1',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1日前
      description: '初期プロジェクト設定',
      author: 'System',
      changes: {
        added: ['初期ファイル群'],
        modified: [],
        removed: []
      }
    }
  ]);
  
  const [selectedVersion, setSelectedVersion] = useState<Version | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newVersionDesc, setNewVersionDesc] = useState('');

  const generateNextVersion = (): string => {
    if (versions.length === 0) return 'v0.0.1';
    
    const latestVersion = versions[0].version;
    const versionMatch = latestVersion.match(/v(\d+)\.(\d+)\.(\d+)/);
    
    if (versionMatch) {
      const [, major, minor, patch] = versionMatch;
      const newPatch = parseInt(patch) + 1;
      return `v${major}.${minor}.${newPatch}`;
    }
    
    return 'v0.0.1';
  };

  const createNewVersion = () => {
    if (!newVersionDesc.trim()) return;

    const newVersion: Version = {
      id: Date.now().toString(),
      version: generateNextVersion(),
      timestamp: new Date(),
      description: newVersionDesc.trim(),
      author: 'User',
      changes: {
        added: currentContent ? ['現在の変更'] : [],
        modified: [],
        removed: []
      },
      content: currentContent
    };

    setVersions([newVersion, ...versions]);
    setNewVersionDesc('');
    setShowCreateDialog(false);
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 60) return `${diffMins}分前`;
    if (diffHours < 24) return `${diffHours}時間前`;
    return `${diffDays}日前`;
  };

  const getChangesSummary = (changes: Version['changes']) => {
    const total = changes.added.length + changes.modified.length + changes.removed.length;
    if (total === 0) return '変更なし';
    
    const parts = [];
    if (changes.added.length > 0) parts.push(`+${changes.added.length}`);
    if (changes.modified.length > 0) parts.push(`~${changes.modified.length}`);
    if (changes.removed.length > 0) parts.push(`-${changes.removed.length}`);
    
    return parts.join(' ');
  };

  return (
    <div className="h-full flex flex-col bg-base-100">
      {/* ヘッダー */}
      <div className="p-4 border-b border-base-content/10 bg-base-200/50">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <History size={20} />
            <h2 className="text-lg font-bold">変更履歴</h2>
            <div className="badge badge-primary badge-sm">{versions.length}件</div>
          </div>
          <button
            onClick={() => setShowCreateDialog(true)}
            className="btn btn-sm btn-primary"
          >
            <Plus size={16} />
            新しいバージョン
          </button>
        </div>
        
        <div className="text-sm text-base-content/70">
          プロジェクトの変更履歴とバージョン管理
        </div>
      </div>

      {/* バージョン一覧 */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-3">
          {versions.map((version, index) => (
            <div
              key={version.id}
              className={`border rounded-lg p-4 transition-all cursor-pointer ${
                selectedVersion?.id === version.id
                  ? 'border-primary bg-primary/5'
                  : 'border-base-content/20 hover:border-primary/50 hover:bg-base-200/50'
              }`}
              onClick={() => setSelectedVersion(selectedVersion?.id === version.id ? null : version)}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2">
                  <div className="badge badge-outline badge-sm">
                    <Tag size={10} className="mr-1" />
                    {version.version}
                  </div>
                  {index === 0 && (
                    <div className="badge badge-success badge-sm">最新</div>
                  )}
                </div>
                <div className="text-xs text-base-content/60 flex items-center gap-1">
                  <Clock size={10} />
                  {formatTimeAgo(version.timestamp)}
                </div>
              </div>
              
              <div className="mb-2">
                <h3 className="font-medium text-sm">{version.description}</h3>
                <div className="text-xs text-base-content/60 mt-1">
                  by {version.author} • {getChangesSummary(version.changes)}
                </div>
              </div>

              {selectedVersion?.id === version.id && (
                <div className="mt-3 pt-3 border-t border-base-content/10">
                  <div className="space-y-2">
                    {version.changes.added.length > 0 && (
                      <div>
                        <div className="text-xs font-medium text-success mb-1">追加されたファイル:</div>
                        <div className="flex flex-wrap gap-1">
                          {version.changes.added.map((file, i) => (
                            <div key={i} className="badge badge-success badge-xs">{file}</div>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {version.changes.modified.length > 0 && (
                      <div>
                        <div className="text-xs font-medium text-warning mb-1">変更されたファイル:</div>
                        <div className="flex flex-wrap gap-1">
                          {version.changes.modified.map((file, i) => (
                            <div key={i} className="badge badge-warning badge-xs">{file}</div>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {version.changes.removed.length > 0 && (
                      <div>
                        <div className="text-xs font-medium text-error mb-1">削除されたファイル:</div>
                        <div className="flex flex-wrap gap-1">
                          {version.changes.removed.map((file, i) => (
                            <div key={i} className="badge badge-error badge-xs">{file}</div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex gap-2 mt-3">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onVersionRestore?.(version);
                      }}
                      className="btn btn-xs btn-outline"
                    >
                      <Undo size={12} />
                      この版に戻す
                    </button>
                    <button className="btn btn-xs btn-ghost">
                      <FileText size={12} />
                      詳細
                    </button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
        
        {versions.length === 0 && (
          <div className="text-center py-8 text-base-content/50">
            <GitBranch size={48} className="mx-auto mb-2 opacity-50" />
            <p>まだバージョン履歴がありません</p>
            <p className="text-xs">初回保存時に自動的に作成されます</p>
          </div>
        )}
      </div>

      {/* 新しいバージョン作成ダイアログ */}
      {showCreateDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-base-100 rounded-lg p-6 w-96 max-w-[90vw]">
            <h3 className="text-lg font-bold mb-4">新しいバージョンを作成</h3>
            
            <div className="mb-4">
              <div className="text-sm text-base-content/70 mb-2">
                バージョン: <span className="font-mono font-medium">{generateNextVersion()}</span>
              </div>
              <label className="label">
                <span className="label-text">変更内容の説明</span>
              </label>
              <textarea
                value={newVersionDesc}
                onChange={(e) => setNewVersionDesc(e.target.value)}
                className="textarea textarea-bordered w-full"
                placeholder="この変更で何を追加/修正/削除したかを記述してください..."
                rows={3}
              />
            </div>
            
            <div className="flex gap-2 justify-end">
              <button
                onClick={() => {
                  setShowCreateDialog(false);
                  setNewVersionDesc('');
                }}
                className="btn btn-ghost"
              >
                キャンセル
              </button>
              <button
                onClick={createNewVersion}
                className="btn btn-primary"
                disabled={!newVersionDesc.trim()}
              >
                <Save size={16} />
                バージョン作成
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}