'use client';

import { useState, useEffect } from 'react';
import { <PERSON>older, FileText, Settings, Wrench, BarChart3, Bot, Calendar, Search, Plus, Edit3, Copy, Trash2 } from 'lucide-react';
import Image from 'next/image';

interface SidebarProps {
  onDashboardClick?: () => void;
  onProjectClick?: (project: string) => void;
  onLauncherClick?: () => void;
  onSystemClick?: (system: string) => void;
  onFileClick?: (fileName: string, filePath?: string) => void;
  onHomeClick?: () => void;
  projectListVersion?: number;
  lastCreatedProject?: { name: string; template: any } | null;
}

interface Project {
  id: string;
  name: string;
  type: 'mobile' | 'web' | 'ai' | 'game' | 'desktop';
  priority: 1 | 2 | 3 | 4 | 5;
  progress: number;
  updatedAt: Date;
}

export default function Sidebar({ 
  onDashboardClick, 
  onProjectClick, 
  onLauncherClick, 
  onSystemClick, 
  onFileClick, 
  onHomeClick,
  projectListVersion,
  lastCreatedProject
}: SidebarProps) {
  const [activeProject, setActiveProject] = useState<string | null>(null);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [fileItems, setFileItems] = useState<any[]>([]);
  const [filesLoading, setFilesLoading] = useState(false);

  // デフォルトプロジェクト
  const defaultProjects: Project[] = [
    {
      id: '1',
      name: '瞑想アプリ_projext',
      type: 'mobile',
      priority: 5,
      progress: 75,
      updatedAt: new Date('2024-01-01T10:00:00Z')
    },
    {
      id: '2',
      name: '投資bot_projext',
      type: 'ai',
      priority: 4,
      progress: 90,
      updatedAt: new Date('2024-01-01T08:00:00Z')
    },
    {
      id: '3',
      name: 'iS_streamer_projext',
      type: 'web',
      priority: 3,
      progress: 25,
      updatedAt: new Date('2023-12-31T10:00:00Z')
    }
  ];

  const [projects, setProjects] = useState<Project[]>(defaultProjects);

  // 新規プロジェクト追加の監視
  useEffect(() => {
    if (lastCreatedProject) {
      const newProject: Project = {
        id: Date.now().toString(),
        name: lastCreatedProject.name,
        type: lastCreatedProject.template?.type || 'web',
        priority: 5,
        progress: 0,
        updatedAt: new Date()
      };

      setProjects(prev => {
        const existingIndex = prev.findIndex(p => p.name === newProject.name);
        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = { ...updated[existingIndex], updatedAt: new Date() };
          return updated;
        } else {
          return [newProject, ...prev];
        }
      });

      setTimeout(() => {
        setActiveProject(lastCreatedProject.name);
      }, 100);
    }
  }, [projectListVersion, lastCreatedProject]);

  // ファイルシステムの読み込み
  useEffect(() => {
    const loadFiles = async () => {
      setFilesLoading(true);
      try {
        const response = await fetch('/api/files');
        const data = await response.json();
        if (data.success && data.items) {
          setFileItems(data.items);
        }
      } catch (error) {
        console.error('Failed to load files:', error);
      } finally {
        setFilesLoading(false);
      }
    };

    loadFiles();
  }, []);

  const getTypeColor = (type: Project['type']) => {
    switch (type) {
      case 'mobile': return 'text-primary';
      case 'ai': return 'text-secondary';
      case 'web': return 'text-accent';
      case 'game': return 'text-error';
      case 'desktop': return 'text-warning';
      default: return 'text-neutral';
    }
  };

  const getFileIcon = (fileName: string, isDirectory: boolean) => {
    if (isDirectory) return <Folder size={16} className="text-info" />;
    if (fileName.endsWith('.md')) return <FileText size={16} className="text-info" />;
    if (fileName.endsWith('.yaml') || fileName.endsWith('.yml')) return <FileText size={16} className="text-warning" />;
    if (fileName.endsWith('.json')) return <FileText size={16} className="text-success" />;
    if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) return <FileText size={16} className="text-primary" />;
    if (fileName.endsWith('.js') || fileName.endsWith('.jsx')) return <FileText size={16} className="text-warning" />;
    return <FileText size={16} className="text-base-content" />;
  };

  const handleDirectoryClick = async (path: string, isDirectory: boolean) => {
    if (isDirectory) {
      const isExpanded = expandedFolders.has(path);
      if (isExpanded) {
        setExpandedFolders(prev => {
          const newSet = new Set(prev);
          newSet.delete(path);
          return newSet;
        });
      } else {
        setExpandedFolders(prev => new Set([...prev, path]));
        // Load directory contents
        try {
          const response = await fetch(`/api/files?path=${encodeURIComponent(path)}`);
          const data = await response.json();
          if (data.success && data.items) {
            // Handle directory expansion logic here
          }
        } catch (error) {
          console.error('Failed to load directory:', error);
        }
      }
    } else {
      onFileClick?.(path.split('/').pop() || '', path);
    }
  };

  const renderFileTree = (items: any[], level: number = 0) => {
    if (!items) return null;
    
    return items.map((item, index) => {
      const isExpanded = expandedFolders.has(item.path);
      const indent = level * 16;
      
      return (
        <div key={`${item.path}-${index}`}>
          <div
            className="flex items-center gap-2 p-1 rounded cursor-pointer hover:bg-base-200/50"
            style={{ paddingLeft: `${8 + indent}px` }}
            onClick={() => handleDirectoryClick(item.path, item.type === 'directory')}
          >
            {getFileIcon(item.name, item.type === 'directory')}
            <span className="text-sm truncate">{item.name}</span>
            {item.type === 'directory' && (
              <span className="text-xs text-base-content/50 ml-auto">
                {isExpanded ? '▼' : '▶'}
              </span>
            )}
          </div>
          {item.type === 'directory' && isExpanded && item.children && (
            <div>
              {renderFileTree(item.children, level + 1)}
            </div>
          )}
        </div>
      );
    });
  };

  return (
    <div className="h-full flex flex-col p-4 neo-glass">
      {/* メタスタジオアイコン & タイトル */}
      <div className="mb-6">
        <div 
          className="flex items-center gap-3 mb-2 p-3 rounded-lg neo-depth cursor-pointer hover:neo-hover transition-all"
          onClick={onHomeClick}
          title="ダッシュボードに戻る"
        >
          <div className="relative w-10 h-10">
            <Image
              src="/meta-studio-logo.png"
              alt="Meta Studio Logo"
              width={40}
              height={40}
              className="rounded-full"
            />
          </div>
          <div>
            <h1 className="text-lg font-bold">メタスタジオ</h1>
            <p className="text-xs text-base-content/70">
              脳内現実化ツール
            </p>
          </div>
        </div>
      </div>

      {/* プロジェクト管理エリア */}
      <div className="flex-1 overflow-y-auto">
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-sm font-semibold text-base-content/80">
              プロジェクト
            </h2>
            <button
              className="btn btn-xs btn-ghost text-primary neo-hover"
              onClick={() => onSystemClick?.('管理')}
              title="管理ダッシュボード"
            >
              管理
            </button>
          </div>
          <div className="space-y-1">
            {projects.map(project => (
              <div 
                key={project.id}
                className={`flex flex-col gap-1 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 ${
                  activeProject === project.name ? 'ring-1 ring-primary' : 'hover:bg-base-300'
                }`}
                onClick={() => {
                  setActiveProject(project.name);
                  onProjectClick?.(project.name);
                }}
              >
                <div className="flex items-center gap-2">
                  <Folder size={16} className={getTypeColor(project.type)} />
                  <span className="text-sm font-medium flex-1 truncate">
                    {project.name}
                  </span>
                </div>
                
                <div className="flex items-center justify-between text-xs">
                  <span className={`badge badge-xs ${getTypeColor(project.type).replace('text-', 'badge-')}`}>
                    {project.type}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <div className="flex-1 bg-base-content/20 rounded-full h-1">
                    <div 
                      className={`h-1 rounded-full ${getTypeColor(project.type).replace('text-', 'bg-')}`}
                      style={{ width: `${project.progress}%` }}
                    />
                  </div>
                  <span className="text-xs text-base-content/60 w-8 text-right">
                    {project.progress}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* ファイル管理エリア */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-sm font-semibold text-base-content/80">
              ファイル
            </h2>
            <Search size={14} className="text-base-content/50" />
          </div>
          <div className="space-y-1 max-h-40 overflow-y-auto">
            {filesLoading ? (
              <div className="text-xs text-center text-base-content/50 py-2">
                読み込み中...
              </div>
            ) : (
              <>
                <div 
                  className="flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-base-200/50"
                  onClick={() => onFileClick?.('README.md', '/README.md')}
                >
                  <FileText size={16} className="text-info" />
                  <span className="text-sm">README.md</span>
                </div>
                <div 
                  className="flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-base-200/50"
                  onClick={() => onFileClick?.('task.md', '/task.md')}
                >
                  <FileText size={16} className="text-warning" />
                  <span className="text-sm">task.md</span>
                </div>
                <div 
                  className="flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-base-200/50"
                  onClick={() => onFileClick?.('test-yaml.yaml', '/test-yaml.yaml')}
                >
                  <FileText size={16} className="text-warning" />
                  <span className="text-sm">test-yaml.yaml</span>
                </div>
                {renderFileTree(fileItems.slice(0, 5))}
              </>
            )}
          </div>
        </div>

        {/* アプリドック */}
        <div className="mb-4">
          <h2 className="text-sm font-semibold mb-2 text-base-content/80">
            アプリドック
          </h2>
          <div className="space-y-1">
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onLauncherClick?.()}
            >
              <Folder size={16} className="text-primary brightness-110" />
              <span className="text-sm font-medium">アプリドック</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('エージェント管理')}
            >
              <Bot size={16} className="text-secondary brightness-125" />
              <span className="text-sm font-medium">エージェント管理</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('統計')}
            >
              <BarChart3 size={16} className="text-accent brightness-125" />
              <span className="text-sm font-medium">統計</span>
            </div>
          </div>
        </div>

        {/* システムツール */}
        <div className="mb-4">
          <h2 className="text-sm font-semibold mb-2 text-base-content/80">
            システムツール
          </h2>
          <div className="space-y-1">
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('エディター')}
            >
              <FileText size={16} className="text-info" />
              <span className="text-sm font-medium">エディター</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('AIキャラクター')}
            >
              <Bot size={16} className="text-primary" />
              <span className="text-sm font-medium">AIキャラクター</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('タスクマネージャー')}
            >
              <Calendar size={16} className="text-secondary brightness-125" />
              <span className="text-sm font-medium">タスクマネージャー</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('インボックス')}
            >
              <FileText size={16} className="text-warning" />
              <span className="text-sm font-medium">インボックス</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('ブラウザ自動化')}
            >
              <Bot size={16} className="text-accent" />
              <span className="text-sm font-medium">ブラウザ自動化</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('対談スタジオ')}
            >
              <Bot size={16} className="text-primary brightness-150" />
              <span className="text-sm font-medium">対談スタジオ</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('バックアップ')}
            >
              <Copy size={16} className="text-warning" />
              <span className="text-sm font-medium">バックアップ</span>
            </div>
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
              onClick={() => onSystemClick?.('システム概要')}
            >
              <BarChart3 size={16} className="text-info" />
              <span className="text-sm font-medium">システム概要</span>
            </div>
          </div>
        </div>
      </div>

      {/* 設定ボタン */}
      <div className="border-t pt-4 pb-6 mt-auto">
        <div className="flex justify-end">
          <div 
            className="flex items-center gap-2 px-3 py-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
            onClick={() => onSystemClick?.('設定')}
            title="設定"
          >
            <Wrench size={16} className="text-info brightness-150" />
            <span className="text-sm font-medium">設定</span>
          </div>
        </div>
      </div>
    </div>
  );
}