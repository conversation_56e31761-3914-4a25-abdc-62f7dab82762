'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { Puzzle, Play, Pause, Settings, Download, Trash2, RefreshCw } from 'lucide-react';

// LocalAIVtuber参考: プラグインインターフェース
interface PluginInterface {
  id: string;
  name: string;
  version: string;
  description: string;
  type: 'TTS' | 'LLM' | 'VRM' | 'VOICE' | 'INTEGRATION';
  enabled: boolean;
  config: Record<string, any>;
  dependencies?: string[];
}

interface TTSPlugin extends PluginInterface {
  type: 'TTS';
  synthesize: (text: string) => Promise<ArrayBuffer>;
  supportedLanguages: string[];
  voiceOptions: string[];
}

interface LLMPlugin extends PluginInterface {
  type: 'LLM';
  predict: (message: string, history: any[], systemPrompt: string) => Promise<string> | AsyncGenerator<string>;
  isGenerator: boolean;
  maxTokens: number;
  temperature: number;
}

interface VRMPlugin extends PluginInterface {
  type: 'VRM';
  setAvatarData: (data: any) => void;
  updateExpression: (emotion: string) => void;
  updateMouthOpen: (value: number) => void;
}

type Plugin = TTSPlugin | LLMPlugin | VRMPlugin;

interface PluginSystemProps {
  onPluginChange?: (type: string, plugin: Plugin | null) => void;
  className?: string;
}

export default function PluginSystem({ onPluginChange, className = '' }: PluginSystemProps) {
  // LocalAIVtuber参考: プラグイン管理
  const [availablePlugins, setAvailablePlugins] = useState<Plugin[]>([]);
  const [activePlugins, setActivePlugins] = useState<Record<string, Plugin | null>>({
    TTS: null,
    LLM: null,
    VRM: null,
    VOICE: null,
    INTEGRATION: null
  });
  const [pluginConfigs, setPluginConfigs] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [selectedType, setSelectedType] = useState<string>('TTS');

  // aituber-kit参考: デフォルトプラグイン定義
  const defaultPlugins: Plugin[] = [
    {
      id: 'web-speech-tts',
      name: 'Web Speech TTS',
      version: '1.0.0',
      description: 'ブラウザ内蔵の音声合成API',
      type: 'TTS',
      enabled: true,
      config: { voice: 'default', rate: 1.0, pitch: 1.0 },
      synthesize: async (text: string) => {
        return new Promise((resolve) => {
          const utterance = new SpeechSynthesisUtterance(text);
          utterance.onend = () => {
            // ダミーのArrayBufferを返す（実際の実装では音声データを返す）
            resolve(new ArrayBuffer(0));
          };
          speechSynthesis.speak(utterance);
        });
      },
      supportedLanguages: ['ja-JP', 'en-US'],
      voiceOptions: ['default', 'female', 'male']
    } as TTSPlugin,
    
    {
      id: 'claude-llm',
      name: 'Claude LLM',
      version: '1.0.0',
      description: 'Claude APIを使用したLLM',
      type: 'LLM',
      enabled: true,
      config: { model: 'claude-3-haiku', temperature: 0.7, maxTokens: 1000 },
      predict: async (message: string, history: any[], systemPrompt: string) => {
        // 実際の実装ではClaude APIを呼び出し
        return `Claude応答: ${message}`;
      },
      isGenerator: false,
      maxTokens: 1000,
      temperature: 0.7
    } as LLMPlugin,
    
    {
      id: 'vrm-controller',
      name: 'VRM Controller',
      version: '1.0.0',
      description: 'VRMモデル制御プラグイン',
      type: 'VRM',
      enabled: true,
      config: { lipSyncSensitivity: 0.8, expressionIntensity: 1.0 },
      setAvatarData: (data: any) => {
        console.log('VRM Avatar Data:', data);
      },
      updateExpression: (emotion: string) => {
        console.log('VRM Expression:', emotion);
      },
      updateMouthOpen: (value: number) => {
        console.log('VRM Mouth Open:', value);
      }
    } as VRMPlugin
  ];

  // プラグイン初期化
  useEffect(() => {
    initializePlugins();
  }, []);

  const initializePlugins = useCallback(async () => {
    setIsLoading(true);
    
    try {
      // LocalAIVtuber参考: プラグイン読み込み
      const plugins = [...defaultPlugins];
      
      // 保存された設定を復元
      const savedConfigs = localStorage.getItem('meta-studio-plugin-configs');
      if (savedConfigs) {
        const configs = JSON.parse(savedConfigs);
        setPluginConfigs(configs);
        
        // プラグイン設定を適用
        plugins.forEach(plugin => {
          if (configs[plugin.id]) {
            plugin.config = { ...plugin.config, ...configs[plugin.id] };
          }
        });
      }
      
      setAvailablePlugins(plugins);
      
      // デフォルトアクティブプラグインを設定
      const newActivePlugins: Record<string, Plugin | null> = {};
      plugins.forEach(plugin => {
        if (plugin.enabled && !newActivePlugins[plugin.type]) {
          newActivePlugins[plugin.type] = plugin;
        }
      });
      
      setActivePlugins(newActivePlugins);
      
    } catch (error) {
      console.error('プラグイン初期化エラー:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // プラグイン切り替え
  const switchPlugin = useCallback((type: string, pluginId: string | null) => {
    const plugin = pluginId ? availablePlugins.find(p => p.id === pluginId) || null : null;
    
    setActivePlugins(prev => ({
      ...prev,
      [type]: plugin
    }));
    
    onPluginChange?.(type, plugin);
  }, [availablePlugins, onPluginChange]);

  // プラグイン設定更新
  const updatePluginConfig = useCallback((pluginId: string, config: Record<string, any>) => {
    setPluginConfigs(prev => {
      const newConfigs = {
        ...prev,
        [pluginId]: { ...prev[pluginId], ...config }
      };
      
      // 設定を保存
      localStorage.setItem('meta-studio-plugin-configs', JSON.stringify(newConfigs));
      
      return newConfigs;
    });
    
    // プラグインの設定を更新
    setAvailablePlugins(prev => prev.map(plugin => 
      plugin.id === pluginId 
        ? { ...plugin, config: { ...plugin.config, ...config } }
        : plugin
    ));
  }, []);

  // プラグイン有効/無効切り替え
  const togglePlugin = useCallback((pluginId: string) => {
    setAvailablePlugins(prev => prev.map(plugin => 
      plugin.id === pluginId 
        ? { ...plugin, enabled: !plugin.enabled }
        : plugin
    ));
  }, []);

  // プラグインタイプ別フィルタリング
  const getPluginsByType = useCallback((type: string) => {
    return availablePlugins.filter(plugin => plugin.type === type);
  }, [availablePlugins]);

  return (
    <div className={`plugin-system ${className}`}>
      <div className="flex flex-col h-full">
        {/* ヘッダー */}
        <div className="flex items-center justify-between p-4 border-b border-base-content/10">
          <div className="flex items-center gap-2">
            <Puzzle size={20} className="text-primary" />
            <h3 className="font-semibold">プラグインシステム</h3>
          </div>
          <div className="flex items-center gap-2">
            <button
              className="btn btn-sm btn-outline"
              onClick={initializePlugins}
              disabled={isLoading}
              title="プラグイン再読み込み"
            >
              <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
            </button>
          </div>
        </div>

        {/* プラグインタイプ選択 */}
        <div className="flex border-b border-base-content/10">
          {Object.keys(activePlugins).map(type => (
            <button
              key={type}
              className={`flex-1 p-3 text-sm font-medium transition-colors ${
                selectedType === type 
                  ? 'bg-primary text-primary-content' 
                  : 'hover:bg-base-200'
              }`}
              onClick={() => setSelectedType(type)}
            >
              {type}
              {activePlugins[type] && (
                <div className="w-2 h-2 bg-success rounded-full ml-2 inline-block"></div>
              )}
            </button>
          ))}
        </div>

        {/* プラグインリスト */}
        <div className="flex-1 overflow-y-auto p-4 space-y-3">
          {getPluginsByType(selectedType).map(plugin => (
            <div 
              key={plugin.id}
              className={`p-3 rounded-lg border transition-all ${
                activePlugins[selectedType]?.id === plugin.id
                  ? 'border-primary bg-primary/5'
                  : 'border-base-content/10 hover:border-base-content/20'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{plugin.name}</h4>
                    <span className="badge badge-outline badge-xs">{plugin.version}</span>
                    {plugin.enabled && (
                      <span className="badge badge-success badge-xs">有効</span>
                    )}
                  </div>
                  <p className="text-sm text-base-content/70 mt-1">{plugin.description}</p>
                  
                  {/* プラグイン設定 */}
                  <div className="mt-2 space-y-2">
                    {Object.entries(plugin.config).map(([key, value]) => (
                      <div key={key} className="flex items-center gap-2">
                        <label className="text-xs text-base-content/60 min-w-[4rem]">
                          {key}:
                        </label>
                        <input
                          type={typeof value === 'number' ? 'number' : 'text'}
                          value={value}
                          onChange={(e) => {
                            const newValue = typeof value === 'number' 
                              ? parseFloat(e.target.value) || 0
                              : e.target.value;
                            updatePluginConfig(plugin.id, { [key]: newValue });
                          }}
                          className="input input-xs input-bordered flex-1"
                        />
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="flex flex-col gap-1 ml-3">
                  <button
                    className={`btn btn-xs ${
                      activePlugins[selectedType]?.id === plugin.id
                        ? 'btn-error'
                        : 'btn-primary'
                    }`}
                    onClick={() => switchPlugin(
                      selectedType, 
                      activePlugins[selectedType]?.id === plugin.id ? null : plugin.id
                    )}
                    disabled={!plugin.enabled}
                  >
                    {activePlugins[selectedType]?.id === plugin.id ? (
                      <>
                        <Pause size={12} />
                        停止
                      </>
                    ) : (
                      <>
                        <Play size={12} />
                        開始
                      </>
                    )}
                  </button>
                  
                  <button
                    className="btn btn-xs btn-outline"
                    onClick={() => togglePlugin(plugin.id)}
                  >
                    {plugin.enabled ? '無効化' : '有効化'}
                  </button>
                </div>
              </div>
            </div>
          ))}
          
          {getPluginsByType(selectedType).length === 0 && (
            <div className="text-center py-8 text-base-content/50">
              {selectedType}プラグインがありません
            </div>
          )}
        </div>

        {/* 現在のアクティブプラグイン表示 */}
        <div className="border-t border-base-content/10 p-4">
          <div className="text-sm">
            <span className="text-base-content/60">アクティブ: </span>
            {activePlugins[selectedType] ? (
              <span className="font-medium text-primary">
                {activePlugins[selectedType]?.name}
              </span>
            ) : (
              <span className="text-base-content/50">なし</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
