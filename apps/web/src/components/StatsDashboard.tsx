'use client';

import { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Activity, Calendar, Users, Code, CheckCircle, AlertTriangle, Grid3X3, List, BarChart3 } from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';

interface StatItem {
  label: string;
  value: number | string;
  trend?: number;
  icon: any;
  color: string;
}

export default function StatsDashboard() {
  const { sendProjectNotification } = useNotifications();
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('month');
  const [viewType, setViewType] = useState<'grid' | 'list' | 'chart'>('grid');
  
  // シミュレートされた統計データ
  const [stats] = useState({
    projects: {
      total: 127,
      completed: 89,
      inProgress: 31,
      failed: 7,
      completionRate: 92,
    },
    agents: {
      total: 45,
      active: 38,
      efficiency: 85,
      tasksCompleted: 3421,
    },
    performance: {
      averageTime: '3.2日',
      speedImprovement: 18,
      codeQuality: 94,
      testCoverage: 87,
    },
    usage: {
      dailyActiveUsers: 156,
      totalCommits: 12543,
      linesOfCode: 2145632,
      deployments: 89,
    }
  });

  const mainStats: StatItem[] = [
    {
      label: '完成プロジェクト',
      value: stats.projects.completed,
      trend: 12,
      icon: CheckCircle,
      color: 'text-success'
    },
    {
      label: '実現率',
      value: `${stats.projects.completionRate}%`,
      trend: 5,
      icon: TrendingUp,
      color: 'text-primary'
    },
    {
      label: 'アクティブエージェント',
      value: stats.agents.active,
      trend: -2,
      icon: Users,
      color: 'text-secondary'
    },
    {
      label: '平均開発時間',
      value: stats.performance.averageTime,
      trend: -15,
      icon: Activity,
      color: 'text-accent'
    }
  ];

  // チャートデータのシミュレーション
  const chartData = {
    week: [65, 59, 80, 81, 56, 88, 92],
    month: [45, 52, 38, 65, 59, 80, 81, 78, 92, 88, 95, 89, 85, 92, 88, 94, 98, 102, 95, 110, 108, 115, 120, 118, 125, 122, 127, 130, 135, 132],
    year: [89, 92, 95, 88, 102, 108, 115, 122, 118, 125, 127, 132]
  };

  const projectTimeline = [
    { name: '瞑想アプリ', status: 'completed', date: '2日前', time: '2.5日' },
    { name: '投資bot', status: 'deployed', date: '5日前', time: '4.2日' },
    { name: 'iS_streamer', status: 'in_progress', date: '進行中', time: '1.8日経過' },
    { name: '天気予報API', status: 'completed', date: '1週間前', time: '1.2日' },
    { name: 'タスク管理', status: 'failed', date: '2週間前', time: '3.5日' },
  ];

  const getStatusBadge = (status: string) => {
    const badges = {
      completed: { class: 'badge-success', label: '完了' },
      deployed: { class: 'badge-primary', label: 'デプロイ済' },
      in_progress: { class: 'badge-warning', label: '進行中' },
      failed: { class: 'badge-error', label: '失敗' }
    };
    return badges[status as keyof typeof badges] || { class: 'badge-neutral', label: status };
  };

  // 模擬的なプロジェクト完了通知
  useEffect(() => {
    const timer = setTimeout(() => {
      sendProjectNotification('音声認識プラグイン', 'completed');
    }, 30000); // 30秒後に通知

    return () => clearTimeout(timer);
  }, [sendProjectNotification]);

  // リスト表示用のコンポーネント
  const renderListView = () => (
    <div className="space-y-4">
      {/* 統計サマリー（リスト形式） */}
      <div className="card bg-base-200 shadow-xl neo-depth">
        <div className="card-body">
          <h2 className="card-title">統計サマリー</h2>
          <div className="overflow-x-auto">
            <table className="table table-zebra">
              <thead>
                <tr>
                  <th>項目</th>
                  <th>現在値</th>
                  <th>変化率</th>
                  <th>ステータス</th>
                </tr>
              </thead>
              <tbody>
                {mainStats.map((stat) => (
                  <tr key={stat.label}>
                    <td className="flex items-center gap-2">
                      <stat.icon size={16} className={stat.color} />
                      {stat.label}
                    </td>
                    <td className="font-bold">{stat.value}</td>
                    <td>
                      {stat.trend && stat.trend > 0 ? (
                        <span className="text-success flex items-center gap-1">
                          <TrendingUp size={12} />
                          +{stat.trend}%
                        </span>
                      ) : stat.trend ? (
                        <span className="text-error flex items-center gap-1">
                          <TrendingDown size={12} />
                          {stat.trend}%
                        </span>
                      ) : (
                        <span className="text-base-content/50">-</span>
                      )}
                    </td>
                    <td>
                      <div className="badge badge-success badge-sm">正常</div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* 詳細項目（リスト形式） */}
      <div className="card bg-base-200 shadow-xl neo-depth">
        <div className="card-body">
          <h2 className="card-title">エージェント効率</h2>
          <div className="space-y-3">
            {[
              { name: '神（God）', efficiency: 98 },
              { name: '王（King）', efficiency: 92 },
              { name: '母（Mother）', efficiency: 95 },
              { name: '将（General）', efficiency: 85 },
              { name: '兵（Soldier）', efficiency: 78 }
            ].map((agent) => (
              <div key={agent.name} className="flex items-center justify-between p-2 bg-base-100 rounded">
                <span className="font-medium">{agent.name}</span>
                <div className="flex items-center gap-2">
                  <progress className="progress progress-primary w-20" value={agent.efficiency} max="100" />
                  <span className="text-sm font-bold">{agent.efficiency}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  // チャート表示用のコンポーネント
  const renderChartView = () => (
    <div className="space-y-4">
      <div className="alert alert-info">
        <Activity size={16} />
        <span>チャートビューは開発中です。現在はシンプルな進捗バーで表示しています。</span>
      </div>
      
      <div className="card bg-base-200 shadow-xl neo-depth">
        <div className="card-body">
          <h2 className="card-title">プロジェクト完成率</h2>
          <div className="h-64 flex items-end gap-2">
            {[
              { label: '完了', value: 89, color: 'bg-success' },
              { label: '進行中', value: 31, color: 'bg-primary' },
              { label: '失敗', value: 7, color: 'bg-error' }
            ].map((item) => (
              <div key={item.label} className="flex-1 flex flex-col items-center gap-2">
                <div 
                  className={`w-full ${item.color} rounded-t`}
                  style={{ height: `${(item.value / 100) * 200}px` }}
                />
                <div className="text-xs text-center">
                  <div className="font-bold">{item.value}</div>
                  <div>{item.label}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col neo-glass">
      {/* ヘッダー */}
      <div className="p-4 border-b border-base-content/10 bg-base-300/80">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold">統計サマリー</h1>
            <p className="text-sm text-base-content/70">プロジェクトとエージェントのパフォーマンス分析</p>
          </div>
          <div className="flex gap-4">
            {/* ビュータイプ選択 */}
            <div className="flex gap-1">
              {[
                { type: 'grid' as const, icon: Grid3X3, label: 'グリッド' },
                { type: 'list' as const, icon: List, label: 'リスト' },
                { type: 'chart' as const, icon: BarChart3, label: 'チャート' }
              ].map(({ type, icon: Icon, label }) => (
                <button
                  key={type}
                  className={`btn btn-sm neo-hover ${
                    viewType === type ? 'btn-primary' : 'btn-outline'
                  }`}
                  onClick={() => setViewType(type)}
                  title={label}
                >
                  <Icon size={16} />
                </button>
              ))}
            </div>
            
            {/* 時間範囲選択 */}
            <div className="flex gap-2">
              {(['week', 'month', 'year'] as const).map((range) => (
                <button
                  key={range}
                  className={`btn btn-sm neo-hover ${
                    timeRange === range ? 'btn-primary' : 'btn-outline'
                  }`}
                  onClick={() => setTimeRange(range)}
                >
                  {range === 'week' ? '週' : range === 'month' ? '月' : '年'}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* メイン統計 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {mainStats.map((stat) => (
            <div key={stat.label} className="stats shadow neo-depth">
              <div className="stat p-3">
                <div className="stat-figure text-primary">
                  <stat.icon size={24} className={stat.color} />
                </div>
                <div className="stat-title text-xs">{stat.label}</div>
                <div className="stat-value text-2xl">{stat.value}</div>
                <div className="stat-desc flex items-center gap-1">
                  {stat.trend && stat.trend > 0 ? (
                    <>
                      <TrendingUp size={12} className="text-success" />
                      <span className="text-success">+{stat.trend}%</span>
                    </>
                  ) : stat.trend ? (
                    <>
                      <TrendingDown size={12} className="text-error" />
                      <span className="text-error">{stat.trend}%</span>
                    </>
                  ) : null}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* コンテンツエリア */}
      <div className="flex-1 overflow-y-auto p-4">
        {viewType === 'list' && renderListView()}
        {viewType === 'chart' && renderChartView()}
        {viewType === 'grid' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          
          {/* プロジェクト完了チャート */}
          <div className="lg:col-span-2 card bg-base-200 shadow-xl neo-depth">
            <div className="card-body">
              <h2 className="card-title text-lg">プロジェクト完了推移</h2>
              <div className="h-64 flex items-end justify-between gap-1 p-4">
                {chartData[timeRange].map((value, index) => (
                  <div
                    key={index}
                    className="flex-1 bg-primary/80 rounded-t transition-all hover:bg-primary"
                    style={{ height: `${(value / 140) * 100}%` }}
                    title={`${value}件`}
                  />
                ))}
              </div>
              <div className="text-center text-sm text-base-content/60">
                {timeRange === 'week' ? '過去7日間' : 
                 timeRange === 'month' ? '過去30日間' : 
                 '過去12ヶ月'}
              </div>
            </div>
          </div>

          {/* エージェント効率 */}
          <div className="card bg-base-200 shadow-xl neo-depth">
            <div className="card-body">
              <h2 className="card-title text-lg">エージェント効率</h2>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm">王（King）</span>
                    <span className="text-sm font-bold">92%</span>
                  </div>
                  <progress className="progress progress-primary" value="92" max="100"></progress>
                </div>
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm">母（Mother）</span>
                    <span className="text-sm font-bold">95%</span>
                  </div>
                  <progress className="progress progress-secondary" value="95" max="100"></progress>
                </div>
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm">将（General）</span>
                    <span className="text-sm font-bold">85%</span>
                  </div>
                  <progress className="progress progress-accent" value="85" max="100"></progress>
                </div>
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm">兵（Soldier）</span>
                    <span className="text-sm font-bold">78%</span>
                  </div>
                  <progress className="progress progress-info" value="78" max="100"></progress>
                </div>
              </div>
            </div>
          </div>

          {/* 最近のプロジェクト */}
          <div className="lg:col-span-3 card bg-base-200 shadow-xl neo-depth">
            <div className="card-body">
              <h2 className="card-title text-lg mb-4">最近のプロジェクト</h2>
              <div className="overflow-x-auto">
                <table className="table table-zebra">
                  <thead>
                    <tr>
                      <th>プロジェクト名</th>
                      <th>ステータス</th>
                      <th>完了日</th>
                      <th>開発時間</th>
                    </tr>
                  </thead>
                  <tbody>
                    {projectTimeline.map((project, index) => (
                      <tr key={index}>
                        <td className="font-medium">{project.name}</td>
                        <td>
                          <div className={`badge ${getStatusBadge(project.status).class} badge-sm`}>
                            {getStatusBadge(project.status).label}
                          </div>
                        </td>
                        <td className="text-sm text-base-content/70">{project.date}</td>
                        <td className="text-sm">{project.time}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* パフォーマンス指標 */}
          <div className="lg:col-span-3 grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="card bg-base-200 shadow neo-depth">
              <div className="card-body p-4 text-center">
                <Code size={32} className="mx-auto text-primary mb-2" />
                <div className="text-2xl font-bold">{stats.usage.linesOfCode.toLocaleString()}</div>
                <div className="text-sm text-base-content/70">コード行数</div>
              </div>
            </div>
            <div className="card bg-base-200 shadow neo-depth">
              <div className="card-body p-4 text-center">
                <Activity size={32} className="mx-auto text-secondary mb-2" />
                <div className="text-2xl font-bold">{stats.usage.totalCommits.toLocaleString()}</div>
                <div className="text-sm text-base-content/70">総コミット数</div>
              </div>
            </div>
            <div className="card bg-base-200 shadow neo-depth">
              <div className="card-body p-4 text-center">
                <CheckCircle size={32} className="mx-auto text-success mb-2" />
                <div className="text-2xl font-bold">{stats.performance.testCoverage}%</div>
                <div className="text-sm text-base-content/70">テストカバレッジ</div>
              </div>
            </div>
            <div className="card bg-base-200 shadow neo-depth">
              <div className="card-body p-4 text-center">
                <AlertTriangle size={32} className="mx-auto text-warning mb-2" />
                <div className="text-2xl font-bold">{stats.projects.failed}</div>
                <div className="text-sm text-base-content/70">失敗プロジェクト</div>
              </div>
            </div>
          </div>
        </div>
        )}
      </div>
    </div>
  );
}