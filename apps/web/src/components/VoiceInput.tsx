'use client';

import { useState, useEffect, useRef } from 'react';
import { Mi<PERSON>, MicOff, Play, Pause, Square, Save, Trash2 } from 'lucide-react';

interface VoiceRecord {
  id: string;
  text: string;
  timestamp: Date;
  category: 'idea' | 'requirement' | 'note' | 'task';
  audioBlob?: Blob;
}

export default function VoiceInput() {
  const [isRecording, setIsRecording] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [currentText, setCurrentText] = useState('');
  const [records, setRecords] = useState<VoiceRecord[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<VoiceRecord['category']>('idea');
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recognitionRef = useRef<any>(null);

  // Web Speech API の初期化
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      
      if (SpeechRecognition) {
        const recognition = new SpeechRecognition();
        recognition.continuous = true;
        recognition.interimResults = true;
        recognition.lang = 'ja-JP';
        recognition.maxAlternatives = 1;

        recognition.onstart = () => {
          console.log('音声認識が開始されました');
          setIsListening(true);
        };

        recognition.onresult = (event: any) => {
          let finalTranscript = '';
          let interimTranscript = '';

          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            if (event.results[i].isFinal) {
              finalTranscript += transcript;
            } else {
              interimTranscript += transcript;
            }
          }

          const combinedText = finalTranscript + interimTranscript;
          console.log('音声認識結果:', combinedText);
          setCurrentText(combinedText);
        };

        recognition.onerror = (event: any) => {
          console.error('音声認識エラー:', event.error);
          setIsListening(false);
          
          if (event.error === 'not-allowed') {
            alert('音声認識が許可されていません。ブラウザの設定を確認してください。');
          } else if (event.error === 'network') {
            alert('ネットワークエラーが発生しました。');
          } else if (event.error === 'no-speech') {
            console.log('音声が検出されませんでした');
          }
        };

        recognition.onend = () => {
          console.log('音声認識が終了しました');
          setIsListening(false);
        };

        recognitionRef.current = recognition;
        console.log('音声認識が初期化されました');
      } else {
        console.warn('このブラウザは音声認識をサポートしていません');
      }
    }
  }, []);

  const startRecording = async () => {
    try {
      // マイクアクセス許可をチェック
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('このブラウザはマイク機能をサポートしていません');
      }

      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } 
      });
      
      // MediaRecorder setup with error handling
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: MediaRecorder.isTypeSupported('audio/webm') ? 'audio/webm' : 'audio/mp4'
      });
      
      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder エラー:', event);
        setIsRecording(false);
      };

      mediaRecorder.onstart = () => {
        console.log('MediaRecorder 開始');
        setIsRecording(true);
      };

      mediaRecorder.onstop = () => {
        console.log('MediaRecorder 停止');
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current = mediaRecorder;
      
      // Speech Recognition start with error handling
      if (recognitionRef.current) {
        try {
          recognitionRef.current.start();
          setIsListening(true);
          console.log('音声認識開始');
        } catch (speechError) {
          console.error('音声認識開始エラー:', speechError);
        }
      } else {
        console.warn('音声認識が利用できません');
      }

      mediaRecorder.start();
      setCurrentText('');
      
    } catch (error) {
      console.error('録音開始エラー:', error);
      setIsRecording(false);
      setIsListening(false);
      
      if (error.name === 'NotAllowedError') {
        alert('マイクへのアクセスが拒否されました。ブラウザの設定でマイクを許可してください。');
      } else if (error.name === 'NotFoundError') {
        alert('マイクが見つかりません。マイクが接続されていることを確認してください。');
      } else {
        alert(`録音エラー: ${error.message}`);
      }
    }
  };

  const stopRecording = () => {
    console.log('停止処理開始');
    
    if (mediaRecorderRef.current && isRecording) {
      try {
        mediaRecorderRef.current.stop();
        console.log('MediaRecorder停止');
      } catch (error) {
        console.error('MediaRecorder停止エラー:', error);
      }
      setIsRecording(false);
    }

    if (recognitionRef.current && isListening) {
      try {
        recognitionRef.current.stop();
        console.log('音声認識停止');
      } catch (error) {
        console.error('音声認識停止エラー:', error);
      }
      setIsListening(false);
    }
  };

  const saveRecord = () => {
    if (currentText.trim()) {
      const newRecord: VoiceRecord = {
        id: Date.now().toString(),
        text: currentText.trim(),
        timestamp: new Date(),
        category: selectedCategory,
      };

      setRecords(prev => [newRecord, ...prev]);
      setCurrentText('');
    }
  };

  const deleteRecord = (id: string) => {
    setRecords(prev => prev.filter(record => record.id !== id));
  };

  const getCategoryIcon = (category: VoiceRecord['category']) => {
    switch (category) {
      case 'idea': return '💡';
      case 'requirement': return '📋';
      case 'note': return '📝';
      case 'task': return '✅';
      default: return '💭';
    }
  };

  const getCategoryColor = (category: VoiceRecord['category']) => {
    switch (category) {
      case 'idea': return 'badge-warning';
      case 'requirement': return 'badge-primary';
      case 'note': return 'badge-info';
      case 'task': return 'badge-success';
      default: return 'badge-neutral';
    }
  };

  return (
    <div className="h-full flex flex-col neo-glass">
      {/* ヘッダー */}
      <div className="p-4 border-b border-base-content/10 bg-base-300/80">
        <div className="flex items-center gap-3 mb-3">
          <div className="text-lg font-semibold">🎤 音声インボックス</div>
          <div className="text-xs text-base-content/70">アイデア自動キャプチャ</div>
        </div>

        {/* カテゴリー選択 */}
        <div className="flex gap-2 mb-3">
          {(['idea', 'requirement', 'note', 'task'] as const).map((cat) => (
            <button
              key={cat}
              onClick={() => setSelectedCategory(cat)}
              className={`btn btn-xs neo-hover ${
                selectedCategory === cat ? 'btn-primary' : 'btn-outline'
              }`}
            >
              {getCategoryIcon(cat)}
              {cat === 'idea' ? 'アイデア' : 
               cat === 'requirement' ? '要件' :
               cat === 'note' ? 'メモ' : 'タスク'}
            </button>
          ))}
        </div>

        {/* 録音コントロール */}
        <div className="flex items-center gap-2">
          <button
            onClick={isRecording ? stopRecording : startRecording}
            className={`btn btn-sm neo-hover ${
              isRecording ? 'btn-error animate-pulse' : 'btn-primary'
            }`}
          >
            {isRecording ? <MicOff size={16} /> : <Mic size={16} />}
            {isRecording ? '停止' : '録音開始'}
          </button>

          {currentText && (
            <button
              onClick={saveRecord}
              className="btn btn-sm btn-success neo-hover"
            >
              <Save size={16} />
              保存
            </button>
          )}

          <div className="flex items-center gap-1 text-xs text-base-content/70">
            <div className={`w-2 h-2 rounded-full ${
              isListening ? 'bg-success animate-pulse' : 'bg-base-content/30'
            }`}></div>
            {isListening ? '音声認識中...' : '待機中'}
          </div>
        </div>
      </div>

      {/* 現在の入力テキスト */}
      {currentText && (
        <div className="p-4 bg-primary/10 border-b border-base-content/10">
          <div className="text-sm text-base-content/70 mb-1">認識中のテキスト:</div>
          <div className="p-3 bg-base-100/50 rounded-lg neo-depth">
            {currentText}
          </div>
        </div>
      )}

      {/* 保存された記録一覧 */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-3">
          {records.length === 0 ? (
            <div className="text-center py-8 text-base-content/50">
              <Mic size={48} className="mx-auto mb-2 opacity-50" />
              <p>まだ音声記録がありません</p>
              <p className="text-xs">マイクボタンで録音を開始してください</p>
            </div>
          ) : (
            records.map((record) => (
              <div key={record.id} className="p-3 neo-depth rounded-lg hover:shadow-lg transition-all">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className={`badge ${getCategoryColor(record.category)} badge-sm`}>
                      {getCategoryIcon(record.category)}
                      {record.category === 'idea' ? 'アイデア' : 
                       record.category === 'requirement' ? '要件' :
                       record.category === 'note' ? 'メモ' : 'タスク'}
                    </div>
                    <div className="text-xs text-base-content/70">
                      {record.timestamp.toLocaleString('ja-JP')}
                    </div>
                  </div>
                  <button
                    onClick={() => deleteRecord(record.id)}
                    className="btn btn-xs btn-ghost btn-error neo-hover"
                  >
                    <Trash2 size={12} />
                  </button>
                </div>
                <p className="text-sm leading-relaxed">{record.text}</p>
              </div>
            ))
          )}
        </div>
      </div>

      {/* ステータスバー */}
      <div className="p-2 border-t border-base-content/10 bg-base-200/50">
        <div className="flex items-center justify-between text-xs text-base-content/70">
          <div>記録数: {records.length}</div>
          <div className="flex items-center gap-2">
            <span>自動分類: ON</span>
            <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>
    </div>
  );
}