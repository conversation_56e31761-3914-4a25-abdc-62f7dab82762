'use client';

import { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

export interface ContextMenuItem {
  id: string;
  label: string;
  icon?: string;
  action: () => void;
  disabled?: boolean;
  danger?: boolean;
  separator?: boolean;
  submenu?: ContextMenuItem[];
}

interface ContextMenuProps {
  items: ContextMenuItem[];
  x: number;
  y: number;
  onClose: () => void;
  visible: boolean;
}

export function ContextMenu({ items, x, y, onClose, visible }: ContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);
  const [submenuOpen, setSubmenuOpen] = useState<string | null>(null);
  const [submenuPosition, setSubmenuPosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    if (!visible) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [visible, onClose]);

  // 画面端での位置調整
  useEffect(() => {
    if (!visible || !menuRef.current) return;

    const menu = menuRef.current;
    const rect = menu.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let adjustedX = x;
    let adjustedY = y;

    // 右端を超える場合は左に配置
    if (x + rect.width > viewportWidth) {
      adjustedX = viewportWidth - rect.width - 10;
    }

    // 下端を超える場合は上に配置
    if (y + rect.height > viewportHeight) {
      adjustedY = viewportHeight - rect.height - 10;
    }

    menu.style.left = `${Math.max(0, adjustedX)}px`;
    menu.style.top = `${Math.max(0, adjustedY)}px`;
  }, [visible, x, y]);

  const handleItemClick = (item: ContextMenuItem, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    
    console.log('Context menu item clicked:', item.label, item);
    
    if (item.disabled) {
      console.log('Item is disabled, ignoring click');
      return;
    }
    
    if (item.submenu) {
      console.log('Opening submenu for:', item.label);
      const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
      setSubmenuPosition({
        x: rect.right,
        y: rect.top
      });
      setSubmenuOpen(submenuOpen === item.id ? null : item.id);
      return;
    }

    console.log('Executing action for:', item.label);
    try {
      item.action();
      console.log('Action executed successfully');
    } catch (error) {
      console.error('Error executing action:', error);
    }
    onClose();
  };

  const handleItemHover = (item: ContextMenuItem, event: React.MouseEvent) => {
    if (item.submenu) {
      const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
      setSubmenuPosition({
        x: rect.right,
        y: rect.top
      });
      setSubmenuOpen(item.id);
    } else if (!item.submenu) {
      setSubmenuOpen(null);
    }
  };

  if (!visible) return null;

  const menu = (
    <div
      ref={menuRef}
      className="fixed z-[9999] bg-base-100 shadow-2xl border border-base-content/20 rounded-lg py-2 min-w-48 neo-depth"
      style={{ left: x, top: y }}
      onClick={(e) => e.stopPropagation()}
    >
      {items.map((item, index) => (
        <div key={item.id || index}>
          {item.separator ? (
            <div className="h-px bg-base-content/20 my-1" />
          ) : (
            <div
              className={`
                flex items-center gap-3 px-4 py-2.5 cursor-pointer transition-all duration-200 text-sm rounded-lg mx-1 relative
                ${item.disabled 
                  ? 'text-base-content/40 cursor-not-allowed' 
                  : item.danger 
                    ? 'hover:bg-error/30 hover:text-error-content hover:shadow-lg hover:scale-[1.05] border-l-4 border-transparent hover:border-error hover:bg-gradient-to-r hover:from-error/20 hover:to-error/10 hover:font-semibold hover:tracking-wide' 
                    : 'hover:bg-primary/30 hover:text-primary-content hover:shadow-lg hover:scale-[1.05] border-l-4 border-transparent hover:border-primary text-base-content hover:bg-gradient-to-r hover:from-primary/20 hover:to-primary/10 hover:font-semibold hover:tracking-wide'
                }
                ${item.submenu ? 'justify-between' : ''}
                group
              `}
              onClick={(e) => handleItemClick(item, e)}
              onMouseEnter={(e) => handleItemHover(item, e)}
              onMouseLeave={() => !item.submenu && setSubmenuOpen(null)}
            >
              <div className="flex items-center gap-3">
                {item.icon && (
                  <span className="text-base w-4 h-4 flex items-center justify-center transition-transform duration-200 group-hover:scale-110">
                    {item.icon}
                  </span>
                )}
                <span className="font-medium transition-all duration-200 group-hover:font-semibold">{item.label}</span>
              </div>
              {item.submenu && (
                <span className="text-base-content/60 transition-all duration-200 group-hover:text-primary group-hover:translate-x-1">▶</span>
              )}
              
              {/* 強化されたホバーエフェクト */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/15 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 rounded-lg" />
              <div className="absolute left-0 top-0 bottom-0 w-1 bg-primary opacity-0 group-hover:opacity-100 transition-all duration-300 rounded-r" />
            </div>
          )}
        </div>
      ))}

      {/* サブメニュー */}
      {submenuOpen && items.find(item => item.id === submenuOpen)?.submenu && (
        <ContextMenu
          items={items.find(item => item.id === submenuOpen)!.submenu!}
          x={submenuPosition.x}
          y={submenuPosition.y}
          onClose={() => setSubmenuOpen(null)}
          visible={true}
        />
      )}
    </div>
  );

  return createPortal(menu, document.body);
}

// コンテキストメニューを管理するカスタムフック
export function useContextMenu() {
  const [contextMenu, setContextMenu] = useState<{
    items: ContextMenuItem[];
    x: number;
    y: number;
    visible: boolean;
  }>({
    items: [],
    x: 0,
    y: 0,
    visible: false
  });

  const showContextMenu = (
    event: React.MouseEvent,
    items: ContextMenuItem[]
  ) => {
    event.preventDefault();
    event.stopPropagation();
    
    console.log('Context menu triggered at:', event.clientX, event.clientY, 'with items:', items);
    
    setContextMenu({
      items,
      x: event.clientX,
      y: event.clientY,
      visible: true
    });
  };

  const hideContextMenu = () => {
    setContextMenu(prev => ({ ...prev, visible: false }));
  };

  const ContextMenuComponent = () => (
    <ContextMenu
      items={contextMenu.items}
      x={contextMenu.x}
      y={contextMenu.y}
      onClose={hideContextMenu}
      visible={contextMenu.visible}
    />
  );

  return {
    showContextMenu,
    hideContextMenu,
    ContextMenuComponent
  };
}