'use client';

import { useEffect, useRef, useState, useCallback } from 'react';

interface FaceTrackingData {
  happy: number;
  angry: number;
  sad: number;
  relaxed: number;
  surprised: number;
  aa: number;
  ih: number;
  ou: number;
  ee: number;
  oh: number;
  eyeBlinkLeft: number;
  eyeBlinkRight: number;
  headRotationX: number;
  headRotationY: number;
  headRotationZ: number;
}

interface VRMViewerProps {
  vrmUrl?: string;
  vrmArrayBuffer?: ArrayBuffer; // ArrayBufferデータ
  agentId: string;
  lipSyncValue?: number;
  expressionValue?: number;
  isActive?: boolean;
  className?: string;
  showControls?: boolean;
  faceTrackingData?: FaceTrackingData | null;
  onVRMUpload?: (file: File) => void;
}

// パフォーマンス設定
const PERFORMANCE_CONFIG = {
  TARGET_FPS: 60,
  MAX_TEXTURE_SIZE: 1024,
  ANIMATION_ENABLED: true,
  SHADOW_MAP_SIZE: 512,
  FRUSTUM_CULLING: true,
  LOD_ENABLED: true
};

// LocalAIVtuber & aituber-kit参考: 高度なVRM制御
const VRM_CONTROL_CONFIG = {
  MOUTH_SYNC_SENSITIVITY: 0.8,
  EXPRESSION_INTENSITY: 1.0,
  BLINK_FREQUENCY: 3000, // 3秒間隔
  IDLE_ANIMATION_SPEED: 0.5,
  EMOTION_TRANSITION_SPEED: 0.3,
  LIP_SYNC_SMOOTHING: 0.7
};

// 感情表現マッピング（LocalAIVtuber参考）
const EMOTION_EXPRESSIONS = {
  neutral: { happy: 0, angry: 0, sad: 0, surprised: 0, relaxed: 0.3 },
  happy: { happy: 1.0, angry: 0, sad: 0, surprised: 0, relaxed: 0 },
  sad: { happy: 0, angry: 0, sad: 1.0, surprised: 0, relaxed: 0 },
  angry: { happy: 0, angry: 1.0, sad: 0, surprised: 0, relaxed: 0 },
  surprised: { happy: 0, angry: 0, sad: 0, surprised: 1.0, relaxed: 0 },
  thinking: { happy: 0, angry: 0, sad: 0.2, surprised: 0, relaxed: 0.8 },
  speaking: { happy: 0.3, angry: 0, sad: 0, surprised: 0, relaxed: 0.2 }
};

// Three.jsライブラリキャッシュ
let THREE_CACHE: any = null;
let GLTFLoader_CACHE: any = null;
let VRMLoader_CACHE: any = null;

export default function VRMViewer({
  vrmUrl,
  vrmArrayBuffer,
  agentId,
  lipSyncValue = 80,
  expressionValue = 90,
  isActive = false,
  className = "",
  showControls = true,
  faceTrackingData,
  onVRMUpload,
  // LocalAIVtuber & aituber-kit参考: 高度な制御
  emotion = 'neutral',
  voiceVolume = 0,
  enableAutoBlinking = true,
  enableIdleAnimation = true,
  onExpressionChange
}: VRMViewerProps & {
  emotion?: keyof typeof EMOTION_EXPRESSIONS;
  voiceVolume?: number;
  enableAutoBlinking?: boolean;
  enableIdleAnimation?: boolean;
  onExpressionChange?: (expression: string, intensity: number) => void;
}) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [modelLoaded, setModelLoaded] = useState(false);
  const [viewMode, setViewMode] = useState<'bust' | 'full'>('bust');
  const [controlsExpanded, setControlsExpanded] = useState(false);
  const sceneRef = useRef<any>(null);
  const rendererRef = useRef<any>(null);
  const vrmRef = useRef<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const loadingRef = useRef(false);

  // レンダリング制御のためのrefs
  const animationIdRef = useRef<number | null>(null);
  const lastFrameTimeRef = useRef(0);
  const needsRenderRef = useRef(true);
  const performanceRef = useRef({ frameCount: 0, lastFpsCheck: 0, fps: 60 });
  const previousVrmUrlRef = useRef<string | null>(null);
  const currentAgentIdRef = useRef<string>(agentId);

  // LocalAIVtuber参考: 高度なVRM制御
  const [currentEmotion, setCurrentEmotion] = useState<keyof typeof EMOTION_EXPRESSIONS>('neutral');
  const [blinkTimer, setBlinkTimer] = useState(0);
  const [idleAnimationTime, setIdleAnimationTime] = useState(0);
  const emotionTransitionRef = useRef<Record<string, number>>({});
  const lastBlinkTimeRef = useRef(0);
  const lipSyncSmoothingRef = useRef(0);

  // マークレンダリング要求（最優先で定義）
  const markNeedsRender = useCallback(() => {
    needsRenderRef.current = true;
  }, []);

  // LocalAIVtuber参考: 感情表現制御
  const updateVRMExpression = useCallback((emotion: keyof typeof EMOTION_EXPRESSIONS, intensity: number = 1.0) => {
    if (!vrmRef.current?.vrm?.expressionManager) return;

    try {
      const expressionManager = vrmRef.current.vrm.expressionManager;
      const targetExpressions = EMOTION_EXPRESSIONS[emotion];

      // 現在の表情から目標表情へのスムーズな遷移
      Object.entries(targetExpressions).forEach(([expressionName, targetValue]) => {
        const currentValue = emotionTransitionRef.current[expressionName] || 0;
        const newValue = currentValue + (targetValue * intensity - currentValue) * VRM_CONTROL_CONFIG.EMOTION_TRANSITION_SPEED;

        emotionTransitionRef.current[expressionName] = newValue;
        expressionManager.setValue(expressionName, newValue);
      });

      expressionManager.update();
      onExpressionChange?.(emotion, intensity);
      markNeedsRender();

    } catch (error) {
      console.warn('VRM表情更新エラー:', error);
    }
  }, [markNeedsRender, onExpressionChange]);

  // LocalAIVtuber参考: リップシンク制御
  const updateLipSync = useCallback((volume: number) => {
    if (!vrmRef.current?.vrm?.expressionManager) return;

    try {
      const expressionManager = vrmRef.current.vrm.expressionManager;

      // 音量に基づく口の開き具合（スムージング適用）
      const targetMouthOpen = Math.min(volume * VRM_CONTROL_CONFIG.MOUTH_SYNC_SENSITIVITY, 1.0);
      lipSyncSmoothingRef.current += (targetMouthOpen - lipSyncSmoothingRef.current) * VRM_CONTROL_CONFIG.LIP_SYNC_SMOOTHING;

      // VRM標準の口形状を適用
      expressionManager.setValue('aa', lipSyncSmoothingRef.current * 0.8);
      expressionManager.setValue('ih', lipSyncSmoothingRef.current * 0.3);
      expressionManager.setValue('ou', lipSyncSmoothingRef.current * 0.5);
      expressionManager.setValue('ee', lipSyncSmoothingRef.current * 0.2);
      expressionManager.setValue('oh', lipSyncSmoothingRef.current * 0.6);

      expressionManager.update();
      markNeedsRender();

    } catch (error) {
      console.warn('VRMリップシンクエラー:', error);
    }
  }, [markNeedsRender]);

  // LocalAIVtuber参考: 自動まばたき（点滅修正）
  const updateAutoBlinking = useCallback((deltaTime: number) => {
    if (!enableAutoBlinking || !vrmRef.current?.vrm?.expressionManager) return;

    const now = Date.now();

    try {
      const expressionManager = vrmRef.current.vrm.expressionManager;

      // まばたき開始判定
      if (blinkTimer === 0 && now - lastBlinkTimeRef.current > VRM_CONTROL_CONFIG.BLINK_FREQUENCY + Math.random() * 2000) {
        setBlinkTimer(0.01); // まばたき開始
      }

      // まばたきアニメーション実行中
      if (blinkTimer > 0) {
        const blinkProgress = blinkTimer / 0.3; // 0.3秒でまばたき完了
        let blinkIntensity = 0;

        if (blinkProgress < 0.5) {
          // 閉じる（0→1）
          blinkIntensity = Math.sin(blinkProgress * Math.PI);
        } else {
          // 開く（1→0）
          blinkIntensity = Math.sin((1 - blinkProgress) * Math.PI);
        }

        // まばたき表情を適用
        expressionManager.setValue('blink', blinkIntensity);
        expressionManager.setValue('blinkLeft', blinkIntensity);
        expressionManager.setValue('blinkRight', blinkIntensity);

        setBlinkTimer(prev => prev + deltaTime);

        if (blinkTimer >= 0.3) { // まばたき完了
          setBlinkTimer(0);
          lastBlinkTimeRef.current = now;

          // まばたき表情をリセット
          expressionManager.setValue('blink', 0);
          expressionManager.setValue('blinkLeft', 0);
          expressionManager.setValue('blinkRight', 0);
        }
      }

    } catch (error) {
      console.warn('VRM自動まばたきエラー:', error);
    }
  }, [enableAutoBlinking, blinkTimer]);

  // LocalAIVtuber参考: アイドルアニメーション
  const updateIdleAnimation = useCallback((deltaTime: number) => {
    if (!enableIdleAnimation || !vrmRef.current?.vrm?.humanoid) return;

    try {
      const humanoid = vrmRef.current.vrm.humanoid;
      const time = idleAnimationTime * VRM_CONTROL_CONFIG.IDLE_ANIMATION_SPEED;

      // 軽微な呼吸アニメーション
      const breathingIntensity = Math.sin(time * 2) * 0.02;
      const chestBone = humanoid.getNormalizedBoneNode('chest');
      if (chestBone) {
        chestBone.rotation.x = breathingIntensity;
      }

      // 軽微な頭の動き
      const headSway = Math.sin(time * 0.5) * 0.01;
      const headBone = humanoid.getNormalizedBoneNode('head');
      if (headBone) {
        headBone.rotation.y = headSway;
      }

      setIdleAnimationTime(prev => prev + deltaTime);
      markNeedsRender();

    } catch (error) {
      console.warn('VRMアイドルアニメーションエラー:', error);
    }
  }, [enableIdleAnimation, idleAnimationTime, markNeedsRender]);

  // フェイストラッキングデータをVRMに適用
  const applyFaceTrackingToVRM = useCallback((trackingData: FaceTrackingData) => {
    if (!vrmRef.current?.vrm?.expressionManager) return;
    
    try {
      const expressionManager = vrmRef.current.vrm.expressionManager;
      
      // VRM標準表情の適用
      if (trackingData.happy > 0.1) {
        expressionManager.setValue('happy', trackingData.happy);
      }
      if (trackingData.angry > 0.1) {
        expressionManager.setValue('angry', trackingData.angry);
      }
      if (trackingData.sad > 0.1) {
        expressionManager.setValue('sad', trackingData.sad);
      }
      if (trackingData.surprised > 0.1) {
        expressionManager.setValue('surprised', trackingData.surprised);
      }
      
      // 口の形状（リップシンク）
      if (trackingData.aa > 0.1) {
        expressionManager.setValue('aa', trackingData.aa);
      }
      if (trackingData.ih > 0.1) {
        expressionManager.setValue('ih', trackingData.ih);
      }
      if (trackingData.ou > 0.1) {
        expressionManager.setValue('ou', trackingData.ou);
      }
      if (trackingData.ee > 0.1) {
        expressionManager.setValue('ee', trackingData.ee);
      }
      if (trackingData.oh > 0.1) {
        expressionManager.setValue('oh', trackingData.oh);
      }
      
      // まばたき
      const blinkValue = Math.max(trackingData.eyeBlinkLeft, trackingData.eyeBlinkRight);
      if (blinkValue > 0.1) {
        expressionManager.setValue('blink', blinkValue);
        expressionManager.setValue('blinkLeft', trackingData.eyeBlinkLeft);
        expressionManager.setValue('blinkRight', trackingData.eyeBlinkRight);
      }
      
      // 頭の回転
      if (vrmRef.current.vrm.humanoid?.getNormalizedBoneNode('head')) {
        const headBone = vrmRef.current.vrm.humanoid.getNormalizedBoneNode('head');
        if (headBone) {
          headBone.rotation.x = trackingData.headRotationX * 0.5; // 回転を調整
          headBone.rotation.y = trackingData.headRotationY * 0.5;
          headBone.rotation.z = trackingData.headRotationZ * 0.3;
        }
      }
      
      // 表情の更新を適用
      expressionManager.update();
      
      // レンダリング要求
      markNeedsRender();
      
    } catch (error) {
      console.warn('フェイストラッキング適用エラー:', error);
    }
  }, [markNeedsRender]);

  // テクスチャ圧縮とロード最適化
  const optimizeTexture = useCallback((texture: any, THREE: any) => {
    if (!texture) return texture;
    
    // テクスチャサイズ制限
    const maxSize = PERFORMANCE_CONFIG.MAX_TEXTURE_SIZE;
    if (texture.image) {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (ctx && (texture.image.width > maxSize || texture.image.height > maxSize)) {
        const scale = Math.min(maxSize / texture.image.width, maxSize / texture.image.height);
        canvas.width = texture.image.width * scale;
        canvas.height = texture.image.height * scale;
        ctx.drawImage(texture.image, 0, 0, canvas.width, canvas.height);
        texture.image = canvas;
      }
    }
    
    texture.generateMipmaps = true;
    texture.minFilter = THREE.LinearMipmapLinearFilter;
    texture.magFilter = THREE.LinearFilter;
    texture.format = THREE.RGBAFormat;
    texture.flipY = false;
    
    return texture;
  }, []);

  // ジオメトリ最適化
  const optimizeGeometry = useCallback((geometry: any) => {
    if (!geometry) return geometry;
    
    const unnecessaryAttributes = ['uv2', 'uv3', 'color2'];
    unnecessaryAttributes.forEach(attr => {
      if (geometry.attributes[attr]) {
        geometry.deleteAttribute(attr);
      }
    });
    
    return geometry;
  }, []);

  // モデルクリア関数（安定化）
  const clearCurrentModel = useCallback(() => {
    if (vrmRef.current && sceneRef.current) {
      try {
        // シーンからモデルを安全に削除
        if (vrmRef.current.scene && vrmRef.current.scene.parent) {
          sceneRef.current.remove(vrmRef.current.scene);
        }
        
        // モデルのリソース解放
        if (vrmRef.current.scene) {
          vrmRef.current.scene.traverse((object: any) => {
            if (object.geometry) {
              object.geometry.dispose();
            }
            if (object.material) {
              if (Array.isArray(object.material)) {
                object.material.forEach((mat: any) => {
                  if (mat.map) mat.map.dispose();
                  if (mat.normalMap) mat.normalMap.dispose();
                  if (mat.roughnessMap) mat.roughnessMap.dispose();
                  if (mat.metalnessMap) mat.metalnessMap.dispose();
                  mat.dispose();
                });
              } else {
                if (object.material.map) object.material.map.dispose();
                if (object.material.normalMap) object.material.normalMap.dispose();
                if (object.material.roughnessMap) object.material.roughnessMap.dispose();
                if (object.material.metalnessMap) object.material.metalnessMap.dispose();
                object.material.dispose();
              }
            }
          });
        }
        
        vrmRef.current = null;
        setModelLoaded(false);
        markNeedsRender();
        console.log(`🧹 VRMモデルをクリア: ${agentId}`);
      } catch (error) {
        console.error(`⚠️ モデルクリア中にエラー: ${agentId}`, error);
        vrmRef.current = null;
        setModelLoaded(false);
      }
    }
  }, [markNeedsRender, agentId]);

  // VRMファイルのバージョン検証関数
  const detectVRMVersion = useCallback((arrayBuffer: ArrayBuffer): 'vrm0' | 'vrm1' | 'gltf2' | 'unknown' => {
    try {
      if (!arrayBuffer || arrayBuffer.byteLength === 0) {
        console.error(`❌ ArrayBufferが空: ${agentId}`);
        return 'unknown';
      }

      const uint8Array = new Uint8Array(arrayBuffer, 0, Math.min(1024, arrayBuffer.byteLength));
      
      // バイナリglTFファイルかチェック（最初の4バイトが"glTF"）
      if (uint8Array[0] === 0x67 && uint8Array[1] === 0x6C && uint8Array[2] === 0x54 && uint8Array[3] === 0x46) {
        console.log(`📦 バイナリglTF形式検出: ${agentId}`);
        return 'gltf2'; // バイナリはたいていglTF 2.0
      }

      // JSONテキストとして解析
      const jsonString = new TextDecoder().decode(uint8Array);
      console.log(`🔍 VRMバージョン検出: ${agentId} - ファイルサイズ: ${arrayBuffer.byteLength} bytes`);
      
      // JSONが含まれているかチェック
      if (jsonString.includes('"asset"')) {
        if (jsonString.includes('"version":"2.') || jsonString.includes('"version": "2.')) {
          if (jsonString.includes('"VRM"') || jsonString.includes('"VRMC_')) {
            console.log(`✅ VRM 1.0 (glTF 2.0ベース) 検出: ${agentId}`);
            return 'vrm1';
          } else {
            console.log(`✅ 標準glTF 2.0 検出: ${agentId}`);
            return 'gltf2';
          }
        } else if (jsonString.includes('"version":"1.') || jsonString.includes('"version": "1.')) {
          console.log(`✅ VRM 0.x (glTF 1.0ベース) 検出: ${agentId}`);
          return 'vrm0';
        }
      }
      
      console.warn(`⚠️ VRMバージョン不明: ${agentId} - JSONヘッダーが見つからない`);
      return 'unknown';
    } catch (error) {
      console.error(`❌ VRMバージョン検出エラー: ${agentId}`, error);
      return 'unknown';
    }
  }, [agentId]);

  // VRM読み込み関数（改善版・aituber-kit参考）
  const loadVRM = useCallback(async (retryCount = 0) => {
    if ((!vrmUrl && !vrmArrayBuffer) || loadingRef.current) {
      console.log(`⏸️ VRM読み込みスキップ: ${agentId} - データなし`);
      return;
    }

    if (vrmArrayBuffer && !vrmArrayBuffer.byteLength) {
      console.error(`❌ VRM読み込み中止: ${agentId} - ArrayBufferが無効`);
      setError('VRMファイルが正しく読み込まれていません');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      loadingRef.current = true;

      console.log(`🔄 VRM読み込み開始: ${agentId}`);
      console.log(`📋 データ種類: ${vrmArrayBuffer ? `ArrayBuffer (${vrmArrayBuffer.byteLength} bytes)` : `URL: ${vrmUrl}`}`);

      // 既存のモデルがある場合は先にクリア
      if (vrmRef.current) {
        console.log(`🧹 既存モデルをクリア: ${agentId}`);
        clearCurrentModel();
      }

      // VRMバージョン検証（ArrayBufferの場合のみ）
      let vrmVersion = 'unknown';
      if (vrmArrayBuffer) {
        vrmVersion = detectVRMVersion(vrmArrayBuffer);
        console.log(`📋 検出されたVRMバージョン: ${vrmVersion}`);

        // VRM 0.x形式の場合は警告表示（最新ローダーで試行）
        if (vrmVersion === 'vrm0') {
          console.warn(`⚠️ VRM 0.x形式検出 - 最新ローダーで読み込み試行: ${agentId}`);
          console.warn(`📋 注意: VRM 1.0形式への変換を推奨します`);
        }
      }

      // キャッシュされたライブラリを使用
      const THREE = THREE_CACHE || await import('three');
      if (!THREE_CACHE) THREE_CACHE = THREE;
      
      const GLTFLoaderModule = GLTFLoader_CACHE || (await import('three/examples/jsm/loaders/GLTFLoader.js')).GLTFLoader;
      if (!GLTFLoader_CACHE) GLTFLoader_CACHE = GLTFLoaderModule;
      
      // 最新のVRMローダーを使用（全バージョン対応・互換性強化）
      console.log(`📦 VRMローダーを初期化: ${agentId}`);
      const vrmCurrent = VRMLoader_CACHE || await import('@pixiv/three-vrm');
      const VRMLoaderPlugin = vrmCurrent.VRMLoaderPlugin;
      const VRMUtils = vrmCurrent.VRMUtils;
      if (!VRMLoader_CACHE) VRMLoader_CACHE = vrmCurrent;

      const gltfLoader = new GLTFLoaderModule();

      // VRMローダープラグインを設定（エラーハンドリング強化）
      try {
        gltfLoader.register((parser) => {
          const vrmPlugin = new VRMLoaderPlugin(parser);
          console.log(`🔧 VRMローダープラグイン登録: ${agentId}`);
          return vrmPlugin;
        });
      } catch (error) {
        console.warn(`⚠️ VRMローダープラグイン登録警告: ${agentId}`, error);
        // フォールバック: プラグインなしでglTFとして読み込み
      }
      
      console.log(`🎮 VRMローダー開始: ${agentId}`);
      
      let gltfData;
      
      if (vrmArrayBuffer) {
        // ArrayBufferの場合：aituber-kit方式でBlob経由読み込み
        console.log(`📦 ArrayBuffer Blob経由読み込み: ${agentId} (${vrmArrayBuffer.byteLength} bytes)`);
        console.log(`📋 ArrayBuffer型確認:`, vrmArrayBuffer.constructor.name);
        console.log(`📋 ArrayBufferの最初の8バイト:`, new Uint8Array(vrmArrayBuffer, 0, 8));

        // aituber-kit方式: Blobを経由してURLを作成（より安定）
        const blob = new Blob([vrmArrayBuffer], { type: 'application/octet-stream' });
        const blobUrl = window.URL.createObjectURL(blob);

        try {
          gltfData = await new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
              reject(new Error(`VRM読み込みタイムアウト (30秒): ${agentId}`));
            }, 30000);

            gltfLoader.load(
              blobUrl,
              (gltf) => {
                clearTimeout(timeoutId);
                console.log(`✅ Blob VRM読み込み成功: ${agentId}`, gltf);
                console.log(`📋 gltf.scene:`, gltf.scene);
                console.log(`📋 gltf.userData:`, gltf.userData);
                console.log(`📋 gltf.userData.vrm:`, gltf.userData?.vrm);
                resolve(gltf);
              },
              (progress) => {
                const percent = Math.round((progress.loaded / progress.total) * 100);
                console.log(`📈 VRM読み込み進捗 (${agentId}): ${percent}%`);
              },
              (error) => {
                clearTimeout(timeoutId);
                console.error(`❌ Blob VRM読み込みエラー: ${agentId}`, error);
                reject(new Error(`VRMファイル読み込み失敗: ${error.message || 'Blob VRMローダーエラー'}`));
              }
            );
          });
        } finally {
          // 使用後にBlobURLを解放（メモリリーク防止）
          window.URL.revokeObjectURL(blobUrl);
        }
      } else if (vrmUrl?.startsWith('http')) {
        // HTTP URLの場合：通常のload
        console.log(`🌐 HTTP URL読み込み: ${agentId} - ${vrmUrl}`);
        gltfData = await new Promise((resolve, reject) => {
          const timeoutId = setTimeout(() => {
            reject(new Error(`VRM読み込みタイムアウト (30秒): ${agentId}`));
          }, 30000);

          gltfLoader.load(
            vrmUrl,
            (gltf) => {
              clearTimeout(timeoutId);
              console.log(`✅ HTTP VRM読み込み成功: ${agentId}`, gltf);
              resolve(gltf);
            },
            (progress) => {
              const percent = Math.round((progress.loaded / progress.total) * 100);
              console.log(`📈 VRM読み込み進捗 (${agentId}): ${percent}%`);
            },
            (error) => {
              clearTimeout(timeoutId);
              console.error(`❌ HTTP VRM読み込みエラー: ${agentId}`, error);
              reject(new Error(`VRMファイル解析失敗: ${error.message || 'HTTP VRMローダーエラー'}`));
            }
          );
        });
      } else {
        throw new Error('無効なデータです（ArrayBufferまたはHTTP URLが必要）');
      }

      // VRMデータを抽出（glTF互換性向上）
      const vrm = gltfData.userData?.vrm;
      const scene = gltfData.scene;

      if (vrm) {
        console.log(`🎭 VRM固有データ検出: ${agentId}`, vrm.meta?.title || 'Unknown VRM');
      } else {
        console.log(`📦 標準glTF形式として読み込み: ${agentId}`);
        console.log(`📋 glTFシーン情報:`, {
          children: scene?.children?.length || 0,
          animations: gltfData.animations?.length || 0,
          materials: gltfData.materials?.length || 0,
          meshes: gltfData.meshes?.length || 0
        });
      }

      // シーンに追加
      if (sceneRef.current && scene) {
        console.log(`🎯 シーンへの追加開始: ${agentId}`);
        console.log(`📋 scene.children.length:`, scene.children.length);
        console.log(`📋 sceneRef.current:`, sceneRef.current);
        
        // モデルの最適化とスケーリング
        scene.scale.set(1, 1, 1);
        scene.position.set(0, -1, 0);
        scene.rotation.y = Math.PI; // 正面向きに回転
        
        console.log(`🔧 シーンのスケール・位置・回転設定完了: ${agentId}`);
        
        // マテリアルとテクスチャの最適化
        let meshCount = 0;
        scene.traverse((object: any) => {
          if (object.isMesh) {
            meshCount++;
            optimizeGeometry(object.geometry);
            if (object.material) {
              if (Array.isArray(object.material)) {
                object.material.forEach((mat: any) => {
                  if (mat.map) optimizeTexture(mat.map, THREE);
                  if (mat.normalMap) optimizeTexture(mat.normalMap, THREE);
                });
              } else {
                if (object.material.map) optimizeTexture(object.material.map, THREE);
                if (object.material.normalMap) optimizeTexture(object.material.normalMap, THREE);
              }
            }
          }
        });
        
        console.log(`🔧 マテリアル最適化完了: ${agentId} (${meshCount}個のメッシュ)`);

        sceneRef.current.add(scene);
        vrmRef.current = { scene, vrm, gltf: gltfData };
        setModelLoaded(true);

        // 強制的にレンダリングを実行（表示確保）
        markNeedsRender();
        setTimeout(() => markNeedsRender(), 100);
        setTimeout(() => markNeedsRender(), 500);

        console.log(`✅ VRM読み込み完了: ${agentId} - ${scene.name || vrm?.meta?.title || 'VRM Model'}`);
        console.log(`📋 最終確認 - sceneRef.current.children.length:`, sceneRef.current.children.length);
        console.log(`📋 最終確認 - modelLoaded:`, true);
      } else {
        console.error(`❌ シーン追加失敗: ${agentId}`);
        console.error(`📋 sceneRef.current:`, sceneRef.current);
        console.error(`📋 scene:`, scene);
      }
    } catch (error: any) {
      console.error(`⚠️ VRM読み込み失敗: ${agentId}`, error);
      
      // エラー内容に応じた詳細メッセージ
      let errorMessage = 'VRMファイルの読み込みに失敗しました';
      
      if (error.message?.includes('Unsupported asset')) {
        if (error.message?.includes('glTF versions >=2.0')) {
          errorMessage = '⚠️ VRM 0.x形式（glTF 1.0）の互換性問題\n\n' + 
                       '📋 解決方法:\n' +
                       '• VRoidStudio最新版でVRM 1.0形式に変換\n' +
                       '• Blender VRMアドオンで形式を更新\n' +
                       '• UniVRMで再エクスポート\n\n' +
                       '💡 VRM 1.0形式は安定性・互換性が向上しています';
        } else {
          errorMessage = '⚠️ 未対応のファイル形式\n\n対応形式: VRM 0.x/1.0, glTF 2.0';
        }
      } else if (error.message?.includes('Failed to fetch')) {
        errorMessage = '📡 ネットワークエラー\n\nファイルの取得に失敗しました';
      } else if (error.message?.includes('parse') || error.message?.includes('JSON')) {
        errorMessage = '🔧 ファイル解析エラー\n\n' +
                       '原因:\n' +
                       '• ファイルが破損している可能性\n' +
                       '• 不完全なアップロード\n' +
                       '• 非VRMファイルの選択\n\n' +
                       '💡 再度ファイルをアップロードしてみてください';
      } else if (error.message?.includes('timeout')) {
        errorMessage = '⏱️ 読み込みタイムアウト\n\nファイルサイズが大きすぎます（推奨: 50MB未満）';
      }
      
      setError(errorMessage);
      
      // リトライは無効化（無限ループ防止）
      console.log(`🚫 VRM読み込み失敗: ${agentId} - リトライなし`);
    } finally {
      setIsLoading(false);
      loadingRef.current = false;
    }
  }, [vrmUrl, vrmArrayBuffer, agentId, markNeedsRender, clearCurrentModel, optimizeGeometry, optimizeTexture, detectVRMVersion]);

  // プレースホルダー表示関数（非表示）
  const showPlaceholder = useCallback(() => {
    console.log(`📦 プレースホルダー非表示: ${agentId}`);
    clearCurrentModel();
    // 何も表示しない
  }, [agentId]);

  // エージェント変更時の処理
  useEffect(() => {
    if (currentAgentIdRef.current !== agentId) {
      console.log(`🔄 エージェント変更: ${currentAgentIdRef.current} → ${agentId}`);
      console.log(`📋 変更前URL: ${previousVrmUrlRef.current}`);
      console.log(`📋 変更後URL: ${vrmUrl}`);
      
      // 前のエージェントのモデルを確実にクリア
      clearCurrentModel();
      
      currentAgentIdRef.current = agentId;
      previousVrmUrlRef.current = null; // エージェント変更時はURLをリセット
      
      console.log(`✅ エージェント変更処理完了: ${agentId}`);
    }
  }, [agentId, clearCurrentModel, vrmUrl]);

  // フェイストラッキングデータ適用
  useEffect(() => {
    if (faceTrackingData && modelLoaded && vrmRef.current?.vrm) {
      applyFaceTrackingToVRM(faceTrackingData);
    }
  }, [faceTrackingData, modelLoaded, applyFaceTrackingToVRM]);

  // VRM データ変更時の処理（ArrayBuffer対応強化）
  useEffect(() => {
    if (!sceneRef.current || !THREE_CACHE) return;

    // ArrayBufferまたはURL変更の検出と処理（検証強化）
    const hasArrayBuffer = vrmArrayBuffer && vrmArrayBuffer instanceof ArrayBuffer && vrmArrayBuffer.byteLength > 0;
    const hasUrl = vrmUrl && vrmUrl.length > 0;
    const hasData = hasArrayBuffer || hasUrl;
    const hasUrlChanged = vrmUrl !== previousVrmUrlRef.current;

    // ArrayBufferの変更検出（バイト長で判定）
    const currentArrayBufferSize = hasArrayBuffer ? vrmArrayBuffer.byteLength : 0;
    const hasArrayBufferChanged = currentArrayBufferSize > 0 && currentArrayBufferSize !== (vrmRef.current?.arrayBufferSize || 0);

    const hasChanged = hasUrlChanged || hasArrayBufferChanged;

    console.log(`🔄 VRMデータ確認: ${agentId}`);
    console.log(`📋 hasData: ${!!hasData} (ArrayBuffer: ${hasArrayBuffer}, URL: ${hasUrl})`);
    console.log(`📋 hasUrlChanged: ${hasUrlChanged}`);
    console.log(`📋 hasArrayBufferChanged: ${hasArrayBufferChanged}`);
    console.log(`📋 currentArrayBufferSize: ${currentArrayBufferSize}`);
    console.log(`📋 previousArrayBufferSize: ${vrmRef.current?.arrayBufferSize || 0}`);
    console.log(`📋 vrmArrayBuffer詳細:`, vrmArrayBuffer ? {
      isArrayBuffer: vrmArrayBuffer instanceof ArrayBuffer,
      byteLength: vrmArrayBuffer.byteLength,
      constructor: vrmArrayBuffer.constructor.name
    } : 'なし');

    if (hasData && hasChanged) {
      console.log(`🔄 VRMデータ変更検出: ${agentId}`);
      previousVrmUrlRef.current = vrmUrl || null;

      // ArrayBufferサイズを記録
      if (vrmRef.current) {
        vrmRef.current.arrayBufferSize = currentArrayBufferSize;
      }

      // 少し遅延を入れてからロード（安定性向上）
      const loadTimer = setTimeout(() => {
        loadVRM();
      }, 100);

      return () => clearTimeout(loadTimer);
    } else if (!hasData && (previousVrmUrlRef.current || modelLoaded)) {
      // データが削除された場合
      console.log(`🗑️ VRM削除検出: ${agentId}`);
      clearCurrentModel();
      showPlaceholder();
      previousVrmUrlRef.current = null;
      if (vrmRef.current) {
        vrmRef.current.arrayBufferSize = 0;
      }
    } else if (!hasData && !modelLoaded) {
      // VRMがない場合はプレースホルダーを表示（一度だけ）
      showPlaceholder();
    }
  }, [vrmUrl, vrmArrayBuffer, agentId, modelLoaded, loadVRM, clearCurrentModel, showPlaceholder]);

  // Three.js初期化
  useEffect(() => {
    const initThreeJS = async () => {
      try {
        if (!canvasRef.current) return;

        // Three.js動的インポート（キャッシュ活用）
        const THREE = THREE_CACHE || await import('three');
        if (!THREE_CACHE) THREE_CACHE = THREE;

        // シーン初期化
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0x222222);
        sceneRef.current = scene;

        // カメラ設定
        const camera = new THREE.PerspectiveCamera(
          50, 
          canvasRef.current.clientWidth / canvasRef.current.clientHeight, 
          0.1, 
          1000
        );
        camera.position.set(0, 1.6, 2);
        camera.lookAt(0, 1, 0);

        // レンダラー設定
        const renderer = new THREE.WebGLRenderer({ 
          canvas: canvasRef.current,
          antialias: true,
          alpha: true
        });
        
        const canvasWidth = canvasRef.current.clientWidth;
        const canvasHeight = canvasRef.current.clientHeight;
        
        console.log(`🖥️ キャンバスサイズ設定: ${agentId}`);
        console.log(`📋 Canvas clientWidth: ${canvasWidth}px`);
        console.log(`📋 Canvas clientHeight: ${canvasHeight}px`);
        console.log(`📋 Canvas offsetWidth: ${canvasRef.current.offsetWidth}px`);
        console.log(`📋 Canvas offsetHeight: ${canvasRef.current.offsetHeight}px`);
        console.log(`📋 DevicePixelRatio: ${window.devicePixelRatio}`);
        
        renderer.setSize(canvasWidth, canvasHeight);
        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        renderer.outputColorSpace = THREE.SRGBColorSpace;
        rendererRef.current = renderer;
        
        console.log(`📋 レンダラーサイズ: ${renderer.domElement.width}x${renderer.domElement.height}`);

        // ライティング設定
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(1, 1, 1);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.setScalar(PERFORMANCE_CONFIG.SHADOW_MAP_SIZE);
        scene.add(directionalLight);

        // レンダリングループ（点滅修正）
        const renderLoop = () => {
          const now = performance.now();
          const deltaTime = (now - lastFrameTimeRef.current) / 1000; // 秒単位に変換

          // VRMアニメーション更新（モデルが読み込まれている場合）
          if (vrmRef.current?.vrm) {
            // VRMアニメーション更新
            updateAutoBlinking(deltaTime);
            updateIdleAnimation(deltaTime);

            // VRM表情マネージャーの更新
            if (vrmRef.current.vrm.expressionManager) {
              vrmRef.current.vrm.expressionManager.update();
            }

            // VRMヒューマノイドの更新
            if (vrmRef.current.vrm.humanoid) {
              vrmRef.current.vrm.humanoid.update();
            }

            // 継続的なレンダリングを要求
            needsRenderRef.current = true;
          }

          // フレームレート制御付きレンダリング
          if (deltaTime >= 1000 / PERFORMANCE_CONFIG.TARGET_FPS || needsRenderRef.current) {
            // カメラとレンダリング
            renderer.render(scene, camera);
            lastFrameTimeRef.current = now;

            // FPS監視
            performanceRef.current.frameCount++;
            if (now - performanceRef.current.lastFpsCheck > 1000) {
              performanceRef.current.fps = performanceRef.current.frameCount;
              performanceRef.current.frameCount = 0;
              performanceRef.current.lastFpsCheck = now;
            }

            // VRMが読み込まれていない場合のみレンダリング要求をリセット
            if (!vrmRef.current?.vrm) {
              needsRenderRef.current = false;
            }
          }

          animationIdRef.current = requestAnimationFrame(renderLoop);
        };

        renderLoop();
        markNeedsRender();
        
        console.log(`🎮 Three.js初期化完了: ${agentId}`);

        // 少し遅延してからVRMロード開始（ArrayBuffer検証強化）
        setTimeout(() => {
          const hasArrayBuffer = vrmArrayBuffer && vrmArrayBuffer instanceof ArrayBuffer && vrmArrayBuffer.byteLength > 0;
          const hasUrl = vrmUrl && vrmUrl.length > 0;
          const hasData = hasArrayBuffer || hasUrl;
          const arrayBufferSize = hasArrayBuffer ? vrmArrayBuffer.byteLength : 0;

          // ログを確実に出力するために複数の方法を使用
          const logMessage = `⏱️ 初期ロードタイマー実行: ${agentId}`;
          console.log(logMessage);
          console.warn(logMessage); // warn も使用

          console.log(`📋 hasData: ${hasData} (ArrayBuffer: ${hasArrayBuffer}, URL: ${hasUrl})`);
          console.log(`📋 vrmArrayBuffer: ${hasArrayBuffer ? `${arrayBufferSize} bytes` : 'なし'}`);
          console.log(`📋 vrmUrl: ${vrmUrl || 'なし'}`);
          console.log(`📋 previousVrmUrlRef.current: ${previousVrmUrlRef.current}`);

          // ArrayBufferの詳細情報をログ出力
          if (vrmArrayBuffer) {
            console.log(`📋 ArrayBuffer詳細:`, {
              byteLength: vrmArrayBuffer.byteLength,
              constructor: vrmArrayBuffer.constructor.name,
              isArrayBuffer: vrmArrayBuffer instanceof ArrayBuffer,
              isValid: hasArrayBuffer,
              firstBytes: hasArrayBuffer ? new Uint8Array(vrmArrayBuffer, 0, Math.min(16, vrmArrayBuffer.byteLength)) : 'N/A'
            });
          } else {
            console.log(`📋 ArrayBuffer: なし`);
          }

          if (hasData) {
            console.log(`🚀 VRM読み込み開始決定: ${agentId}`);
            console.warn(`🚀 VRM読み込み開始決定: ${agentId}`); // warn も使用
            previousVrmUrlRef.current = vrmUrl || null;

            // ArrayBufferサイズを記録
            if (vrmRef.current) {
              vrmRef.current.arrayBufferSize = arrayBufferSize;
            }

            loadVRM();
          } else {
            console.log(`📦 プレースホルダー表示: ${agentId}`);
            showPlaceholder();
          }
        }, 500);

      } catch (error) {
        console.error(`❌ Three.js初期化エラー: ${agentId}`, error);
        setError('3Dエンジンの初期化に失敗しました');
      }
    };

    initThreeJS();

    // クリーンアップ
    return () => {
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
      clearCurrentModel();
    };
  }, [agentId, loadVRM, markNeedsRender, clearCurrentModel, showPlaceholder]);

  // ファイルアップロード処理（glTF互換性向上）
  const handleFileUpload = useCallback((file: File) => {
    const fileName = file.name.toLowerCase();
    const isValidFile = fileName.endsWith('.vrm') || fileName.endsWith('.glb') || fileName.endsWith('.gltf');

    if (file && isValidFile) {
      console.log(`📁 ファイルアップロード開始: ${agentId} - ${file.name} (${file.size} bytes)`);
      console.log(`📋 ファイル詳細:`, {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: new Date(file.lastModified).toISOString()
      });

      if (onVRMUpload) {
        onVRMUpload(file);
      }
    } else {
      setError('VRM、glTF、またはglBファイルを選択してください\n\n対応形式: .vrm, .glb, .gltf');
    }
  }, [agentId, onVRMUpload]);

  return (
    <div className={`relative w-full h-full ${className}`}>
      <canvas 
        ref={canvasRef}
        className="w-full h-full rounded-lg bg-gradient-to-b from-gray-800 to-gray-900"
        onClick={() => markNeedsRender()}
      />
      
      {/* ローディング表示 */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/20 rounded-lg">
          <div className="flex flex-col items-center space-y-2">
            <div className="loading loading-spinner loading-lg text-primary"></div>
            <span className="text-sm text-white">{agentId}のVRM読み込み中...</span>
          </div>
        </div>
      )}

      {/* エラー表示 */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-500/20 rounded-lg backdrop-blur-sm">
          <div className="bg-base-100 rounded-lg p-6 max-w-md mx-4 shadow-xl border border-error/20">
            <div className="text-center">
              <div className="text-2xl mb-2">⚠️</div>
              <div className="font-bold text-error mb-3">VRM読み込みエラー</div>
              <div className="text-sm text-base-content whitespace-pre-line mb-4 text-left">
                {error}
              </div>
              <div className="flex gap-2 justify-center">
                <button 
                  className="btn btn-sm btn-ghost"
                  onClick={() => setError(null)}
                >
                  閉じる
                </button>
                <button 
                  className="btn btn-sm btn-primary"
                  onClick={() => {
                    setError(null);
                    if (vrmUrl || vrmArrayBuffer) loadVRM();
                  }}
                >
                  再試行
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* ファイルアップロード領域 */}
      {!vrmUrl && !vrmArrayBuffer && !isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div 
            className="border-2 border-dashed border-primary/50 rounded-lg p-8 text-center cursor-pointer hover:border-primary transition-colors"
            onClick={() => fileInputRef.current?.click()}
          >
            <div className="text-4xl mb-2">📁</div>
            <div className="text-sm text-base-content/70">
              {agentId}用3Dモデルをアップロード
            </div>
            <div className="text-xs text-base-content/50 mt-1">
              対応形式: VRM, glTF, glB
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept=".vrm,.glb,.gltf"
              className="hidden"
              onChange={(e) => {
                if (e.target.files?.[0]) {
                  handleFileUpload(e.target.files[0]);
                }
              }}
            />
          </div>
        </div>
      )}

      {/* VRMコントロール */}
      {showControls && modelLoaded && (
        <div className="absolute top-2 right-2 flex flex-col gap-1">
          <button 
            className="btn btn-xs btn-ghost bg-black/20 text-white"
            onClick={() => setViewMode(viewMode === 'bust' ? 'full' : 'bust')}
          >
            {viewMode === 'bust' ? '📷 全身' : '📷 バスト'}
          </button>
          <button 
            className="btn btn-xs btn-ghost bg-black/20 text-white"
            onClick={() => markNeedsRender()}
          >
            🔄 更新
          </button>
        </div>
      )}

      {/* パフォーマンス情報（デバッグ用） */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute bottom-2 left-2 text-xs text-white/50">
          FPS: {performanceRef.current.fps} | Agent: {agentId}
        </div>
      )}
    </div>
  );
}