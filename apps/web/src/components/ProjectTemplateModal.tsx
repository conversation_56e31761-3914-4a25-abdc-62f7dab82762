'use client';

import { useState } from 'react';
import { X, Smartphone, Bot, Video, Globe, Database, Gamepad2, Zap, BookOpen, Image, Music, Film, UserCheck, TrendingUp, Palette, Workflow, ShoppingBag, Share2, Calendar, Bitcoin, Search, Boxes, FileText, Users, Mic, Edit3, ChefHat, Target, Briefcase } from 'lucide-react';

interface ProjectTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  icon: React.ReactNode;
  technologies: string[];
  agentConfig: {
    king: string;
    generals: string[];
    soldiers: string[];
  };
  estimatedDuration: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  yamlStructure: {
    title: string;
    dialogFields: Array<{
      key: string;
      label: string;
      type: 'text' | 'select' | 'textarea' | 'number' | 'boolean';
      required: boolean;
      options?: string[];
      placeholder?: string;
    }>;
  };
}

const projectTemplates: ProjectTemplate[] = [
  // 一般/汎用/基本
  {
    id: 'blank-project',
    name: '空白プロジェクト',
    description: 'ゼロベースで新規作成するカスタムプロジェクト',
    category: '汎用',
    tags: ['汎用', '基本', '空白', 'ブランク', '０ベース', '新規作成', 'カスタム'],
    icon: <FileText size={24} />,
    technologies: ['自由選択'],
    agentConfig: {
      king: '汎用プロジェクト王',
      generals: ['要件定義将', 'アーキテクチャ将'],
      soldiers: ['設計兵', '実装兵', 'テスト兵']
    },
    estimatedDuration: '1-12週間',
    difficulty: 'beginner',
    yamlStructure: {
      title: 'プロジェクト基本設定',
      dialogFields: [
        { key: 'projectType', label: 'プロジェクトタイプ', type: 'select', required: true, options: ['Web', 'Mobile', 'Desktop', 'API', 'CLI', 'Library'] },
        { key: 'targetAudience', label: 'ターゲット層', type: 'text', required: true, placeholder: '例: 20-30代のビジネスパーソン' },
        { key: 'mainFeatures', label: '主要機能', type: 'textarea', required: true, placeholder: '実装したい主要機能を箇条書きで' },
        { key: 'budget', label: '予算（万円）', type: 'number', required: false, placeholder: '100' },
        { key: 'deadline', label: '完成希望日', type: 'text', required: false, placeholder: '2024-12-31' }
      ]
    }
  },

  // 小説/シナリオ/ストーリー/ブログ/執筆/文字
  {
    id: 'writing-platform',
    name: '執筆プラットフォーム',
    description: 'AI支援機能付きの小説・ブログ執筆プラットフォーム',
    category: 'コンテンツ創作',
    tags: ['小説', 'シナリオ', 'ストーリー', 'ブログ', '執筆', '文字', 'コンテンツ', 'AI支援'],
    icon: <BookOpen size={24} />,
    technologies: ['Next.js', 'OpenAI API', 'Supabase', 'TipTap Editor'],
    agentConfig: {
      king: 'コンテンツ創作王',
      generals: ['執筆支援将', 'エディター将'],
      soldiers: ['AI文章生成兵', '校正兵', 'SEO兵']
    },
    estimatedDuration: '3-6週間',
    difficulty: 'intermediate',
    yamlStructure: {
      title: '執筆プラットフォーム設定',
      dialogFields: [
        { key: 'contentType', label: 'コンテンツタイプ', type: 'select', required: true, options: ['小説', 'ブログ', 'シナリオ', '技術記事', '詩・短歌'] },
        { key: 'genrePreferences', label: '対象ジャンル', type: 'text', required: true, placeholder: '例: ファンタジー、SF、恋愛' },
        { key: 'aiFeatures', label: 'AI支援機能', type: 'select', required: true, options: ['文章生成', '校正のみ', 'プロット支援', 'キャラクター生成'] },
        { key: 'publishingGoal', label: '公開目標', type: 'select', required: true, options: ['個人ブログ', '商業出版', 'Web小説サイト', 'SNS投稿'] }
      ]
    }
  },

  // プレゼン/Marp/スライド制作
  {
    id: 'presentation-platform',
    name: 'プレゼンテーション制作プラットフォーム',
    description: 'Marp・Reveal.js対応のAI支援プレゼンテーション制作システム',
    category: 'プレゼンテーション',
    tags: ['プレゼン', 'Marp', 'スライド', 'Reveal.js', 'AI支援', 'ビジネス', 'セミナー', 'パワーポイント'],
    icon: <FileText size={24} />,
    technologies: ['Marp', 'Reveal.js', 'Next.js', 'OpenAI API', 'Puppeteer'],
    agentConfig: {
      king: 'プレゼンテーション王',
      generals: ['スライド設計将', 'ビジュアル将'],
      soldiers: ['構成設計兵', 'デザイン兵', 'アニメーション兵']
    },
    estimatedDuration: '2-4週間',
    difficulty: 'intermediate',
    yamlStructure: {
      title: 'プレゼンテーション制作設定',
      dialogFields: [
        { key: 'presentationType', label: 'プレゼンタイプ', type: 'select', required: true, options: ['ビジネス', '学術', '教育', 'セミナー', 'ピッチ'] },
        { key: 'slideFormat', label: 'スライド形式', type: 'select', required: true, options: ['Marp', 'Reveal.js', 'PowerPoint', 'Google Slides'] },
        { key: 'duration', label: '発表時間', type: 'select', required: true, options: ['5分', '10分', '20分', '45分', '90分'] },
        { key: 'audience', label: '対象聴衆', type: 'text', required: true, placeholder: '例: 経営陣、エンジニア、学生' }
      ]
    }
  },

  // Manim/アニメーション数学
  {
    id: 'manim-animation-platform',
    name: 'Manim数学アニメーションプラットフォーム',
    description: 'Manimを活用した数学・物理教育アニメーション制作システム',
    category: '教育・アニメーション',
    tags: ['Manim', 'アニメーション', '数学', '物理', '教育', 'Python', 'STEM', '3Blue1Brown'],
    icon: <Film size={24} />,
    technologies: ['Manim', 'Python', 'LaTeX', 'FFmpeg', 'Jupyter', 'FastAPI'],
    agentConfig: {
      king: '数学アニメーション王',
      generals: ['Manim設計将', '教育コンテンツ将'],
      soldiers: ['アニメーション兵', '数式レンダリング兵', '動画編集兵']
    },
    estimatedDuration: '4-8週間',
    difficulty: 'advanced',
    yamlStructure: {
      title: 'Manimアニメーション設定',
      dialogFields: [
        { key: 'subject', label: '対象分野', type: 'select', required: true, options: ['数学', '物理', '化学', 'コンピュータサイエンス', '統計学'] },
        { key: 'level', label: '教育レベル', type: 'select', required: true, options: ['中学生', '高校生', '大学生', '大学院生', '研究者'] },
        { key: 'animationType', label: 'アニメーションタイプ', type: 'select', required: true, options: ['説明動画', 'インタラクティブ', 'シミュレーション', 'デモンストレーション'] },
        { key: 'outputFormat', label: '出力形式', type: 'select', required: true, options: ['MP4動画', 'GIFアニメ', 'Web埋め込み', 'インタラクティブHTML'] }
      ]
    }
  },

  // Podcast制作
  {
    id: 'podcast-platform',
    name: 'Podcast制作プラットフォーム',
    description: 'AI音声生成・編集機能付きのPodcast制作・配信システム',
    category: 'オーディオ制作',
    tags: ['Podcast', '音声', 'AI音声生成', 'ElevenLabs', '配信', 'Spotify', 'Apple Podcasts', 'RSS'],
    icon: <Music size={24} />,
    technologies: ['ElevenLabs', 'OpenAI Whisper', 'Node.js', 'RSS', 'AWS S3', 'Podcast APIs'],
    agentConfig: {
      king: 'Podcast制作王',
      generals: ['音声編集将', '配信戦略将'],
      soldiers: ['音声生成兵', 'ミキシング兵', 'RSS配信兵']
    },
    estimatedDuration: '3-6週間',
    difficulty: 'intermediate',
    yamlStructure: {
      title: 'Podcast制作設定',
      dialogFields: [
        { key: 'podcastType', label: 'ポッドキャストタイプ', type: 'select', required: true, options: ['対談・インタビュー', '一人語り', 'ニュース解説', '教育・学習', 'エンタメ'] },
        { key: 'episodeLength', label: 'エピソード長', type: 'select', required: true, options: ['10分以下', '15-30分', '45-60分', '90分以上'] },
        { key: 'voiceType', label: '音声タイプ', type: 'select', required: true, options: ['人間の声', 'AI音声', 'ミックス（人間+AI）', 'キャラクター音声'] },
        { key: 'distributionPlatform', label: '配信プラットフォーム', type: 'select', required: true, options: ['Spotify', 'Apple Podcasts', 'Google Podcasts', '複数プラットフォーム'] }
      ]
    }
  },

  // 士業・法務
  {
    id: 'legal-practice-platform',
    name: '士業・法務管理プラットフォーム',
    description: '法律事務所・士業向けの案件管理・文書作成支援システム',
    category: '士業・法務',
    tags: ['士業', '法務', '弁護士', '司法書士', '行政書士', '税理士', '社労士', '案件管理', '文書作成'],
    icon: <Users size={24} />,
    technologies: ['Next.js', 'PostgreSQL', 'Prisma', 'NextAuth', 'PDF-lib', 'Calendar API'],
    agentConfig: {
      king: '法務管理王',
      generals: ['案件管理将', '文書作成将'],
      soldiers: ['スケジュール兵', 'PDF生成兵', '顧客管理兵']
    },
    estimatedDuration: '6-12週間',
    difficulty: 'advanced',
    yamlStructure: {
      title: '士業プラットフォーム設定',
      dialogFields: [
        { key: 'practiceType', label: '士業種別', type: 'select', required: true, options: ['弁護士', '司法書士', '行政書士', '税理士', '社会保険労務士', '弁理士'] },
        { key: 'specialization', label: '専門分野', type: 'text', required: true, placeholder: '例: 企業法務、相続、不動産' },
        { key: 'clientType', label: '主要顧客層', type: 'select', required: true, options: ['個人', '中小企業', '大企業', 'ミックス'] },
        { key: 'documentTypes', label: '主要文書類', type: 'text', required: true, placeholder: '例: 契約書、遺言書、登記申請書' }
      ]
    }
  },

  // ライター業務
  {
    id: 'writer-business-platform',
    name: 'ライター業務管理プラットフォーム',
    description: 'フリーランスライター向けの案件管理・執筆支援・納期管理システム',
    category: 'ライター業務',
    tags: ['ライター', 'フリーランス', 'コピーライティング', 'Webライティング', 'SEO', '案件管理', '納期管理', 'AI校正'],
    icon: <BookOpen size={24} />,
    technologies: ['Next.js', 'OpenAI API', 'Notion API', 'Calendar API', 'Grammarly API', 'Stripe'],
    agentConfig: {
      king: 'ライター業務王',
      generals: ['案件管理将', 'AI執筆支援将'],
      soldiers: ['SEO分析兵', '校正兵', '納期管理兵']
    },
    estimatedDuration: '4-8週間',
    difficulty: 'intermediate',
    yamlStructure: {
      title: 'ライター業務設定',
      dialogFields: [
        { key: 'writingType', label: 'ライティングタイプ', type: 'select', required: true, options: ['Webライティング', 'コピーライティング', '技術ライティング', 'コンテンツマーケティング', 'ブログライティング'] },
        { key: 'specialization', label: '専門分野', type: 'text', required: true, placeholder: '例: IT、美容、金融、不動産' },
        { key: 'clientManagement', label: '顧客管理機能', type: 'select', required: true, options: ['基本管理', 'CRM連携', 'プロジェクト管理', 'フル機能'] },
        { key: 'aiAssistance', label: 'AI支援レベル', type: 'select', required: true, options: ['校正のみ', 'アイデア提案', '文章生成支援', 'フル AI アシスタント'] }
      ]
    }
  },

  // 画像/イラスト/アニメーション/漫画
  {
    id: 'visual-creation-studio',
    name: 'ビジュアル創作スタジオ',
    description: 'AI画像生成・編集機能を備えたイラスト・アニメーション制作プラットフォーム',
    category: 'ビジュアル制作',
    tags: ['画像', 'イラスト', 'アニメーション', '漫画', 'AI画像生成', 'Stable Diffusion', 'デジタルアート'],
    icon: <Image size={24} />,
    technologies: ['React', 'Stable Diffusion', 'Canvas API', 'FFmpeg', 'WebGL'],
    agentConfig: {
      king: 'ビジュアル制作王',
      generals: ['AI画像生成将', 'アニメーション将'],
      soldiers: ['画像処理兵', 'エフェクト兵', 'レンダリング兵']
    },
    estimatedDuration: '4-8週間',
    difficulty: 'advanced',
    yamlStructure: {
      title: 'ビジュアル創作設定',
      dialogFields: [
        { key: 'artStyle', label: 'アートスタイル', type: 'select', required: true, options: ['アニメ風', 'リアル系', 'イラスト系', 'ドット絵', '水彩画風'] },
        { key: 'outputFormat', label: '出力フォーマット', type: 'select', required: true, options: ['静止画のみ', 'GIFアニメ', 'MP4動画', '4コマ漫画'] },
        { key: 'targetResolution', label: '目標解像度', type: 'select', required: true, options: ['SD (512x512)', 'HD (1024x1024)', '4K (2048x2048)', 'カスタム'] },
        { key: 'aiModel', label: 'AI生成モデル', type: 'select', required: true, options: ['Stable Diffusion XL', 'Midjourney API', 'DALL-E 3', 'Anime専用モデル'] }
      ]
    }
  },

  // 音声/音楽/SE/BGM
  {
    id: 'audio-production-suite',
    name: 'オーディオ制作スイート',
    description: 'AI音楽生成・音声合成機能付きの総合音響制作プラットフォーム',
    category: 'オーディオ制作',
    tags: ['音声', '音楽', 'SE', 'BGM', 'AI音楽生成', 'VOCALOID', 'ポッドキャスト', 'ナレーション'],
    icon: <Music size={24} />,
    technologies: ['Web Audio API', 'TensorFlow.js', 'Tone.js', 'OpenAI Whisper', 'ElevenLabs'],
    agentConfig: {
      king: 'オーディオ制作王',
      generals: ['AI音楽生成将', '音声合成将'],
      soldiers: ['ミキシング兵', 'マスタリング兵', '効果音兵']
    },
    estimatedDuration: '5-10週間',
    difficulty: 'advanced',
    yamlStructure: {
      title: 'オーディオ制作設定',
      dialogFields: [
        { key: 'audioType', label: '制作タイプ', type: 'select', required: true, options: ['BGM・楽曲', 'SE・効果音', 'ナレーション', 'ポッドキャスト'] },
        { key: 'musicGenre', label: '音楽ジャンル', type: 'text', required: false, placeholder: 'ロック、ジャズ、クラシックなど' },
        { key: 'voiceType', label: '音声タイプ', type: 'select', required: false, options: ['男性', '女性', '中性', 'ロボット', 'キャラクター風'] },
        { key: 'outputQuality', label: '出力品質', type: 'select', required: true, options: ['CD品質 (44.1kHz)', 'ハイレゾ (96kHz)', 'Web最適化', 'ゲーム用'] }
      ]
    }
  },

  // プレゼンテーション・Marp・資料作成
  {
    id: 'presentation-studio',
    name: 'プレゼンテーションスタジオ',
    description: 'Marp・reveal.js・manim対応の次世代プレゼンテーション作成プラットフォーム',
    category: 'プレゼンテーション',
    tags: ['プレゼン', 'Marp', 'manim', 'reveal.js', 'スライド', 'アニメーション', 'データ可視化'],
    icon: <BookOpen size={24} />,
    technologies: ['Marp', 'reveal.js', 'manim', 'D3.js', 'Chart.js', 'Three.js', 'Lottie'],
    agentConfig: {
      king: 'プレゼン作成王',
      generals: ['デザイン将', 'アニメーション将'],
      soldiers: ['スライド兵', 'グラフ兵', 'レイアウト兵']
    },
    estimatedDuration: '3-6週間',
    difficulty: 'intermediate',
    yamlStructure: {
      title: 'プレゼンテーション設定',
      dialogFields: [
        { key: 'presentationType', label: 'プレゼンタイプ', type: 'select', required: true, options: ['ビジネス提案', '学術発表', '教育・セミナー', '製品紹介', '技術解説'] },
        { key: 'outputFormat', label: '出力形式', type: 'select', required: true, options: ['Marp (Markdown)', 'reveal.js (HTML)', 'manim (動画)', 'PowerPoint (PPTX)'] },
        { key: 'theme', label: 'テーマ', type: 'select', required: true, options: ['ビジネス', 'アカデミック', 'モダン', 'ミニマル', 'カスタム'] },
        { key: 'duration', label: '発表時間', type: 'select', required: true, options: ['5分以内', '10-15分', '30分', '1時間', '1時間以上'] }
      ]
    }
  },

  // ポッドキャスト専用
  {
    id: 'podcast-studio',
    name: 'ポッドキャストスタジオ',
    description: 'AI音声処理・自動編集機能付きのポッドキャスト制作プラットフォーム',
    category: 'オーディオ制作',
    tags: ['ポッドキャスト', '音声配信', 'AI編集', '音声認識', 'RSS配信', 'Spotify', 'Apple Podcasts'],
    icon: <Mic size={24} />,
    technologies: ['Web Audio API', 'OpenAI Whisper', 'Spotify API', 'RSS 2.0', 'ffmpeg', 'Audacity'],
    agentConfig: {
      king: 'ポッドキャスト王',
      generals: ['音声編集将', '配信将'],
      soldiers: ['録音兵', 'ミキシング兵', 'RSS配信兵']
    },
    estimatedDuration: '4-8週間',
    difficulty: 'intermediate',
    yamlStructure: {
      title: 'ポッドキャスト設定',
      dialogFields: [
        { key: 'showFormat', label: '番組形式', type: 'select', required: true, options: ['対談形式', 'ソロ配信', 'ニュース解説', 'インタビュー', '教育・学習'] },
        { key: 'episodeLength', label: '1話の長さ', type: 'select', required: true, options: ['10分以内', '20-30分', '45分-1時間', '1時間以上'] },
        { key: 'distributionPlatforms', label: '配信プラットフォーム', type: 'select', required: true, options: ['Spotify', 'Apple Podcasts', 'Google Podcasts', 'YouTube', '全プラットフォーム'] },
        { key: 'monetization', label: '収益化', type: 'select', required: false, options: ['広告収入', 'サブスクリプション', 'スポンサーシップ', '無料配信'] }
      ]
    }
  },

  // 士業・法務・コンサルティング
  {
    id: 'legal-consulting-platform',
    name: '士業・法務プラットフォーム',
    description: '弁護士・税理士・行政書士向けの業務管理・文書作成システム',
    category: '士業・法務',
    tags: ['法務', '弁護士', '税理士', '行政書士', '司法書士', '社労士', '文書作成', '顧客管理'],
    icon: <FileText size={24} />,
    technologies: ['PDF.js', 'DocuSign API', 'e-Gov API', '法令API', 'OCR', 'AI契約書解析'],
    agentConfig: {
      king: '法務王',
      generals: ['契約書将', '税務将'],
      soldiers: ['文書作成兵', '顧客管理兵', '申請書兵']
    },
    estimatedDuration: '8-12週間',
    difficulty: 'advanced',
    yamlStructure: {
      title: '士業システム設定',
      dialogFields: [
        { key: 'profession', label: '士業種別', type: 'select', required: true, options: ['弁護士', '税理士', '行政書士', '司法書士', '社会保険労務士', '公認会計士'] },
        { key: 'practiceArea', label: '専門分野', type: 'select', required: true, options: ['企業法務', '個人法務', '税務', '労務', '不動産', '知的財産', '相続・遺言'] },
        { key: 'clientManagement', label: '顧客管理機能', type: 'select', required: true, options: ['基本管理のみ', '案件管理', '請求書管理', 'CRM統合'] },
        { key: 'documentTypes', label: '作成文書', type: 'select', required: true, options: ['契約書', '申請書', '訴状', '意見書', '税務申告書', '登記申請書'] }
      ]
    }
  },

  // ライター・編集・出版
  {
    id: 'writing-publishing-platform',
    name: 'ライティング・出版プラットフォーム',
    description: 'AI校正・SEO最適化機能付きの総合ライティング・出版支援システム',
    category: 'ライティング・出版',
    tags: ['ライティング', '編集', '出版', 'SEO', 'AI校正', 'ブログ', '書籍', 'Webメディア'],
    icon: <Edit3 size={24} />,
    technologies: ['OpenAI GPT', 'Grammarly API', 'SEO分析', 'Kindle API', 'WordPress API', 'Ghost CMS'],
    agentConfig: {
      king: 'ライティング王',
      generals: ['編集将', 'SEO将'],
      soldiers: ['校正兵', '構成兵', '配信兵']
    },
    estimatedDuration: '6-10週間',
    difficulty: 'intermediate',
    yamlStructure: {
      title: 'ライティング設定',
      dialogFields: [
        { key: 'contentType', label: 'コンテンツタイプ', type: 'select', required: true, options: ['ブログ記事', 'Webメディア', '電子書籍', '技術文書', 'マーケティング資料', '小説・エッセイ'] },
        { key: 'targetAudience', label: 'ターゲット', type: 'select', required: true, options: ['一般読者', 'ビジネスパーソン', '技術者', '学生・研究者', '専門家'] },
        { key: 'seoOptimization', label: 'SEO最適化', type: 'select', required: true, options: ['基本SEO', '高度SEO', 'キーワード分析', 'SERPs対策', '不要'] },
        { key: 'publishingPlatform', label: '公開プラットフォーム', type: 'select', required: false, options: ['WordPress', 'Ghost', 'Medium', 'note', 'Kindle', 'カスタム'] }
      ]
    }
  },

  // 動画/映像/ドラマ/ショートビデオ/フィルム
  {
    id: 'video-production-platform',
    name: '映像制作プラットフォーム',
    description: 'AI編集支援機能付きの包括的動画制作システム',
    category: '映像制作',
    tags: ['動画', '映像', 'ドラマ', 'ショートビデオ', 'フィルム', 'YouTube', 'TikTok', 'AI編集'],
    icon: <Film size={24} />,
    technologies: ['FFmpeg', 'WebRTC', 'OpenCV', 'RunwayML', 'Adobe After Effects API'],
    agentConfig: {
      king: '映像制作王',
      generals: ['AI編集将', 'エフェクト将'],
      soldiers: ['カット編集兵', '色調補正兵', '音響兵']
    },
    estimatedDuration: '6-12週間',
    difficulty: 'advanced',
    yamlStructure: {
      title: '映像制作設定',
      dialogFields: [
        { key: 'videoFormat', label: '動画フォーマット', type: 'select', required: true, options: ['ショート動画 (縦)', 'YouTube (横)', '映画 (シネマ)', 'ドラマ (16:9)'] },
        { key: 'targetPlatform', label: '配信プラットフォーム', type: 'select', required: true, options: ['YouTube', 'TikTok', 'Instagram', '映画館', 'Netflix'] },
        { key: 'contentGenre', label: 'コンテンツジャンル', type: 'text', required: true, placeholder: 'エンタメ、教育、ドキュメンタリーなど' },
        { key: 'aiEditingLevel', label: 'AI編集レベル', type: 'select', required: true, options: ['基本カットのみ', '自動字幕・効果', 'フル自動編集', '手動+AI支援'] }
      ]
    }
  },

  // キャラクター/人格VRM/FBX/LIVE2D
  {
    id: 'character-creation-studio',
    name: 'キャラクター創作スタジオ',
    description: 'VRM・Live2D対応のAIキャラクター生成・人格設定プラットフォーム',
    category: 'キャラクター制作',
    tags: ['キャラクター', '人格', 'VRM', 'FBX', 'LIVE2D', 'VTuber', 'AI人格', 'Unity'],
    icon: <UserCheck size={24} />,
    technologies: ['Unity', 'Live2D', 'VRM SDK', 'ChatGPT API', 'Three.js', 'Blender API'],
    agentConfig: {
      king: 'キャラクター制作王',
      generals: ['3Dモデリング将', 'AI人格将'],
      soldiers: ['モデリング兵', 'アニメーション兵', '人格設計兵']
    },
    estimatedDuration: '8-16週間',
    difficulty: 'advanced',
    yamlStructure: {
      title: 'キャラクター制作設定',
      dialogFields: [
        { key: 'characterType', label: 'キャラクタータイプ', type: 'select', required: true, options: ['VTuber', 'ゲームキャラ', 'AI assistant', 'アニメキャラ'] },
        { key: 'modelFormat', label: 'モデル形式', type: 'select', required: true, options: ['VRM', 'Live2D', 'FBX', '複数対応'] },
        { key: 'personalityType', label: '人格タイプ', type: 'text', required: true, placeholder: '活発、知的、おっとり、ツンデレなど' },
        { key: 'interactionLevel', label: 'インタラクション機能', type: 'select', required: true, options: ['音声対話', 'テキストのみ', '感情表現', 'フル会話AI'] }
      ]
    }
  },

  // マーケティング/広告/PR/ファネル/セールスレター
  {
    id: 'marketing-automation-platform',
    name: 'マーケティング自動化プラットフォーム',
    description: 'AI分析機能付きの包括的デジタルマーケティングシステム',
    category: 'マーケティング',
    tags: ['マーケティング', '広告', 'PR', 'ファネル', 'セールスレター', 'LP', 'Google Ads', 'SNS広告'],
    icon: <TrendingUp size={24} />,
    technologies: ['Next.js', 'Google Analytics API', 'Facebook Ads API', 'Mailchimp', 'Stripe'],
    agentConfig: {
      king: 'マーケティング王',
      generals: ['広告運用将', 'コンテンツ将'],
      soldiers: ['SEO兵', 'SNS運用兵', 'データ分析兵']
    },
    estimatedDuration: '4-8週間',
    difficulty: 'intermediate',
    yamlStructure: {
      title: 'マーケティング設定',
      dialogFields: [
        { key: 'businessType', label: 'ビジネスタイプ', type: 'select', required: true, options: ['B2B', 'B2C', 'EC', 'SaaS', 'サービス業'] },
        { key: 'targetMarket', label: 'ターゲット市場', type: 'text', required: true, placeholder: '年齢層、業界、興味など' },
        { key: 'marketingGoal', label: 'マーケティング目標', type: 'select', required: true, options: ['リード獲得', '売上向上', '認知拡大', 'リテンション'] },
        { key: 'budget', label: '月予算（万円）', type: 'number', required: true, placeholder: '50' }
      ]
    }
  },

  // ゲーム/アプリ/ソフトウェア/サービス
  {
    id: 'game-app-development',
    name: 'ゲーム・アプリ開発',
    description: 'Unity/React Nativeを使用したクロスプラットフォーム開発',
    category: 'ゲーム・アプリ',
    tags: ['ゲーム', 'アプリ', 'ソフトウェア', 'サービス', 'Unity', 'React Native', 'モバイル'],
    icon: <Gamepad2 size={24} />,
    technologies: ['Unity', 'React Native', 'C#', 'TypeScript', 'Firebase'],
    agentConfig: {
      king: 'ゲーム開発王',
      generals: ['ゲームプレイ将', 'UI/UX将'],
      soldiers: ['プログラマー兵', 'デザイナー兵', 'テスター兵']
    },
    estimatedDuration: '6-16週間',
    difficulty: 'advanced',
    yamlStructure: {
      title: 'ゲーム・アプリ開発設定',
      dialogFields: [
        { key: 'productType', label: 'プロダクトタイプ', type: 'select', required: true, options: ['モバイルゲーム', 'Webアプリ', 'デスクトップソフト', 'SaaSサービス'] },
        { key: 'gameGenre', label: 'ゲームジャンル', type: 'text', required: false, placeholder: 'RPG、パズル、アクションなど' },
        { key: 'monetization', label: '収益化方法', type: 'select', required: true, options: ['無料（広告）', '買い切り', 'サブスク', 'フリーミアム'] },
        { key: 'platform', label: 'プラットフォーム', type: 'select', required: true, options: ['iOS/Android', 'Web', 'PC', 'Nintendo Switch'] }
      ]
    }
  },

  // デザイン/宣材
  {
    id: 'design-portfolio-system',
    name: 'デザイン・宣材制作システム',
    description: 'AI支援機能付きのブランディング・宣材制作プラットフォーム',
    category: 'デザイン',
    tags: ['デザイン', '宣材', 'ブランディング', 'ロゴ', 'ポートフォリオ', 'Figma', 'Adobe'],
    icon: <Palette size={24} />,
    technologies: ['Next.js', 'Figma API', 'Canvas API', 'Midjourney API', 'Canva API'],
    agentConfig: {
      king: 'デザイン王',
      generals: ['ブランディング将', 'グラフィック将'],
      soldiers: ['ロゴデザイン兵', 'レイアウト兵', '色彩設計兵']
    },
    estimatedDuration: '3-6週間',
    difficulty: 'intermediate',
    yamlStructure: {
      title: 'デザイン制作設定',
      dialogFields: [
        { key: 'designType', label: 'デザインタイプ', type: 'select', required: true, options: ['ロゴ・ブランディング', 'Webデザイン', '印刷物', 'プレゼン資料'] },
        { key: 'brandTone', label: 'ブランドトーン', type: 'text', required: true, placeholder: 'モダン、クラシック、ポップなど' },
        { key: 'colorPreference', label: '色彩傾向', type: 'text', required: false, placeholder: '青系、暖色系、モノクロなど' },
        { key: 'targetIndustry', label: '対象業界', type: 'text', required: true, placeholder: 'IT、医療、教育、飲食など' }
      ]
    }
  },

  // ワークフロー/自動化/オートメーション
  {
    id: 'workflow-automation',
    name: 'ワークフロー自動化システム',
    description: 'Zapier風の包括的ビジネスプロセス自動化プラットフォーム',
    category: 'オートメーション',
    tags: ['ワークフロー', '自動化', 'オートメーション', 'RPA', 'Zapier', 'API連携', 'ビジネスプロセス'],
    icon: <Workflow size={24} />,
    technologies: ['Node.js', 'Redis', 'PostgreSQL', 'Docker', 'API Gateway'],
    agentConfig: {
      king: '自動化王',
      generals: ['ワークフロー将', 'インテグレーション将'],
      soldiers: ['API連携兵', 'スケジュール兵', 'モニタリング兵']
    },
    estimatedDuration: '4-10週間',
    difficulty: 'advanced',
    yamlStructure: {
      title: 'ワークフロー自動化設定',
      dialogFields: [
        { key: 'automationType', label: '自動化タイプ', type: 'select', required: true, options: ['データ処理', 'コミュニケーション', '管理業務', 'マーケティング'] },
        { key: 'triggerType', label: 'トリガー方式', type: 'select', required: true, options: ['時間ベース', 'イベントベース', 'Webhook', '手動実行'] },
        { key: 'integrationServices', label: '連携サービス', type: 'text', required: true, placeholder: 'Slack, Gmail, Shopify, Salesforceなど' },
        { key: 'processingVolume', label: '処理量（月間）', type: 'select', required: true, options: ['〜1,000件', '〜10,000件', '〜100,000件', '100,000件以上'] }
      ]
    }
  },

  // EC/購入
  {
    id: 'ecommerce-platform',
    name: 'ECプラットフォーム',
    description: 'AI推奨エンジン付きの高機能オンラインショップシステム',
    category: 'EC・購入',
    tags: ['購入', 'EC', 'オンラインショップ', 'Shopify', 'Stripe', 'カート', '在庫管理'],
    icon: <ShoppingBag size={24} />,
    technologies: ['Next.js', 'Stripe', 'Shopify API', 'PostgreSQL', 'Redis'],
    agentConfig: {
      king: 'EC運営王',
      generals: ['商品管理将', '決済将'],
      soldiers: ['在庫管理兵', 'マーケティング兵', 'カスタマーサポート兵']
    },
    estimatedDuration: '5-10週間',
    difficulty: 'intermediate',
    yamlStructure: {
      title: 'ECプラットフォーム設定',
      dialogFields: [
        { key: 'storeType', label: 'ストアタイプ', type: 'select', required: true, options: ['単品販売', '複数商品', 'デジタル商品', 'サブスクリプション'] },
        { key: 'productCategory', label: '商品カテゴリ', type: 'text', required: true, placeholder: 'ファッション、家電、食品など' },
        { key: 'targetMarket', label: 'ターゲット市場', type: 'select', required: true, options: ['国内', 'グローバル', 'アジア', '特定地域'] },
        { key: 'paymentMethods', label: '決済方法', type: 'text', required: true, placeholder: 'クレカ、PayPay、銀行振込など' }
      ]
    }
  },

  // ソーシャル/運用/SNS
  {
    id: 'social-media-management',
    name: 'ソーシャルメディア運用システム',
    description: 'AI投稿生成・分析機能付きのSNS総合管理プラットフォーム',
    category: 'ソーシャル運用',
    tags: ['ソーシャル', '運用', 'SNS', 'Twitter', 'Instagram', 'Facebook', 'TikTok', 'AI投稿'],
    icon: <Share2 size={24} />,
    technologies: ['Next.js', 'Twitter API', 'Instagram API', 'OpenAI API', 'Buffer API'],
    agentConfig: {
      king: 'SNS運用王',
      generals: ['コンテンツ将', 'エンゲージメント将'],
      soldiers: ['投稿作成兵', '画像生成兵', '分析兵']
    },
    estimatedDuration: '3-6週間',
    difficulty: 'intermediate',
    yamlStructure: {
      title: 'SNS運用設定',
      dialogFields: [
        { key: 'platforms', label: '対象プラットフォーム', type: 'text', required: true, placeholder: 'Twitter, Instagram, TikTokなど' },
        { key: 'contentType', label: 'コンテンツタイプ', type: 'select', required: true, options: ['テキスト中心', '画像中心', '動画中心', 'ミックス'] },
        { key: 'postingFrequency', label: '投稿頻度', type: 'select', required: true, options: ['1日1回', '1日3回', '週3回', 'カスタム'] },
        { key: 'brandVoice', label: 'ブランドボイス', type: 'text', required: true, placeholder: 'フレンドリー、プロフェッショナル、カジュアルなど' }
      ]
    }
  },

  // 予約/コンシェルジュ/サービス業
  {
    id: 'booking-concierge-system',
    name: '予約・コンシェルジュシステム',
    description: 'AI対応の包括的予約管理・コンシェルジュサービスプラットフォーム',
    category: '予約・サービス業',
    tags: ['予約', 'コンシェルジュ', '出前', 'ホテル', 'レストラン', 'カフェ', 'バー', 'ショップ', 'サービス業'],
    icon: <Calendar size={24} />,
    technologies: ['Next.js', 'PostgreSQL', 'Stripe', 'Twilio', 'Google Calendar API'],
    agentConfig: {
      king: 'サービス運営王',
      generals: ['予約管理将', 'カスタマー将'],
      soldiers: ['予約調整兵', '顧客対応兵', 'スケジュール兵']
    },
    estimatedDuration: '4-8週間',
    difficulty: 'intermediate',
    yamlStructure: {
      title: '予約・コンシェルジュ設定',
      dialogFields: [
        { key: 'businessType', label: 'ビジネスタイプ', type: 'select', required: true, options: ['レストラン', 'ホテル', '美容院', 'クリニック', 'コンサル'] },
        { key: 'bookingType', label: '予約タイプ', type: 'select', required: true, options: ['時間予約', '日付予約', 'テーブル予約', 'サービス予約'] },
        { key: 'conciergeLevel', label: 'コンシェルジュ機能', type: 'select', required: true, options: ['基本予約のみ', 'AI チャット対応', '人間オペレーター', 'ハイブリッド'] },
        { key: 'paymentTiming', label: '決済タイミング', type: 'select', required: true, options: ['予約時決済', '当日決済', '事後決済', '選択可能'] }
      ]
    }
  },

  // HP/ウェブサイト/LP/まとめリンク
  {
    id: 'website-builder',
    name: 'ウェブサイトビルダー',
    description: 'AI生成機能付きのコーポレートサイト・LP制作プラットフォーム',
    category: 'ウェブサイト制作',
    tags: ['HP', 'ウェブサイト', 'LP', 'まとめリンク', 'コーポレートサイト', 'ランディングページ', 'CMS'],
    icon: <Globe size={24} />,
    technologies: ['Next.js', 'Sanity CMS', 'Vercel', 'Tailwind CSS', 'Framer Motion'],
    agentConfig: {
      king: 'Web制作王',
      generals: ['UI/UX将', 'SEO将'],
      soldiers: ['デザイン兵', 'コーディング兵', 'コンテンツ兵']
    },
    estimatedDuration: '2-5週間',
    difficulty: 'intermediate',
    yamlStructure: {
      title: 'ウェブサイト制作設定',
      dialogFields: [
        { key: 'siteType', label: 'サイトタイプ', type: 'select', required: true, options: ['コーポレートサイト', 'ランディングページ', 'ポートフォリオ', 'まとめリンク'] },
        { key: 'targetPages', label: 'ページ数', type: 'select', required: true, options: ['1ページ', '5-10ページ', '10-50ページ', '50ページ以上'] },
        { key: 'designStyle', label: 'デザインスタイル', type: 'text', required: true, placeholder: 'ミニマル、コーポレート、クリエイティブなど' },
        { key: 'seoFocus', label: 'SEO重視度', type: 'select', required: true, options: ['基本SEO', '中級SEO', '上級SEO', 'SEO専門'] }
      ]
    }
  },

  // 仮想通貨/クリプト/NFT/ジェネラティブアート
  {
    id: 'blockchain-platform',
    name: 'ブロックチェーン・NFTプラットフォーム',
    description: 'Web3対応のNFT・ジェネラティブアート制作・取引プラットフォーム',
    category: 'ブロックチェーン',
    tags: ['仮想通貨', 'クリプト', 'NFT', 'ジェネラティブアート', 'Web3', 'DeFi', 'DAO', 'スマートコントラクト'],
    icon: <Bitcoin size={24} />,
    technologies: ['Solidity', 'Ethers.js', 'IPFS', 'OpenSea API', 'MetaMask', 'Hardhat'],
    agentConfig: {
      king: 'ブロックチェーン王',
      generals: ['スマートコントラクト将', 'NFT将'],
      soldiers: ['Solidity兵', 'フロントエンド兵', 'セキュリティ兵']
    },
    estimatedDuration: '8-16週間',
    difficulty: 'advanced',
    yamlStructure: {
      title: 'ブロックチェーンプロジェクト設定',
      dialogFields: [
        { key: 'blockchainType', label: 'ブロックチェーン', type: 'select', required: true, options: ['Ethereum', 'Polygon', 'Solana', 'Binance Smart Chain'] },
        { key: 'projectType', label: 'プロジェクトタイプ', type: 'select', required: true, options: ['NFTコレクション', 'DeFiプロトコル', 'DAOプラットフォーム', 'ゲームトークン'] },
        { key: 'nftType', label: 'NFTタイプ', type: 'select', required: false, options: ['PFP', 'ジェネラティブアート', 'ユーティリティNFT', '音楽NFT'] },
        { key: 'tokenomics', label: 'トークンエコノミクス', type: 'select', required: true, options: ['なし', '基本トークン', '複雑なエコノミクス', 'ガバナンストークン'] }
      ]
    }
  },

  // リサーチ/調査/分析/研究
  {
    id: 'research-analysis-platform',
    name: 'リサーチ・分析プラットフォーム',
    description: 'AI支援機能付きの包括的データ収集・分析・レポート生成システム',
    category: 'リサーチ・分析',
    tags: ['リサーチ', '調査', '分析', '研究', 'データサイエンス', 'アンケート', 'レポート', 'BI'],
    icon: <Search size={24} />,
    technologies: ['Python', 'Jupyter', 'Pandas', 'Plotly', 'PostgreSQL', 'Apache Airflow'],
    agentConfig: {
      king: 'リサーチ王',
      generals: ['データ分析将', 'レポート将'],
      soldiers: ['データ収集兵', '統計分析兵', '可視化兵']
    },
    estimatedDuration: '4-8週間',
    difficulty: 'advanced',
    yamlStructure: {
      title: 'リサーチ・分析設定',
      dialogFields: [
        { key: 'researchType', label: 'リサーチタイプ', type: 'select', required: true, options: ['市場調査', '学術研究', 'ユーザー調査', '競合分析'] },
        { key: 'dataSource', label: 'データソース', type: 'select', required: true, options: ['アンケート', 'Webスクレイピング', 'API取得', '既存データ'] },
        { key: 'analysisMethod', label: '分析手法', type: 'select', required: true, options: ['記述統計', '機械学習', '時系列分析', 'テキスト分析'] },
        { key: 'outputFormat', label: '出力形式', type: 'select', required: true, options: ['ダッシュボード', 'PDFレポート', 'プレゼン資料', 'データファイル'] }
      ]
    }
  },

  // メタバース/デジタルツイン
  {
    id: 'metaverse-platform',
    name: 'メタバース・デジタルツインプラットフォーム',
    description: 'VR/AR対応の3D仮想空間・デジタルツイン構築プラットフォーム',
    category: 'メタバース',
    tags: ['メタバース', 'デジタルツイン', 'VR', 'AR', '3D', 'Unity', 'バーチャル空間', 'アバター'],
    icon: <Boxes size={24} />,
    technologies: ['Unity', 'WebXR', 'Three.js', 'Blender', 'Photon', 'Ready Player Me'],
    agentConfig: {
      king: 'メタバース王',
      generals: ['3D空間将', 'アバター将'],
      soldiers: ['3Dモデリング兵', 'ネットワーク兵', 'インタラクション兵']
    },
    estimatedDuration: '10-20週間',
    difficulty: 'advanced',
    yamlStructure: {
      title: 'メタバース・デジタルツイン設定',
      dialogFields: [
        { key: 'spaceType', label: '空間タイプ', type: 'select', required: true, options: ['ソーシャル空間', 'ビジネス会議', 'エンタメ空間', '教育・学習'] },
        { key: 'platformTarget', label: 'プラットフォーム', type: 'select', required: true, options: ['VRヘッドセット', 'Webブラウザ', 'モバイルAR', 'PC・Mac'] },
        { key: 'avatarSystem', label: 'アバターシステム', type: 'select', required: true, options: ['既存アバター', 'カスタムアバター', 'AI生成', 'VRMインポート'] },
        { key: 'interactionLevel', label: 'インタラクション', type: 'select', required: true, options: ['観覧のみ', '基本操作', '物理演算', 'フル インタラクション'] }
      ]
    }
  }
];

interface ProjectTemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateProject: (template: ProjectTemplate, projectName: string) => void;
}

export default function ProjectTemplateModal({ isOpen, onClose, onCreateProject }: ProjectTemplateModalProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<ProjectTemplate | null>(null);
  const [projectName, setProjectName] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showYamlDialog, setShowYamlDialog] = useState(false);
  const [yamlFormData, setYamlFormData] = useState<Record<string, string>>({});

  const categories = ['all', ...Array.from(new Set(projectTemplates.map(t => t.category)))];
  const filteredTemplates = projectTemplates.filter(template => {
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesSearch = searchQuery === '' || 
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  const handleCreate = () => {
    if (selectedTemplate && projectName.trim()) {
      setShowYamlDialog(true);
    }
  };

  const handleYamlSubmit = () => {
    if (selectedTemplate && projectName.trim()) {
      // yamlFormDataを含めてプロジェクトを作成
      onCreateProject({ ...selectedTemplate, yamlFormData }, projectName.trim());
      onClose();
      setSelectedTemplate(null);
      setProjectName('');
      setYamlFormData({});
      setShowYamlDialog(false);
    }
  };

  const handleYamlFieldChange = (key: string, value: string) => {
    setYamlFormData(prev => ({ ...prev, [key]: value }));
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'badge-success';
      case 'intermediate': return 'badge-warning';
      case 'advanced': return 'badge-error';
      default: return 'badge-primary';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal modal-open">
      <div className="modal-box max-w-4xl h-[80vh] flex flex-col">
        {/* ヘッダー */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">新規プロジェクト作成</h2>
          <button 
            className="btn btn-ghost btn-sm btn-circle"
            onClick={onClose}
          >
            <X size={20} />
          </button>
        </div>

        {/* 検索とフィルター */}
        <div className="mb-4 space-y-3">
          {/* 検索フィールド */}
          <div className="form-control">
            <input
              type="text"
              placeholder="テンプレート・タグで検索..."
              className="input input-bordered w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          {/* カテゴリフィルター */}
          <div className="flex gap-2 overflow-x-auto">
            {categories.map(category => (
              <button
                key={category}
                className={`btn btn-sm ${selectedCategory === category ? 'btn-primary' : 'btn-outline'}`}
                onClick={() => setSelectedCategory(category)}
              >
                {category === 'all' ? 'すべて' : category}
              </button>
            ))}
          </div>
        </div>

        {/* テンプレート選択 */}
        <div className="flex-1 overflow-y-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {filteredTemplates.map(template => (
              <div
                key={template.id}
                className={`card card-compact bg-base-200 cursor-pointer transition-all hover:shadow-lg ${
                  selectedTemplate?.id === template.id ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => setSelectedTemplate(template)}
              >
                <div className="card-body">
                  <div className="flex items-start gap-3">
                    <div className="text-primary">{template.icon}</div>
                    <div className="flex-1">
                      <h3 className="card-title text-lg">{template.name}</h3>
                      <p className="text-sm text-base-content/70 mb-3">{template.description}</p>
                      
                      {/* タグ */}
                      <div className="flex flex-wrap gap-1 mb-2">
                        {template.tags.slice(0, 4).map(tag => (
                          <span key={tag} className="badge badge-secondary badge-xs">
                            {tag}
                          </span>
                        ))}
                        {template.tags.length > 4 && (
                          <span className="badge badge-secondary badge-xs">
                            +{template.tags.length - 4}
                          </span>
                        )}
                      </div>
                      
                      {/* 技術スタック */}
                      <div className="flex flex-wrap gap-1 mb-2">
                        {template.technologies.slice(0, 3).map(tech => (
                          <span key={tech} className="badge badge-outline badge-xs">
                            {tech}
                          </span>
                        ))}
                        {template.technologies.length > 3 && (
                          <span className="badge badge-outline badge-xs">
                            +{template.technologies.length - 3}
                          </span>
                        )}
                      </div>
                      
                      {/* メタ情報 */}
                      <div className="flex items-center gap-2 text-xs">
                        <span className={`badge badge-xs ${getDifficultyColor(template.difficulty)}`}>
                          {template.difficulty === 'beginner' ? '初級' : 
                           template.difficulty === 'intermediate' ? '中級' : '上級'}
                        </span>
                        <span className="text-base-content/60">{template.estimatedDuration}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 詳細情報 */}
        {selectedTemplate && (
          <div className="bg-base-300/50 rounded-lg p-4 mb-4">
            <h4 className="font-semibold mb-2">選択されたテンプレート: {selectedTemplate.name}</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <h5 className="font-medium mb-1">関連タグ:</h5>
                <div className="flex flex-wrap gap-1">
                  {selectedTemplate.tags.map(tag => (
                    <span key={tag} className="badge badge-secondary badge-sm">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
              <div>
                <h5 className="font-medium mb-1">技術スタック:</h5>
                <div className="flex flex-wrap gap-1">
                  {selectedTemplate.technologies.map(tech => (
                    <span key={tech} className="badge badge-outline badge-sm">
                      {tech}
                    </span>
                  ))}
                </div>
              </div>
              <div>
                <h5 className="font-medium mb-1">エージェント構成:</h5>
                <div className="space-y-1">
                  <div>👑 {selectedTemplate.agentConfig.king}</div>
                  <div>⚔️ {selectedTemplate.agentConfig.generals.join(', ')}</div>
                  <div>🛡️ {selectedTemplate.agentConfig.soldiers.join(', ')}</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* プロジェクト名入力 */}
        <div className="form-control mb-4">
          <label className="label">
            <span className="label-text font-medium">プロジェクト名</span>
          </label>
          <input
            type="text"
            placeholder="例: 瞑想アプリ、投資ボット"
            className="input input-bordered"
            value={projectName}
            onChange={(e) => setProjectName(e.target.value)}
          />
        </div>

        {/* アクションボタン */}
        <div className="flex gap-2 justify-end">
          <button 
            className="btn btn-ghost"
            onClick={onClose}
          >
            キャンセル
          </button>
          <button 
            className="btn btn-primary"
            onClick={handleCreate}
            disabled={!selectedTemplate || !projectName.trim()}
          >
            <Zap size={16} />
            プロジェクトを作成
          </button>
        </div>
      </div>

      {/* YAMLフィールドダイアログ */}
      {showYamlDialog && selectedTemplate && (
        <div className="modal modal-open">
          <div className="modal-box max-w-2xl">
            <h3 className="font-bold text-lg mb-4">{selectedTemplate.yamlStructure.title}</h3>
            
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {selectedTemplate.yamlStructure.dialogFields.map((field) => (
                <div key={field.key} className="form-control">
                  <label className="label">
                    <span className="label-text font-medium">
                      {field.label}
                      {field.required && <span className="text-error ml-1">*</span>}
                    </span>
                  </label>
                  
                  {field.type === 'select' ? (
                    <select 
                      className="select select-bordered"
                      value={yamlFormData[field.key] || ''}
                      onChange={(e) => handleYamlFieldChange(field.key, e.target.value)}
                      required={field.required}
                    >
                      <option value="">選択してください</option>
                      {field.options?.map(option => (
                        <option key={option} value={option}>{option}</option>
                      ))}
                    </select>
                  ) : field.type === 'textarea' ? (
                    <textarea
                      className="textarea textarea-bordered"
                      placeholder={field.placeholder}
                      value={yamlFormData[field.key] || ''}
                      onChange={(e) => handleYamlFieldChange(field.key, e.target.value)}
                      required={field.required}
                      rows={3}
                    />
                  ) : field.type === 'number' ? (
                    <input
                      type="number"
                      className="input input-bordered"
                      placeholder={field.placeholder}
                      value={yamlFormData[field.key] || ''}
                      onChange={(e) => handleYamlFieldChange(field.key, e.target.value)}
                      required={field.required}
                    />
                  ) : field.type === 'boolean' ? (
                    <input
                      type="checkbox"
                      className="checkbox"
                      checked={yamlFormData[field.key] === 'true'}
                      onChange={(e) => handleYamlFieldChange(field.key, e.target.checked.toString())}
                    />
                  ) : (
                    <input
                      type="text"
                      className="input input-bordered"
                      placeholder={field.placeholder}
                      value={yamlFormData[field.key] || ''}
                      onChange={(e) => handleYamlFieldChange(field.key, e.target.value)}
                      required={field.required}
                    />
                  )}
                </div>
              ))}
            </div>

            <div className="modal-action">
              <button 
                className="btn btn-ghost"
                onClick={() => setShowYamlDialog(false)}
              >
                戻る
              </button>
              <button 
                className="btn btn-primary"
                onClick={handleYamlSubmit}
                disabled={selectedTemplate.yamlStructure.dialogFields
                  .filter(f => f.required)
                  .some(f => !yamlFormData[f.key]?.trim())
                }
              >
                <Zap size={16} />
                プロジェクト作成実行
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}