'use client';

import { useState, useEffect, memo, useCallback, useMemo } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import { GripVertical, Settings } from 'lucide-react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

// ウィジェットコンポーネント（React.memoで最適化）
const WidgetWrapper = memo(({ children, title, onSettings }: { 
  children: React.ReactNode; 
  title: string; 
  onSettings?: () => void;
}) => {
  const handleSettingsClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onSettings?.();
  }, [onSettings]);

  return (
    <div className="h-full bg-base-200 rounded-lg shadow-xl neo-depth overflow-hidden">
      <div className="react-grid-dragHandleClassName flex items-center justify-between p-3 border-b border-base-content/10 bg-base-300/50 cursor-move">
        <h3 className="font-semibold text-sm truncate flex-1 select-none">{title}</h3>
        <div className="flex items-center gap-1">
          {onSettings && (
            <button 
              className="btn btn-ghost btn-xs"
              onClick={handleSettingsClick}
            >
              <Settings size={12} />
            </button>
          )}
          <GripVertical size={14} className="text-base-content/40" />
        </div>
      </div>
      <div className="p-3 h-[calc(100%-48px)] overflow-hidden no-drag-content">
        {children}
      </div>
    </div>
  );
});

WidgetWrapper.displayName = 'WidgetWrapper';

// 個別ウィジェット（React.memoで最適化）
const VisionWidget = memo(() => {
  const [personalGoal, setPersonalGoal] = useState('AI開発プラットフォームの完成');
  const [targetDate, setTargetDate] = useState('2025年末');
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = useCallback(() => {
    setIsEditing(false);
  }, []);

  const handleEdit = useCallback(() => {
    setIsEditing(true);
  }, []);

  const handleGoalChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setPersonalGoal(e.target.value);
  }, []);

  const handleDateChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setTargetDate(e.target.value);
  }, []);
  
  return (
    <div className="h-full flex flex-col">
      {isEditing ? (
        <div className="flex-1">
          <input
            className="input input-bordered input-sm w-full mb-2"
            value={personalGoal}
            onChange={handleGoalChange}
            placeholder="個人目標を入力..."
            autoFocus
          />
          <input
            className="input input-bordered input-sm w-full mb-3"
            value={targetDate}
            onChange={handleDateChange}
            placeholder="目標期日..."
          />
          <div className="flex gap-1">
            <button className="btn btn-xs btn-primary" onClick={handleSave}>保存</button>
            <button className="btn btn-xs btn-ghost" onClick={handleSave}>キャンセル</button>
          </div>
        </div>
      ) : (
        <div className="h-full flex flex-col" onClick={handleEdit}>
          <h2 className="text-lg font-bold mb-2 truncate cursor-pointer hover:text-primary transition-colors">{personalGoal}</h2>
          <p className="text-xs text-base-content/70 mb-3 truncate">目標: {targetDate}</p>
          <div className="flex gap-1 flex-wrap">
            <span className="badge badge-warning badge-xs">個人目標</span>
            <span className="badge badge-primary badge-xs">編集可能</span>
            <span className="badge badge-success badge-xs">追跡中</span>
          </div>
        </div>
      )}
    </div>
  );
});

VisionWidget.displayName = 'VisionWidget';

const TaskWidget = memo(() => {
  const [personalTasks, setPersonalTasks] = useState([
    { id: 1, task: 'Claude Code適用音調査', completed: false, priority: 'high' },
    { id: 2, task: 'ダッシュボード実装', completed: true, priority: 'high' },
    { id: 3, task: 'プロジェクト要件整理', completed: false, priority: 'medium' },
    { id: 4, task: 'UI/UX改善', completed: false, priority: 'medium' }
  ]);
  const [newTask, setNewTask] = useState('');
  
  // useMemoで計算結果をメモ化
  const { completedCount, progressPercentage } = useMemo(() => {
    const completed = personalTasks.filter(t => t.completed).length;
    return {
      completedCount: completed,
      progressPercentage: (completed / personalTasks.length) * 100
    };
  }, [personalTasks]);
  
  const toggleTask = useCallback((id: number) => {
    setPersonalTasks(prev => 
      prev.map(task => 
        task.id === id ? { ...task, completed: !task.completed } : task
      )
    );
  }, []);
  
  const addTask = useCallback(() => {
    if (newTask.trim()) {
      const newTaskObj = {
        id: Date.now(),
        task: newTask.trim(),
        completed: false,
        priority: 'medium' as const
      };
      setPersonalTasks(prev => [...prev, newTaskObj]);
      setNewTask('');
    }
  }, [newTask]);

  const handleNewTaskChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setNewTask(e.target.value);
  }, []);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      addTask();
    }
  }, [addTask]);
  
  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-semibold">個人タスク</span>
        <span className="text-xs text-base-content/60">{completedCount}/{personalTasks.length}</span>
      </div>
      
      {/* 常時表示の入力フィールド */}
      <div className="flex gap-1 mb-2">
        <input
          className="input input-xs flex-1 bg-base-100 border-base-content/20"
          value={newTask}
          onChange={handleNewTaskChange}
          placeholder="新しいタスクを入力..."
          onKeyPress={handleKeyPress}
        />
        <button 
          className="btn btn-xs btn-primary" 
          onClick={addTask}
          disabled={!newTask.trim()}
        >
          追加
        </button>
      </div>
      
      <div className="space-y-2 flex-1 overflow-y-auto">
        {personalTasks.map(item => (
          <TaskItem 
            key={item.id} 
            item={item} 
            onToggle={toggleTask}
          />
        ))}
      </div>
      
      <div className="w-full bg-base-300 rounded-full h-1.5 mt-2">
        <div className="bg-primary h-1.5 rounded-full transition-all" style={{width: `${progressPercentage}%`}}></div>
      </div>
    </div>
  );
});

TaskWidget.displayName = 'TaskWidget';

// TaskItemコンポーネント（個別最適化）
const TaskItem = memo(({ item, onToggle }: {
  item: { id: number; task: string; completed: boolean; priority: string };
  onToggle: (id: number) => void;
}) => {
  const handleToggle = useCallback(() => {
    onToggle(item.id);
  }, [item.id, onToggle]);

  return (
    <div className="flex items-center gap-2 text-xs">
      <input
        type="checkbox"
        className="checkbox checkbox-xs"
        checked={item.completed}
        onChange={handleToggle}
      />
      <span className={`flex-1 ${item.completed ? 'line-through text-base-content/50' : ''}`}>
        {item.task}
      </span>
      <span className={`badge badge-xs ${item.priority === 'high' ? 'badge-error' : 'badge-warning'}`}>
        {item.priority}
      </span>
    </div>
  );
});

TaskItem.displayName = 'TaskItem';

const TimeBlockWidget = () => {
  const [selectedSlot, setSelectedSlot] = useState<string | null>(null);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const [resizingSlot, setResizingSlot] = useState<string | null>(null);
  const [resizeStartY, setResizeStartY] = useState<number>(0);
  const [resizeStartDuration, setResizeStartDuration] = useState<number>(0);
  const [editingTask, setEditingTask] = useState<string | null>(null);
  const [tempTaskValue, setTempTaskValue] = useState('');
  const [contextMenu, setContextMenu] = useState<{x: number, y: number, slotId: string} | null>(null);
  const [timeSlots, setTimeSlots] = useState([
    { id: '1', time: '12:00', task: '昼食', durationMinutes: 60, color: 'bg-info' },
    { id: '2', time: '13:00', task: 'コーディング', durationMinutes: 120, color: 'bg-primary' },
    { id: '3', time: '15:00', task: 'ミーティング', durationMinutes: 60, color: 'bg-secondary' },
    { id: '4', time: '16:00', task: 'デザイン', durationMinutes: 180, color: 'bg-accent' },
    { id: '5', time: '19:00', task: 'レビュー', durationMinutes: 60, color: 'bg-success' },
  ]);

  // 色選択オプション
  const colorOptions = [
    { value: 'bg-info', label: '青', preview: '#3abff8' },
    { value: 'bg-primary', label: '紫', preview: '#a855f7' },
    { value: 'bg-secondary', label: 'ピンク', preview: '#ec4899' },
    { value: 'bg-accent', label: '緑', preview: '#10b981' },
    { value: 'bg-success', label: '成功', preview: '#16a34a' },
    { value: 'bg-warning', label: '警告', preview: '#f59e0b' },
    { value: 'bg-error', label: 'エラー', preview: '#dc2626' },
  ];

  // 時間選択オプション (30分刻み)
  const timeOptions = Array.from({ length: 48 }, (_, i) => {
    const hour = Math.floor(i / 2);
    const minute = (i % 2) * 30;
    return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
  });

  const changeSlotColor = (slotId: string, newColor: string) => {
    setTimeSlots(prev => 
      prev.map(slot => 
        slot.id === slotId ? { ...slot, color: newColor } : slot
      )
    );
  };

  const changeSlotTime = (slotId: string, newTime: string) => {
    setTimeSlots(prev => {
      const updated = prev.map(slot => 
        slot.id === slotId ? { ...slot, time: newTime } : slot
      );
      return recalculateTimeSlots(updated);
    });
  };

  const startTaskEdit = (slotId: string, currentTask: string) => {
    setEditingTask(slotId);
    setTempTaskValue(currentTask);
  };

  const saveTaskEdit = () => {
    if (!editingTask) return;
    setTimeSlots(prev => 
      prev.map(slot => 
        slot.id === editingTask ? { ...slot, task: tempTaskValue } : slot
      )
    );
    setEditingTask(null);
    setTempTaskValue('');
  };

  const cancelTaskEdit = () => {
    setEditingTask(null);
    setTempTaskValue('');
  };

  // 新しいタイムスロット追加
  const addNewTimeSlot = () => {
    const lastSlot = timeSlots.length > 0 
      ? timeSlots.sort((a, b) => parseTimeToMinutes(a.time) - parseTimeToMinutes(b.time)).slice(-1)[0]
      : null;
    
    let newTime = '';
    if (lastSlot) {
      const endTime = parseTimeToMinutes(lastSlot.time) + lastSlot.durationMinutes;
      newTime = formatMinutesToTime(endTime);
    } else {
      const now = new Date();
      newTime = `${now.getHours().toString().padStart(2, '0')}:00`;
    }
    
    const newSlot = {
      id: Date.now().toString(),
      time: newTime,
      task: '新しいタスク',
      durationMinutes: 60,
      color: 'bg-primary'
    };
    
    setTimeSlots(prev => [...prev, newSlot]);
  };

  // タイムスロット削除（自動時間調節付き）
  const deleteTimeSlot = (slotId: string) => {
    setTimeSlots(prev => {
      const filtered = prev.filter(slot => slot.id !== slotId);
      return recalculateTimeSlots(filtered);
    });
  };

  // 時間計算ユーティリティ
  const parseTimeToMinutes = (timeStr: string): number => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  };

  const formatMinutesToTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  };

  const formatDuration = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours === 0) return `${mins}m`;
    if (mins === 0) return `${hours}h`;
    return `${hours}h${mins}m`;
  };

  // 時間の自動調節
  const recalculateTimeSlots = (slots: any[]) => {
    if (slots.length === 0) return slots;
    
    const sortedSlots = [...slots].sort((a, b) => parseTimeToMinutes(a.time) - parseTimeToMinutes(b.time));
    
    // 最初のスロットの時間はそのまま
    const adjustedSlots = [sortedSlots[0]];
    
    // 以降のスロットは前のスロットの終了時間に合わせる
    for (let i = 1; i < sortedSlots.length; i++) {
      const prevSlot = adjustedSlots[i - 1];
      const prevEndTime = parseTimeToMinutes(prevSlot.time) + prevSlot.durationMinutes;
      const newTime = formatMinutesToTime(prevEndTime);
      
      adjustedSlots.push({
        ...sortedSlots[i],
        time: newTime
      });
    }
    
    return adjustedSlots;
  };

  // ドラッグ&ドロップ処理（時間自動調節付き）
  const handleDragStart = (index: number) => {
    setDraggedIndex(index);
  };

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    setDragOverIndex(index);
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (draggedIndex === null) return;
    
    const newTimeSlots = [...timeSlots];
    const draggedSlot = newTimeSlots[draggedIndex];
    newTimeSlots.splice(draggedIndex, 1);
    newTimeSlots.splice(dropIndex, 0, draggedSlot);
    
    // 並び替え後に時間を自動調節
    const adjustedSlots = recalculateTimeSlots(newTimeSlots);
    
    setTimeSlots(adjustedSlots);
    setDraggedIndex(null);
    setDragOverIndex(null);
  };


  // 継続時間調整（リサイズと時間調整の分離）
  const adjustDuration = (slotId: string, delta: number) => {
    setTimeSlots(prev => {
      const updated = prev.map(slot => {
        if (slot.id === slotId) {
          const newMinutes = Math.max(15, slot.durationMinutes + delta);
          return { 
            ...slot, 
            durationMinutes: newMinutes
          };
        }
        return slot;
      });
      // 時間調整時は自動時間調節を実行
      return recalculateTimeSlots(updated);
    });
  };

  // リサイズ処理の修正
  const handleResizeStart = (e: React.MouseEvent, slotId: string) => {
    e.preventDefault();
    e.stopPropagation();
    
    const slot = timeSlots.find(s => s.id === slotId);
    if (!slot) return;
    
    setResizingSlot(slotId);
    setResizeStartY(e.clientY);
    setResizeStartDuration(slot.durationMinutes);
    
    document.addEventListener('mousemove', handleResizeMove);
    document.addEventListener('mouseup', handleResizeEnd);
  };

  const handleResizeMove = (e: MouseEvent) => {
    if (!resizingSlot) return;
    
    const deltaY = e.clientY - resizeStartY;
    const minutesPerPixel = 2; // 2分/ピクセル（適度な感度）
    const newDuration = Math.max(15, resizeStartDuration + (deltaY * minutesPerPixel));
    
    // リサイズ時は他のスロットの時間を調整しない（直接変更のみ）
    setTimeSlots(prev => 
      prev.map(slot => 
        slot.id === resizingSlot ? { ...slot, durationMinutes: newDuration } : slot
      )
    );
  };

  const handleResizeEnd = () => {
    if (!resizingSlot) return;
    
    // リサイズ完了時にローカルストレージに保存（サイズ固定）
    const slot = timeSlots.find(s => s.id === resizingSlot);
    if (slot) {
      console.log(`ブロック「${slot.task}」の時間を${formatDuration(slot.durationMinutes)}に変更しました`);
      
      // 時間ブロックデータをローカルストレージに保存
      localStorage.setItem('meta-studio-timeblocks', JSON.stringify(timeSlots));
      
      // 他のブロックの時間も自動調整して保存
      const adjustedSlots = recalculateTimeSlots(timeSlots);
      setTimeSlots(adjustedSlots);
      localStorage.setItem('meta-studio-timeblocks', JSON.stringify(adjustedSlots));
    }
    
    setResizingSlot(null);
    
    document.removeEventListener('mousemove', handleResizeMove);
    document.removeEventListener('mouseup', handleResizeEnd);
  };


  // コンテキストメニューを閉じる関数
  const closeContextMenu = () => setContextMenu(null);

  // 画面クリック時にコンテキストメニューを閉じる
  useEffect(() => {
    if (contextMenu) {
      document.addEventListener('click', closeContextMenu);
      return () => document.removeEventListener('click', closeContextMenu);
    }
  }, [contextMenu]);

  return (
    <div className="h-full flex flex-col bg-gradient-to-b from-base-100 to-base-200/30">
      {/* ヘッダー */}
      <div className="p-3 border-b border-base-content/10 bg-base-100/80 backdrop-blur-sm">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            <h3 className="text-sm font-semibold text-base-content">タイムブロック</h3>
          </div>
          <div className="flex items-center gap-1">
            <div className="text-xs text-base-content/60 bg-base-200 px-2 py-1 rounded-full">
              {new Date().toLocaleDateString('ja-JP', { month: 'short', day: 'numeric' })}
            </div>
            <button
              className="btn btn-xs btn-circle btn-primary hover:scale-105 transition-transform"
              onClick={addNewTimeSlot}
              title="新しいブロックを追加"
            >
              ＋
            </button>
          </div>
        </div>
        {/* 統計サマリー */}
        <div className="flex items-center gap-3 text-xs text-base-content/70">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-primary rounded-full"></div>
            <span>{timeSlots.length}個のブロック</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-success rounded-full"></div>
            <span>{formatDuration(timeSlots.reduce((sum, slot) => sum + slot.durationMinutes, 0))} 合計</span>
          </div>
        </div>
      </div>
      
      {/* タイムライン表示エリア */}
      <div className="flex-1 overflow-y-auto p-3 space-y-3">
        {timeSlots.map((slot, index) => {
          const blockHeight = Math.max(80, slot.durationMinutes * 1.2); // より余裕のある高さ計算
          const isSelected = selectedSlot === slot.id;
          const isResizing = resizingSlot === slot.id;
          const isDraggingOver = dragOverIndex === index;
          
          return (
            <div
              key={slot.id}
              draggable={!isResizing}
              onDragStart={() => handleDragStart(index)}
              onDragOver={(e) => handleDragOver(e, index)}
              onDrop={(e) => handleDrop(e, index)}
              onContextMenu={(e) => {
                e.preventDefault();
                setContextMenu({
                  x: e.clientX,
                  y: e.clientY,
                  slotId: slot.id
                });
              }}
              className={`group relative rounded-xl transition-all duration-200 cursor-grab active:cursor-grabbing 
                ${isSelected ? 'ring-2 ring-blue-400/60 shadow-xl scale-[1.02]' : 'hover:scale-[1.01] hover:shadow-lg'} 
                ${isDraggingOver ? 'ring-2 ring-yellow-400/60 shadow-yellow-200/20' : ''} 
                ${isResizing ? 'ring-2 ring-green-400/60 shadow-green-200/20 scale-[1.02]' : ''} 
                ${slot.color} text-white shadow-md backdrop-blur-sm border border-white/10 overflow-hidden`}
              style={{ 
                height: `${blockHeight}px`, 
                minHeight: '80px'
              }}
              onClick={() => setSelectedSlot(isSelected ? null : slot.id)}
            >
              <div className="p-4 h-full flex flex-col relative">
                {/* タイムライン左の線 */}
                <div className="absolute left-0 top-0 bottom-0 w-1 bg-white/30 rounded-r"></div>
                
                {/* ヘッダー：時間と継続時間 */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <button
                      className="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white text-sm font-bold rounded-lg px-3 py-1.5 transition-all duration-200 hover:scale-105 shadow-sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        const currentIndex = timeOptions.indexOf(slot.time);
                        const nextIndex = (currentIndex + 1) % timeOptions.length;
                        changeSlotTime(slot.id, timeOptions[nextIndex]);
                      }}
                      title="クリックで時間変更"
                    >
                      {slot.time}
                    </button>
                    <div className="flex flex-col">
                      <button
                        className="text-white/60 hover:text-white text-xs py-0.5 px-1 hover:bg-white/10 rounded transition-all"
                        onClick={(e) => {
                          e.stopPropagation();
                          const currentIndex = timeOptions.indexOf(slot.time);
                          const nextIndex = (currentIndex + 1) % timeOptions.length;
                          changeSlotTime(slot.id, timeOptions[nextIndex]);
                        }}
                        title="次の時間"
                      >
                        ▲
                      </button>
                      <button
                        className="text-white/60 hover:text-white text-xs py-0.5 px-1 hover:bg-white/10 rounded transition-all"
                        onClick={(e) => {
                          e.stopPropagation();
                          const currentIndex = timeOptions.indexOf(slot.time);
                          const prevIndex = currentIndex === 0 ? timeOptions.length - 1 : currentIndex - 1;
                          changeSlotTime(slot.id, timeOptions[prevIndex]);
                        }}
                        title="前の時間"
                      >
                        ▼
                      </button>
                    </div>
                  </div>
                  
                  <div className="text-xs font-medium bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 shadow-sm">
                    {formatDuration(slot.durationMinutes)}
                  </div>
                </div>
                
                {/* タスク内容 */}
                <div className="flex-1 mb-3">
                  {editingTask === slot.id ? (
                    <input
                      type="text"
                      className="bg-white/20 backdrop-blur-sm text-white text-base px-3 py-2 rounded-lg border border-white/20 outline-none w-full placeholder-white/50 shadow-sm"
                      value={tempTaskValue}
                      onChange={(e) => setTempTaskValue(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') saveTaskEdit();
                        if (e.key === 'Escape') cancelTaskEdit();
                      }}
                      onBlur={saveTaskEdit}
                      autoFocus={false}
                      placeholder="タスク内容..."
                    />
                  ) : (
                    <div 
                      className="text-base font-medium cursor-pointer hover:bg-white/10 rounded-lg px-3 py-2 transition-all duration-200 min-h-[40px] flex items-center border border-transparent hover:border-white/20"
                      onClick={(e) => {
                        e.stopPropagation();
                        startTaskEdit(slot.id, slot.task);
                      }}
                      title="クリックして編集"
                    >
                      <span className="line-clamp-2 leading-relaxed">{slot.task}</span>
                    </div>
                  )}
                </div>

                {/* アクションバー（選択時のみ表示） */}
                {isSelected && (
                  <div className="border-t border-white/20 pt-3 mt-auto">
                    <div className="flex items-center justify-between gap-2">
                      <div className="flex items-center gap-1">
                        <button
                          className="px-3 py-1.5 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-lg text-xs font-medium transition-all duration-200 flex items-center gap-1 hover:scale-105 shadow-sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            adjustDuration(slot.id, -15);
                          }}
                          title="15分短縮"
                        >
                          ⊖ 15分
                        </button>
                        <button
                          className="px-3 py-1.5 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-lg text-xs font-medium transition-all duration-200 flex items-center gap-1 hover:scale-105 shadow-sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            adjustDuration(slot.id, 15);
                          }}
                          title="15分延長"
                        >
                          ⊕ 15分
                        </button>
                      </div>
                      
                      <button
                        className="px-3 py-1.5 bg-red-500/30 hover:bg-red-500/50 backdrop-blur-sm rounded-lg text-xs font-medium transition-all duration-200 hover:scale-105 shadow-sm border border-red-300/30"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteTimeSlot(slot.id);
                        }}
                        title="ブロック削除"
                      >
                        🗑️ 削除
                      </button>
                    </div>
                  </div>
                )}
              </div>
              
              {/* リサイズハンドル（洗練版） */}
              <div
                className="absolute bottom-0 left-0 right-0 h-4 cursor-ns-resize opacity-0 group-hover:opacity-100 transition-all duration-200 bg-gradient-to-t from-white/30 to-transparent flex items-center justify-center backdrop-blur-sm"
                onMouseDown={(e) => handleResizeStart(e, slot.id)}
                title="ドラッグで時間幅を調整"
              >
                <div className="w-8 h-0.5 bg-white/80 rounded-full shadow-sm"></div>
                <div className="w-8 h-0.5 bg-white/80 rounded-full shadow-sm ml-1"></div>
                <div className="w-8 h-0.5 bg-white/80 rounded-full shadow-sm ml-1"></div>
              </div>
            </div>
          );
        })}
      </div>
      
      {/* コンテキストメニュー */}
      {contextMenu && (
        <div
          className="fixed bg-base-200 border border-base-content/20 rounded-lg shadow-xl z-50 p-2"
          style={{ left: contextMenu.x, top: contextMenu.y }}
          onClick={() => setContextMenu(null)}
        >
          <div className="text-xs font-medium mb-2 text-base-content/70">色変更</div>
          <div className="grid grid-cols-4 gap-1">
            {colorOptions.map(color => (
              <button
                key={color.value}
                className="w-6 h-6 rounded border border-base-content/20 hover:scale-110 transition-transform"
                style={{ backgroundColor: color.preview }}
                onClick={(e) => {
                  e.stopPropagation();
                  changeSlotColor(contextMenu.slotId, color.value);
                  setContextMenu(null);
                }}
                title={color.label}
              />
            ))}
          </div>
        </div>
      )}
      
      {/* フッター操作ガイド */}
      <div className="p-3 border-t border-base-content/10 bg-base-100/50 backdrop-blur-sm">
        <div className="text-xs text-base-content/60 space-y-1">
          <div className="font-medium mb-2">操作ガイド:</div>
          <div className="grid grid-cols-2 gap-x-4 gap-y-1">
            <div>• クリック: ブロック選択</div>
            <div>• ドラッグ: 順序変更</div>
            <div>• 時間ボタン: ▲▼で調整</div>
            <div>• 下端: 時間幅リサイズ</div>
            <div>• タスク名: クリックで編集</div>
            <div>• 右クリック: 色変更</div>
          </div>
        </div>
      </div>
    </div>
  );
};

const CashFlowWidget = () => {
  const [expenses, setExpenses] = useState([
    { id: 1, item: '新しいマウス', amount: 8000, category: 'tech', date: '2025-06-14', paid: true },
    { id: 2, item: 'コーヒー豆', amount: 2800, category: 'food', date: '2025-06-13', paid: true },
    { id: 3, item: 'デザイン書籍', amount: 3200, category: 'education', date: '2025-06-15', paid: false },
    { id: 4, item: 'サブスク料金', amount: 2500, category: 'subscription', date: '2025-06-12', paid: true },
  ]);
  
  const [newExpense, setNewExpense] = useState({ item: '', amount: '', category: 'other' });
  
  const budget = 50000;
  const totalExpenses = expenses.reduce((sum, exp) => sum + exp.amount, 0);
  const remainingBudget = budget - totalExpenses;
  
  const categoryColors = {
    tech: 'text-info',
    food: 'text-warning', 
    education: 'text-success',
    subscription: 'text-secondary',
    other: 'text-neutral'
  };
  
  const addExpense = () => {
    if (!newExpense.item.trim() || !newExpense.amount) return;
    
    const expense = {
      id: Date.now(),
      item: newExpense.item,
      amount: parseInt(newExpense.amount),
      category: newExpense.category,
      date: new Date().toISOString().split('T')[0],
      paid: false
    };
    
    setExpenses(prev => [...prev, expense]);
    setNewExpense({ item: '', amount: '', category: 'other' });
  };
  
  const togglePaid = (id: number) => {
    setExpenses(prev => prev.map(exp => 
      exp.id === id ? { ...exp, paid: !exp.paid } : exp
    ));
  };
  
  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-2">
        <span className="text-xs text-base-content/60">支出管理</span>
        <span className="text-xs text-base-content/60">予算 ¥{budget.toLocaleString()}</span>
      </div>
      
      {/* 常時表示の支出追加フォーム */}
      <div className="space-y-1 mb-2">
        <div className="flex gap-1">
          <input
            type="text"
            placeholder="項目名..."
            className="input input-xs input-bordered flex-1 bg-base-100 border-base-content/20"
            value={newExpense.item}
            onChange={(e) => setNewExpense(prev => ({ ...prev, item: e.target.value }))}
          />
          <button 
            className="btn btn-xs btn-primary" 
            onClick={addExpense}
            disabled={!newExpense.item.trim() || !newExpense.amount}
          >
            追加
          </button>
        </div>
        <div className="flex gap-1">
          <input
            type="number"
            placeholder="金額"
            className="input input-xs input-bordered flex-1"
            value={newExpense.amount}
            onChange={(e) => setNewExpense(prev => ({ ...prev, amount: e.target.value }))}
          />
          <select 
            className="select select-xs select-bordered"
            value={newExpense.category}
            onChange={(e) => setNewExpense(prev => ({ ...prev, category: e.target.value }))}
          >
            <option value="tech">技術</option>
            <option value="food">食費</option>
            <option value="education">教育</option>
            <option value="subscription">サブスク</option>
            <option value="other">その他</option>
          </select>
        </div>
      </div>
      
      <div className="space-y-1 flex-1 overflow-y-auto">
        {expenses.map((expense) => (
          <div key={expense.id} className="flex items-center justify-between text-xs p-1 rounded hover:bg-base-300/50">
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <input
                type="checkbox"
                className="checkbox checkbox-xs"
                checked={expense.paid}
                onChange={() => togglePaid(expense.id)}
              />
              <span className={`truncate ${expense.paid ? 'line-through text-base-content/60' : ''}`}>
                {expense.item}
              </span>
            </div>
            <div className="flex flex-col items-end">
              <span className={`font-medium ${categoryColors[expense.category]} ${expense.paid ? 'line-through' : ''}`}>
                ¥{expense.amount.toLocaleString()}
              </span>
              <span className="text-xs text-base-content/40">
                {expense.date.slice(5)}
              </span>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-2 pt-2 border-t border-base-content/10 space-y-1">
        <div className="flex justify-between text-xs">
          <span className="text-base-content/60">予算:</span>
          <span>¥{budget.toLocaleString()}</span>
        </div>
        <div className="flex justify-between text-xs">
          <span className="text-base-content/60">支出:</span>
          <span className="text-error">¥{totalExpenses.toLocaleString()}</span>
        </div>
        <div className="flex justify-between text-xs font-medium">
          <span>残額:</span>
          <span className={remainingBudget >= 0 ? 'text-success' : 'text-error'}>
            ¥{remainingBudget.toLocaleString()}
          </span>
        </div>
        <div className="w-full bg-base-300 rounded-full h-1.5">
          <div 
            className={`h-1.5 rounded-full ${remainingBudget >= 0 ? 'bg-success' : 'bg-error'}`}
            style={{width: `${Math.min(100, (totalExpenses / budget) * 100)}%`}}
          />
        </div>
      </div>
    </div>
  );
};

const IdeaWidget = () => {
  const [ideas, setIdeas] = useState([
    { id: 1, text: 'AIペアプログラミング機能', emoji: '💡', category: 'implementation' },
    { id: 2, text: '音声コマンドでのプロジェクト操作', emoji: '🎤', category: 'feature' },
    { id: 3, text: 'VRでのコード空間可視化', emoji: '🔮', category: 'future' },
  ]);
  const [newIdea, setNewIdea] = useState('');
  const [newCategory, setNewCategory] = useState('implementation');

  const categoryColors = {
    implementation: 'bg-warning/20',
    feature: 'bg-info/20', 
    future: 'bg-success/20',
    business: 'bg-primary/20',
  };

  const addIdea = () => {
    if (!newIdea.trim()) return;
    
    const idea = {
      id: Date.now(),
      text: newIdea,
      emoji: '💡',
      category: newCategory
    };
    
    setIdeas(prev => [...prev, idea]);
    setNewIdea('');
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-2">
        <span className="text-xs text-base-content/60">アイデアボックス</span>
        <span className="text-xs text-base-content/60">総{ideas.length}個</span>
      </div>
      
      {/* 常時表示の入力フィールド */}
      <div className="space-y-1 mb-2">
        <div className="flex gap-1">
          <input
            type="text"
            placeholder="新しいアイデア..."
            className="input input-xs input-bordered flex-1 bg-base-100 border-base-content/20"
            value={newIdea}
            onChange={(e) => setNewIdea(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && addIdea()}
          />
          <button 
            className="btn btn-xs btn-primary" 
            onClick={addIdea}
            disabled={!newIdea.trim()}
          >
            追加
          </button>
        </div>
        <select 
          className="select select-xs select-bordered w-full"
          value={newCategory}
          onChange={(e) => setNewCategory(e.target.value)}
        >
          <option value="implementation">実装</option>
          <option value="feature">機能</option>
          <option value="future">将来</option>
          <option value="business">ビジネス</option>
        </select>
      </div>
      
      <div className="flex-1 overflow-y-auto space-y-2">
        {ideas.map((idea) => (
          <div key={idea.id} className={`p-2 ${categoryColors[idea.category]} rounded text-xs`}>
            {idea.emoji} {idea.text}
          </div>
        ))}
      </div>
    </div>
  );
};

const InsightWidget = () => (
  <div className="h-full flex flex-col justify-between">
    <div>
      <div className="text-2xl font-bold text-success mb-1">+18%</div>
      <div className="text-xs text-base-content/70 mb-1">生産性向上</div>
      <div className="text-xs text-base-content/60 mb-3">12日連続記録</div>
    </div>
    <div className="space-y-1">
      <div className="text-xs">
        <div className="text-base-content/60">ベストタイム:</div>
        <div className="text-xs font-medium overflow-hidden text-ellipsis whitespace-nowrap" title="10:00-12:00">
          10:00-12:00
        </div>
      </div>
    </div>
  </div>
);

const HealthWidget = () => {
  const [healthData, setHealthData] = useState({
    sleep: { value: 7.5, target: 8.0, unit: 'h' },
    water: { value: 2.1, target: 2.5, unit: 'L' },
    steps: { value: 8432, target: 10000, unit: '歩' }
  });
  const [editingField, setEditingField] = useState<string | null>(null);
  const [tempValue, setTempValue] = useState('');

  const handleEdit = (field: string, currentValue: number) => {
    setEditingField(field);
    setTempValue(currentValue.toString());
  };

  const saveValue = (field: string) => {
    const numValue = parseFloat(tempValue);
    if (!isNaN(numValue)) {
      setHealthData(prev => ({
        ...prev,
        [field]: { ...prev[field], value: numValue }
      }));
    }
    setEditingField(null);
    setTempValue('');
  };

  const renderField = (key: string, label: string, data: any) => (
    <div>
      <div className="text-xs text-base-content/60">{label}</div>
      {editingField === key ? (
        <div className="flex gap-1 items-center">
          <input
            type="number"
            step="0.1"
            className="input input-xs input-bordered w-16"
            value={tempValue}
            onChange={(e) => setTempValue(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && saveValue(key)}
            onBlur={() => saveValue(key)}
            autoFocus={false}
          />
          <span className="text-xs">{data.unit}</span>
        </div>
      ) : (
        <div 
          className="text-sm font-medium cursor-pointer hover:bg-base-300/50 rounded p-1 -m-1"
          onClick={() => handleEdit(key, data.value)}
          title={`クリックして編集 (目標: ${data.target}${data.unit})`}
        >
          {key === 'water' 
            ? `${data.value}${data.unit} / ${data.target}${data.unit}`
            : `${data.value.toLocaleString()}${data.unit === 'h' ? 'h' : data.unit}`
          }
        </div>
      )}
    </div>
  );

  return (
    <div className="h-full flex flex-col justify-between">
      <div className="space-y-2">
        {renderField('sleep', '睡眠', healthData.sleep)}
        {renderField('water', '水分', healthData.water)}
      </div>
      <div>
        {renderField('steps', '歩数', healthData.steps)}
      </div>
    </div>
  );
};

const HabitWidget = () => (
  <div className="h-full flex flex-col">
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 flex-1">
      <div className="space-y-1">
        <div className="flex justify-between items-center">
          <span className="text-xs font-medium">💻 コーディング</span>
          <span className="text-xs text-base-content/60">45/365</span>
        </div>
        <div className="w-full bg-base-300 rounded-full h-1.5">
          <div className="bg-primary h-1.5 rounded-full" style={{width: '12%'}}></div>
        </div>
        <div className="text-xs text-center text-primary">12%</div>
      </div>
      
      <div className="space-y-1">
        <div className="flex justify-between items-center">
          <span className="text-xs font-medium">📚 読書</span>
          <span className="text-xs text-base-content/60">23/365</span>
        </div>
        <div className="w-full bg-base-300 rounded-full h-1.5">
          <div className="bg-secondary h-1.5 rounded-full" style={{width: '6%'}}></div>
        </div>
        <div className="text-xs text-center text-secondary">6%</div>
      </div>
      
      <div className="space-y-1">
        <div className="flex justify-between items-center">
          <span className="text-xs font-medium">🏃 運動</span>
          <span className="text-xs text-base-content/60">89/365</span>
        </div>
        <div className="w-full bg-base-300 rounded-full h-1.5">
          <div className="bg-accent h-1.5 rounded-full" style={{width: '24%'}}></div>
        </div>
        <div className="text-xs text-center text-accent">24%</div>
      </div>
      
      <div className="space-y-1">
        <div className="flex justify-between items-center">
          <span className="text-xs font-medium">🧘 瞑想</span>
          <span className="text-xs text-base-content/60">156/365</span>
        </div>
        <div className="w-full bg-base-300 rounded-full h-1.5">
          <div className="bg-success h-1.5 rounded-full" style={{width: '43%'}}></div>
        </div>
        <div className="text-xs text-center text-success">43%</div>
      </div>
      
      <div className="space-y-1">
        <div className="flex justify-between items-center">
          <span className="text-xs font-medium">✍️ 日記</span>
          <span className="text-xs text-base-content/60">67/365</span>
        </div>
        <div className="w-full bg-base-300 rounded-full h-1.5">
          <div className="bg-warning h-1.5 rounded-full" style={{width: '18%'}}></div>
        </div>
        <div className="text-xs text-center text-warning">18%</div>
      </div>
      
      <div className="space-y-1">
        <div className="flex justify-between items-center">
          <span className="text-xs font-medium">🎯 目標</span>
          <span className="text-xs text-base-content/60">234/365</span>
        </div>
        <div className="w-full bg-base-300 rounded-full h-1.5">
          <div className="bg-info h-1.5 rounded-full" style={{width: '64%'}}></div>
        </div>
        <div className="text-xs text-center text-info">64%</div>
      </div>
    </div>
  </div>
);

const LearningWidget = () => (
  <div className="h-full flex flex-col">
    <div className="mb-2">
      <div className="text-sm font-medium truncate">TypeScript Advanced</div>
      <div className="text-xs text-base-content/60">78% 完了</div>
    </div>
    <div className="w-full bg-base-300 rounded-full h-2 mb-2">
      <div className="bg-info h-2 rounded-full" style={{width: '78%'}}></div>
    </div>
    <div className="text-xs text-base-content/60 truncate">
      完了: React Hooks, Next.js 15
    </div>
  </div>
);

const IncomeWidget = () => {
  const [incomes, setIncomes] = useState([
    { id: 1, source: 'フリーランス開発', amount: 320000, date: '2025-06-01', type: 'freelance', recurring: false },
    { id: 2, source: 'アプリ収益', amount: 48000, date: '2025-06-01', type: 'passive', recurring: true },
    { id: 3, source: 'コンサル料', amount: 85000, date: '2025-06-10', type: 'consulting', recurring: false },
    { id: 4, source: 'ブログ広告収入', amount: 12500, date: '2025-06-01', type: 'passive', recurring: true },
  ]);
  
  const [newIncome, setNewIncome] = useState({ source: '', amount: '', type: 'freelance', recurring: false });
  
  const totalIncome = incomes.reduce((sum, inc) => sum + inc.amount, 0);
  const recurringIncome = incomes.filter(inc => inc.recurring).reduce((sum, inc) => sum + inc.amount, 0);
  
  const typeColors = {
    freelance: 'text-primary',
    passive: 'text-success',
    consulting: 'text-info',
    salary: 'text-secondary'
  };
  
  const addIncome = () => {
    if (!newIncome.source.trim() || !newIncome.amount) return;
    
    const income = {
      id: Date.now(),
      source: newIncome.source,
      amount: parseInt(newIncome.amount),
      date: new Date().toISOString().split('T')[0],
      type: newIncome.type,
      recurring: newIncome.recurring
    };
    
    setIncomes(prev => [...prev, income]);
    setNewIncome({ source: '', amount: '', type: 'freelance', recurring: false });
  };
  
  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-2">
        <span className="text-xs text-base-content/60">収入管理</span>
        <span className="text-xs text-success font-medium">¥{totalIncome.toLocaleString()}</span>
      </div>
      
      {/* 常時表示の収入追加フォーム */}
      <div className="space-y-1 mb-2">
        <div className="flex gap-1">
          <input
            type="text"
            placeholder="収入源..."
            className="input input-xs input-bordered flex-1 bg-base-100 border-base-content/20"
            value={newIncome.source}
            onChange={(e) => setNewIncome(prev => ({ ...prev, source: e.target.value }))}
          />
          <button 
            className="btn btn-xs btn-success" 
            onClick={addIncome}
            disabled={!newIncome.source.trim() || !newIncome.amount}
          >
            追加
          </button>
        </div>
        <div className="flex gap-1">
          <input
            type="number"
            placeholder="金額"
            className="input input-xs input-bordered flex-1"
            value={newIncome.amount}
            onChange={(e) => setNewIncome(prev => ({ ...prev, amount: e.target.value }))}
          />
          <select 
            className="select select-xs select-bordered"
            value={newIncome.type}
            onChange={(e) => setNewIncome(prev => ({ ...prev, type: e.target.value }))}
          >
            <option value="freelance">フリーランス</option>
            <option value="passive">不労所得</option>
            <option value="consulting">コンサル</option>
            <option value="salary">給与</option>
          </select>
        </div>
        <label className="flex items-center gap-2 text-xs">
          <input
            type="checkbox"
            className="checkbox checkbox-xs"
            checked={newIncome.recurring}
            onChange={(e) => setNewIncome(prev => ({ ...prev, recurring: e.target.checked }))}
          />
          定期収入
        </label>
      </div>
      
      <div className="space-y-1 flex-1 overflow-y-auto">
        {incomes.map((income) => (
          <div key={income.id} className="flex items-center justify-between text-xs p-1 rounded hover:bg-base-300/50">
            <div className="flex items-center gap-2 flex-1 min-w-0">
              {income.recurring && <span className="text-success">🔄</span>}
              <span className="truncate">{income.source}</span>
            </div>
            <div className="flex flex-col items-end">
              <span className={`font-medium ${typeColors[income.type]}`}>
                ¥{income.amount.toLocaleString()}
              </span>
              <span className="text-xs text-base-content/40">
                {income.date.slice(5)}
              </span>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-2 pt-2 border-t border-base-content/10 space-y-1">
        <div className="flex justify-between text-xs">
          <span className="text-base-content/60">総収入:</span>
          <span className="text-success font-medium">¥{totalIncome.toLocaleString()}</span>
        </div>
        <div className="flex justify-between text-xs">
          <span className="text-base-content/60">定期収入:</span>
          <span className="text-info">¥{recurringIncome.toLocaleString()}</span>
        </div>
      </div>
    </div>
  );
};

const FinancialSummaryWidget = () => {
  // 仮のデータ - 実際の実装では他のウィジェットからデータを取得
  const monthlyIncome = 465500;
  const monthlyExpenses = 18500;
  const netCashFlow = monthlyIncome - monthlyExpenses;
  const savingsRate = ((netCashFlow / monthlyIncome) * 100).toFixed(1);
  
  const [viewMode, setViewMode] = useState<'monthly' | 'yearly'>('monthly');
  
  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-2">
        <span className="text-xs text-base-content/60">財務サマリー</span>
        <div className="flex bg-base-300 rounded p-0.5">
          <button 
            className={`btn btn-xs ${viewMode === 'monthly' ? 'btn-primary' : 'btn-ghost'}`}
            onClick={() => setViewMode('monthly')}
          >
            月
          </button>
          <button 
            className={`btn btn-xs ${viewMode === 'yearly' ? 'btn-primary' : 'btn-ghost'}`}
            onClick={() => setViewMode('yearly')}
          >
            年
          </button>
        </div>
      </div>
      
      <div className="space-y-3 flex-1">
        <div className="text-center">
          <div className="text-2xl font-bold text-success mb-1">
            ¥{netCashFlow.toLocaleString()}
          </div>
          <div className="text-xs text-base-content/60">
            {viewMode === 'monthly' ? '月間' : '年間'}キャッシュフロー
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-xs text-success">📈 収入</span>
            <span className="text-xs font-medium">¥{monthlyIncome.toLocaleString()}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs text-error">📉 支出</span>
            <span className="text-xs font-medium">¥{monthlyExpenses.toLocaleString()}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs text-info">💰 貯蓄率</span>
            <span className="text-xs font-medium">{savingsRate}%</span>
          </div>
        </div>
        
        <div className="w-full bg-base-300 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-success to-info h-2 rounded-full"
            style={{width: `${savingsRate}%`}}
          />
        </div>
      </div>
      
      <div className="mt-2 pt-2 border-t border-base-content/10">
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="text-center">
            <div className="text-warning font-bold">98.2%</div>
            <div className="text-base-content/60">達成率</div>
          </div>
          <div className="text-center">
            <div className="text-primary font-bold">+15%</div>
            <div className="text-base-content/60">前月比</div>
          </div>
        </div>
      </div>
    </div>
  );
};

const ProjectWidget = () => {
  const [projects, setProjects] = useState([
    { id: 1, name: '瞑想アプリ_projext', type: 'Mobile React Native', progress: 75, status: 'active', priority: 'high' },
    { id: 2, name: '投資bot_projext', type: 'AI Trading Bot', progress: 90, status: 'active', priority: 'high' },
    { id: 3, name: 'iS_streamer_projext', type: 'Web Streaming', progress: 25, status: 'planning', priority: 'medium' },
  ]);
  const [selectedProject, setSelectedProject] = useState(0);
  const [isEditingName, setIsEditingName] = useState(false);
  const [tempName, setTempName] = useState('');
  const [newProject, setNewProject] = useState({ name: '', type: '', priority: 'medium' });
  
  const currentProject = projects[selectedProject] || projects[0];
  
  const handleNameEdit = () => {
    setIsEditingName(true);
    setTempName(currentProject.name);
  };
  
  const saveName = () => {
    if (tempName.trim()) {
      setProjects(prev => prev.map((p, idx) => 
        idx === selectedProject ? { ...p, name: tempName.trim() } : p
      ));
    }
    setIsEditingName(false);
  };
  
  const addProject = () => {
    if (!newProject.name.trim() || !newProject.type.trim()) return;
    
    const project = {
      id: Date.now(),
      name: newProject.name.trim() + '_projext',
      type: newProject.type.trim(),
      progress: 0,
      status: 'planning' as const,
      priority: newProject.priority as 'high' | 'medium' | 'low'
    };
    
    setProjects(prev => [...prev, project]);
    setSelectedProject(projects.length);
    setNewProject({ name: '', type: '', priority: 'medium' });
  };
  
  const deleteProject = (index: number) => {
    if (projects.length <= 1) return; // 最低1つは残す
    setProjects(prev => prev.filter((_, idx) => idx !== index));
    if (selectedProject >= projects.length - 1) {
      setSelectedProject(Math.max(0, selectedProject - 1));
    }
  };
  
  const statusColors = {
    active: 'badge-success',
    planning: 'badge-info',
    paused: 'badge-warning',
    completed: 'badge-primary'
  };
  
  const priorityColors = {
    high: 'text-error',
    medium: 'text-warning',
    low: 'text-info'
  };
  
  return (
    <div className="h-full flex flex-col">
      {/* プロジェクトセレクター */}
      <div className="flex items-center gap-2 mb-2">
        <div className="flex-1 overflow-x-auto">
          <div className="flex gap-1">
            {projects.map((project, idx) => (
              <button
                key={project.id}
                className={`btn btn-xs ${selectedProject === idx ? 'btn-primary' : 'btn-ghost'} whitespace-nowrap`}
                onClick={() => setSelectedProject(idx)}
              >
                {project.name.replace('_projext', '')}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      {/* 常時表示の新規プロジェクト追加フォーム */}
      <div className="space-y-1 mb-3">
        <div className="flex gap-1">
          <input
            type="text"
            placeholder="プロジェクト名..."
            className="input input-xs input-bordered flex-1 bg-base-100 border-base-content/20"
            value={newProject.name}
            onChange={(e) => setNewProject(prev => ({ ...prev, name: e.target.value }))}
          />
          <button 
            className="btn btn-xs btn-primary" 
            onClick={addProject}
            disabled={!newProject.name.trim() || !newProject.type.trim()}
          >
            作成
          </button>
        </div>
        <div className="flex gap-1">
          <input
            type="text"
            placeholder="タイプ (Web App, Mobile App等)..."
            className="input input-xs input-bordered flex-1"
            value={newProject.type}
            onChange={(e) => setNewProject(prev => ({ ...prev, type: e.target.value }))}
          />
          <select
            className="select select-xs select-bordered"
            value={newProject.priority}
            onChange={(e) => setNewProject(prev => ({ ...prev, priority: e.target.value }))}
          >
            <option value="high">高</option>
            <option value="medium">中</option>
            <option value="low">低</option>
          </select>
        </div>
      </div>
      
      {/* プロジェクト詳細 */}
      {currentProject && (
        <div className="flex-1 flex flex-col">
          <div>
            {isEditingName ? (
              <div className="flex gap-1 mb-1">
                <input
                  type="text"
                  className="input input-sm input-bordered flex-1"
                  value={tempName}
                  onChange={(e) => setTempName(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && saveName()}
                  onBlur={saveName}
                  autoFocus
                />
              </div>
            ) : (
              <h2 
                className="text-lg font-bold mb-1 truncate cursor-pointer hover:text-primary transition-colors"
                onClick={handleNameEdit}
                title="クリックして編集"
              >
                {currentProject.name}
              </h2>
            )}
            <div className="flex items-center gap-2 mb-3">
              <span className="text-xs text-base-content/70">{currentProject.type}</span>
              <span className={`badge badge-xs ${statusColors[currentProject.status]}`}>
                {currentProject.status}
              </span>
              <span className={`text-xs ${priorityColors[currentProject.priority]}`}>
                ● {currentProject.priority}
              </span>
            </div>
          </div>
          
          <div className="flex-1">
            <div className="flex justify-between text-xs mb-1">
              <span className="text-base-content/60">進捗</span>
              <span className="font-medium">{currentProject.progress}%</span>
            </div>
            <div className="w-full bg-base-300 rounded-full h-2 mb-2 cursor-pointer group"
                 onClick={(e) => {
                   const rect = e.currentTarget.getBoundingClientRect();
                   const x = e.clientX - rect.left;
                   const newProgress = Math.round((x / rect.width) * 100);
                   setProjects(prev => prev.map((p, idx) => 
                     idx === selectedProject ? { ...p, progress: newProgress } : p
                   ));
                 }}
                 title="クリックで進捗を更新">
              <div 
                className="bg-primary h-2 rounded-full transition-all group-hover:bg-primary-focus" 
                style={{width: `${currentProject.progress}%`}}
              />
            </div>
            
            {/* プロジェクトアクション */}
            <div className="flex gap-2 mt-3">
              <button className="btn btn-xs btn-outline">開く</button>
              <button className="btn btn-xs btn-outline">ビルド</button>
              <button 
                className="btn btn-xs btn-outline btn-error"
                onClick={() => deleteProject(selectedProject)}
              >
                削除
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const StatsWidget = () => {
  const [stats, setStats] = useState([
    { id: 1, label: 'プロジェクト', value: 3, icon: '📁', color: 'text-info', trend: 0 },
    { id: 2, label: 'タスク完了', value: 42, icon: '✅', color: 'text-success', trend: +5 },
    { id: 3, label: 'PR済み', value: 18, icon: '🔀', color: 'text-warning', trend: +2 },
    { id: 4, label: 'デプロイ', value: 7, icon: '🚀', color: 'text-primary', trend: +1 },
  ]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedStat, setSelectedStat] = useState<number | null>(null);
  const [tempValue, setTempValue] = useState('');
  
  const handleStatClick = (index: number) => {
    if (isEditMode) {
      setSelectedStat(index);
      setTempValue(stats[index].value.toString());
    }
  };
  
  const updateStatValue = () => {
    if (selectedStat !== null && tempValue) {
      const newValue = parseInt(tempValue);
      if (!isNaN(newValue)) {
        setStats(prev => prev.map((stat, idx) => {
          if (idx === selectedStat) {
            const oldValue = stat.value;
            return { ...stat, value: newValue, trend: newValue - oldValue };
          }
          return stat;
        }));
      }
    }
    setSelectedStat(null);
    setTempValue('');
  };
  
  const incrementStat = (index: number, delta: number) => {
    setStats(prev => prev.map((stat, idx) => {
      if (idx === index) {
        return { ...stat, value: Math.max(0, stat.value + delta), trend: delta };
      }
      return stat;
    }));
  };
  
  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-2">
        <span className="text-xs text-base-content/60">統計ダッシュボード</span>
        <button
          className={`btn btn-xs ${isEditMode ? 'btn-primary' : 'btn-ghost'}`}
          onClick={() => setIsEditMode(!isEditMode)}
        >
          {isEditMode ? '完了' : '編集'}
        </button>
      </div>
      
      <div className="grid grid-cols-2 gap-2 flex-1">
        {stats.map((stat, index) => (
          <div
            key={stat.id}
            className={`relative flex flex-col justify-center items-center p-3 rounded-lg transition-all cursor-pointer
              ${isEditMode ? 'bg-base-200 hover:bg-base-300' : 'hover:bg-base-200/50'}
              ${selectedStat === index ? 'ring-2 ring-primary' : ''}`}
            onClick={() => handleStatClick(index)}
          >
            <div className="text-2xl mb-1">{stat.icon}</div>
            
            {selectedStat === index ? (
              <input
                type="number"
                className="input input-xs input-bordered w-16 text-center"
                value={tempValue}
                onChange={(e) => setTempValue(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && updateStatValue()}
                onBlur={updateStatValue}
                autoFocus
                onClick={(e) => e.stopPropagation()}
              />
            ) : (
              <div className={`text-lg font-bold ${stat.color}`}>
                {stat.value}
              </div>
            )}
            
            <div className="text-xs text-base-content/60 mt-1">{stat.label}</div>
            
            {stat.trend !== 0 && (
              <div className={`absolute top-1 right-1 text-xs font-medium 
                ${stat.trend > 0 ? 'text-success' : 'text-error'}`}
              >
                {stat.trend > 0 ? '+' : ''}{stat.trend}
              </div>
            )}
            
            {isEditMode && selectedStat !== index && (
              <div className="absolute -top-1 -right-1 flex flex-col gap-0.5">
                <button
                  className="btn btn-xs btn-circle btn-ghost text-xs w-5 h-5"
                  onClick={(e) => {
                    e.stopPropagation();
                    incrementStat(index, 1);
                  }}
                >
                  ＋
                </button>
                <button
                  className="btn btn-xs btn-circle btn-ghost text-xs w-5 h-5"
                  onClick={(e) => {
                    e.stopPropagation();
                    incrementStat(index, -1);
                  }}
                >
                  －
                </button>
              </div>
            )}
          </div>
        ))}
      </div>
      
      <div className="mt-2 pt-2 border-t border-base-content/10">
        <div className="text-xs text-base-content/60 text-center">
          週間パフォーマンス: 
          <span className="text-success font-medium ml-1">+15% 向上</span>
        </div>
      </div>
    </div>
  );
};

const QuickActionWidget = () => {
  const [actions, setActions] = useState([
    { id: 1, label: 'デプロイ', icon: '🚀', color: 'btn-primary', command: 'px deploy', count: 0 },
    { id: 2, label: 'ノート', icon: '📝', color: 'btn-secondary', command: 'px note', count: 0 },
    { id: 3, label: '同期', icon: '🔄', color: 'btn-accent', command: 'px sync', count: 0 },
    { id: 4, label: '分析', icon: '📊', color: 'btn-warning', command: 'px analyze', count: 0 },
  ]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [newAction, setNewAction] = useState({ label: '', icon: '', command: '' });
  
  const handleDragStart = (index: number) => {
    setDraggedIndex(index);
  };
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };
  
  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (draggedIndex === null || draggedIndex === dropIndex) return;
    
    const newActions = [...actions];
    const draggedAction = newActions[draggedIndex];
    newActions.splice(draggedIndex, 1);
    newActions.splice(dropIndex, 0, draggedAction);
    
    setActions(newActions);
    setDraggedIndex(null);
  };
  
  const executeAction = (index: number) => {
    setActions(prev => prev.map((action, idx) => 
      idx === index ? { ...action, count: action.count + 1 } : action
    ));
    // ここで実際のコマンド実行処理を追加
    console.log('Executing:', actions[index].command);
  };
  
  const deleteAction = (index: number) => {
    setActions(prev => prev.filter((_, idx) => idx !== index));
  };
  
  const addAction = () => {
    if (!newAction.label.trim() || !newAction.icon.trim() || !newAction.command.trim()) return;
    
    const action = {
      id: Date.now(),
      label: newAction.label.trim(),
      icon: newAction.icon.trim(),
      color: 'btn-neutral',
      command: newAction.command.trim(),
      count: 0
    };
    
    setActions(prev => [...prev, action]);
    setNewAction({ label: '', icon: '', command: '' });
  };
  
  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-2">
        <span className="text-xs text-base-content/60">クイックアクション</span>
        <button
          className={`btn btn-xs ${isEditMode ? 'btn-primary' : 'btn-ghost'}`}
          onClick={() => setIsEditMode(!isEditMode)}
        >
          {isEditMode ? '完了' : '編集'}
        </button>
      </div>
      
      {/* 常時表示の新規アクション追加フォーム */}
      <div className="space-y-1 mb-2">
        <div className="flex gap-1">
          <input
            type="text"
            placeholder="🚀"
            className="input input-xs input-bordered w-10 text-center bg-base-100 border-base-content/20"
            value={newAction.icon}
            onChange={(e) => setNewAction(prev => ({ ...prev, icon: e.target.value }))}
            maxLength={2}
          />
          <input
            type="text"
            placeholder="ラベル"
            className="input input-xs input-bordered flex-1"
            value={newAction.label}
            onChange={(e) => setNewAction(prev => ({ ...prev, label: e.target.value }))}
          />
          <button 
            className="btn btn-xs btn-primary" 
            onClick={addAction}
            disabled={!newAction.label.trim() || !newAction.icon.trim() || !newAction.command.trim()}
          >
            追加
          </button>
        </div>
        <input
          type="text"
          placeholder="コマンド (例: px deploy)"
          className="input input-xs input-bordered w-full"
          value={newAction.command}
          onChange={(e) => setNewAction(prev => ({ ...prev, command: e.target.value }))}
        />
      </div>
      
      <div className="grid grid-cols-2 gap-2 flex-1">
        {actions.map((action, index) => (
          <div
            key={action.id}
            draggable={isEditMode}
            onDragStart={() => handleDragStart(index)}
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, index)}
            className={`relative ${isEditMode ? 'cursor-move' : ''}`}
          >
            <button 
              className={`btn btn-sm ${action.color} neo-hover w-full relative
                ${isEditMode ? 'btn-outline' : ''}`}
              onClick={() => !isEditMode && executeAction(index)}
              disabled={isEditMode}
            >
              <span className="flex items-center gap-1">
                <span>{action.icon}</span>
                <span>{action.label}</span>
              </span>
              
              {action.count > 0 && (
                <span className="absolute -top-1 -right-1 badge badge-xs badge-info">
                  {action.count}
                </span>
              )}
            </button>
            
            {isEditMode && (
              <button
                className="absolute -top-2 -right-2 btn btn-xs btn-circle btn-error"
                onClick={() => deleteAction(index)}
              >
                ×
              </button>
            )}
          </div>
        ))}
      </div>
      
      <div className="mt-2 text-xs text-base-content/60 text-center">
        {isEditMode ? 'ドラッグで並び替え' : 'クリックで実行'}
      </div>
    </div>
  );
};

const InvestmentWidget = () => {
  const [investments, setInvestments] = useState([
    { id: 1, name: '米国株 ETF', amount: 450000, currentValue: 478500, change: +6.3, type: 'stock' },
    { id: 2, name: '暗号資産', amount: 120000, currentValue: 134800, change: +12.3, type: 'crypto' },
    { id: 3, name: '積立NISA', amount: 380000, currentValue: 396200, change: +4.3, type: 'mutual' },
    { id: 4, name: '定期預金', amount: 800000, currentValue: 802100, change: +0.3, type: 'deposit' },
  ]);
  
  const totalInvestment = investments.reduce((sum, inv) => sum + inv.amount, 0);
  const totalCurrentValue = investments.reduce((sum, inv) => sum + inv.currentValue, 0);
  const totalGain = totalCurrentValue - totalInvestment;
  const totalGainPercent = ((totalGain / totalInvestment) * 100).toFixed(1);
  
  const typeIcons = {
    stock: '📈',
    crypto: '₿',
    mutual: '📊',
    deposit: '🏦'
  };
  
  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-2">
        <span className="text-xs text-base-content/60">投資ポートフォリオ</span>
        <span className={`text-xs font-medium ${totalGain >= 0 ? 'text-success' : 'text-error'}`}>
          {totalGain >= 0 ? '+' : ''}{totalGainPercent}%
        </span>
      </div>
      
      <div className="space-y-1 flex-1 overflow-y-auto">
        {investments.map((investment) => (
          <div key={investment.id} className="flex items-center justify-between text-xs p-1 rounded hover:bg-base-300/50">
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <span>{typeIcons[investment.type]}</span>
              <span className="truncate">{investment.name}</span>
            </div>
            <div className="flex flex-col items-end">
              <span className="font-medium">
                ¥{investment.currentValue.toLocaleString()}
              </span>
              <span className={`text-xs ${investment.change >= 0 ? 'text-success' : 'text-error'}`}>
                {investment.change >= 0 ? '+' : ''}{investment.change}%
              </span>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-2 pt-2 border-t border-base-content/10 space-y-1">
        <div className="flex justify-between text-xs">
          <span className="text-base-content/60">投資元本:</span>
          <span>¥{totalInvestment.toLocaleString()}</span>
        </div>
        <div className="flex justify-between text-xs">
          <span className="text-base-content/60">評価額:</span>
          <span className="font-medium">¥{totalCurrentValue.toLocaleString()}</span>
        </div>
        <div className="flex justify-between text-xs font-medium">
          <span>評価損益:</span>
          <span className={totalGain >= 0 ? 'text-success' : 'text-error'}>
            {totalGain >= 0 ? '+' : ''}¥{totalGain.toLocaleString()}
          </span>
        </div>
      </div>
    </div>
  );
};

export default function DashboardGrid() {
  const [layouts, setLayouts] = useState({
    lg: [
      // 左列：主要作業エリア
      { i: 'tasks', x: 0, y: 0, w: 3, h: 4 },
      { i: 'ideas', x: 3, y: 0, w: 3, h: 4 },
      { i: 'vision', x: 6, y: 0, w: 3, h: 4 },
      
      // 右列：タイムブロック（右端固定）
      { i: 'timeblock', x: 9, y: 0, w: 3, h: 12 },
      
      // 中段：学習・生産性
      { i: 'learning', x: 0, y: 4, w: 3, h: 4 },
      { i: 'insights', x: 3, y: 4, w: 3, h: 4 },
      { i: 'health', x: 6, y: 4, w: 3, h: 4 },
      
      // 下段：財務・管理
      { i: 'financial-summary', x: 0, y: 8, w: 4, h: 4 },
      { i: 'cashflow', x: 4, y: 8, w: 2, h: 4 },
      { i: 'income', x: 6, y: 8, w: 3, h: 4 },
      
      // 底部：習慣・投資
      { i: 'habits', x: 0, y: 12, w: 6, h: 2 },
      { i: 'investment', x: 6, y: 12, w: 3, h: 2 },
    ]
  });

  const widgets = {
    vision: { component: <VisionWidget />, title: 'ビジョンボード' },
    tasks: { component: <TaskWidget />, title: 'タスクリスト' },
    cashflow: { component: <CashFlowWidget />, title: '支出管理' },
    timeblock: { component: <TimeBlockWidget />, title: 'タイムブロック' },
    ideas: { component: <IdeaWidget />, title: 'アイデアボックス' },
    insights: { component: <InsightWidget />, title: 'インサイト' },
    health: { component: <HealthWidget />, title: '健康管理' },
    habits: { component: <HabitWidget />, title: '習慣トラッカー' },
    learning: { component: <LearningWidget />, title: '学習ログ' },
    income: { component: <IncomeWidget />, title: '収入管理' },
    'financial-summary': { component: <FinancialSummaryWidget />, title: '財務サマリー' },
    investment: { component: <InvestmentWidget />, title: '投資管理' },
  };

  return (
    <div className="h-full p-4 overflow-hidden">
      <ResponsiveGridLayout
        className="layout"
        layouts={layouts}
        onLayoutChange={(layout, layouts) => setLayouts(layouts)}
        breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
        cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
        rowHeight={60}
        isDraggable={true}
        isResizable={true}
        dragHandleClassName="react-grid-dragHandleClassName"
        draggableCancel=".no-drag-content"
      >
        {Object.entries(widgets).map(([key, widget]) => (
          <div key={key}>
            <WidgetWrapper title={widget.title}>
              {widget.component}
            </WidgetWrapper>
          </div>
        ))}
      </ResponsiveGridLayout>
    </div>
  );
}