'use client';

import { useState } from 'react';
import { FileText, Play, Settings, GitBranch, <PERSON>, <PERSON><PERSON><PERSON>, Edit3, Eye, History } from 'lucide-react';
import Editor from './Editor';
import YamlEditor from './YamlEditor';
import VersionHistory from './VersionHistory';

interface ProjectFile {
  name: string;
  type: 'md' | 'yaml' | 'json' | 'txt';
  description: string;
  lastModified: string;
  content?: string;
}

interface ProjectViewProps {
  projectName: string;
  onFileClick?: (file: ProjectFile) => void;
  onProjectNameChange?: (newName: string) => void;
}

export default function ProjectView({ projectName, onFileClick, onProjectNameChange }: ProjectViewProps) {
  console.log('ProjectView rendered with projectName:', projectName);
  const [selectedFile, setSelectedFile] = useState<ProjectFile | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentView, setCurrentView] = useState<'files' | 'yaml' | 'versions'>('files');
  const [isEditingProjectName, setIsEditingProjectName] = useState(false);
  const [editedProjectName, setEditedProjectName] = useState(projectName);
  
  const getProjectDetails = (name: string) => {
    switch (name) {
      case '瞑想アプリ':
        return {
          description: '心を落ち着かせる瞑想タイマーアプリ',
          status: '開発中',
          progress: 65,
          color: 'primary'
        };
      case '投資bot':
        return {
          description: 'AI駆動の自動投資ボット',
          status: 'テスト中',
          progress: 80,
          color: 'secondary'
        };
      case 'iS_streamer':
        return {
          description: 'インタラクティブストリーミングプラットフォーム',
          status: '企画中',
          progress: 25,
          color: 'accent'
        };
      default:
        return {
          description: 'プロジェクトの説明',
          status: '未定義',
          progress: 0,
          color: 'neutral'
        };
    }
  };

  const project = getProjectDetails(projectName);

  const getProjectFiles = (name: string): ProjectFile[] => {
    const baseTemplates = [
      { name: '要件定義.md', type: 'md' as const, description: 'プロジェクト基本要件・目標設定', lastModified: '2時間前' },
      { name: 'アーキテクチャ設計.md', type: 'md' as const, description: 'システム構成・技術スタック', lastModified: '1日前' },
      { name: 'project-config.yaml', type: 'yaml' as const, description: 'プロジェクト設定・環境変数', lastModified: '1日前' },
      { name: 'ワークフロー図.mermaid', type: 'txt' as const, description: 'Mermaidフローチャート・ER図', lastModified: '2日前' },
      { name: 'テスト計画.md', type: 'md' as const, description: 'テスト戦略・品質保証', lastModified: '3日前' },
      { name: 'API仕様.yaml', type: 'yaml' as const, description: 'OpenAPI/Swagger仕様書', lastModified: '3日前' },
      { name: 'デプロイ手順.md', type: 'md' as const, description: '本番環境デプロイ・CI/CD', lastModified: '4日前' },
      { name: 'チーム体制.md', type: 'md' as const, description: 'ロール分担・コミュニケーション', lastModified: '5日前' },
      { name: 'プロジェクト進捗.yaml', type: 'yaml' as const, description: 'マイルストーン・タスク管理', lastModified: '1週間前' },
      { name: 'README.md', type: 'md' as const, description: 'プロジェクト概要・セットアップ', lastModified: '1週間前' },
    ];

    const specificTemplates = {
      '瞑想アプリ': [
        { name: 'UX設計.md', type: 'md' as const, description: 'ユーザージャーニー・画面設計', lastModified: '1時間前' },
        { name: 'コンテンツ戦略.md', type: 'md' as const, description: '瞑想音源・ガイダンス企画', lastModified: '2時間前' },
        { name: 'app-config.yaml', type: 'yaml' as const, description: 'モバイルアプリ設定', lastModified: '1日前' },
        { name: 'データベース設計.mermaid', type: 'txt' as const, description: 'ユーザー・セッションER図', lastModified: '2日前' },
        { name: 'マネタイズ戦略.md', type: 'md' as const, description: 'サブスク・課金モデル', lastModified: '3日前' },
      ],
      '投資bot': [
        { name: '取引戦略.md', type: 'md' as const, description: 'アルゴリズム・リスク管理', lastModified: '30分前' },
        { name: 'リスク管理.yaml', type: 'yaml' as const, description: 'ポジション・損切り設定', lastModified: '1時間前' },
        { name: 'バックテスト結果.json', type: 'json' as const, description: '過去データ検証・パフォーマンス', lastModified: '2時間前' },
        { name: 'セキュリティ要件.md', type: 'md' as const, description: 'API キー・暗号化・監査', lastModified: '1日前' },
        { name: 'trading-flow.mermaid', type: 'txt' as const, description: '取引フロー・判断ロジック図', lastModified: '2日前' },
      ],
      'iS_streamer': [
        { name: 'ストリーミング仕様.md', type: 'md' as const, description: 'ライブ配信・インタラクション機能', lastModified: '1時間前' },
        { name: 'コンテンツ戦略.md', type: 'md' as const, description: 'プログラム企画・視聴者エンゲージメント', lastModified: '2時間前' },
        { name: 'streaming-config.yaml', type: 'yaml' as const, description: '配信品質・サーバー設定', lastModified: '1日前' },
        { name: 'ユーザー権限.yaml', type: 'yaml' as const, description: '配信者・視聴者・モデレーター権限', lastModified: '2日前' },
        { name: 'platform-architecture.mermaid', type: 'txt' as const, description: 'システム構成・CDN・負荷分散図', lastModified: '3日前' },
      ]
    };

    const projectSpecific = specificTemplates[name as keyof typeof specificTemplates] || [];
    return [...baseTemplates, ...projectSpecific];
  };

  const handleFileClick = (file: ProjectFile) => {
    setSelectedFile(file);
    onFileClick?.(file);
  };

  const handleProjectNameSave = () => {
    if (editedProjectName.trim() && editedProjectName !== projectName) {
      // プロジェクト名変更を通知
      onProjectNameChange?.(editedProjectName);
      
      // トースト通知（Next.jsロゴの位置に表示）
      const notification = document.createElement('div');
      notification.className = 'fixed bottom-4 left-4 z-50 bg-success text-success-content px-4 py-2 rounded-lg shadow-lg flex items-center gap-2';
      notification.innerHTML = `
        <svg viewBox="0 0 180 180" fill="currentColor" class="w-5 h-5">
          <path d="M0 0h180v180H0z"/>
          <path fill="white" d="M37.44 37.44h88.32v71.28H60.48V60.48h42.24v71.28H37.44z"/>
        </svg>
        プロジェクト名を「${editedProjectName}」に変更しました
      `;
      document.body.appendChild(notification);
      setTimeout(() => notification.remove(), 3000);
    }
    setIsEditingProjectName(false);
  };

  const handleProjectNameKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleProjectNameSave();
    } else if (e.key === 'Escape') {
      setEditedProjectName(projectName);
      setIsEditingProjectName(false);
    }
  };

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* プロジェクトヘッダー */}
      <div className="p-4 border-b border-base-content/10 bg-base-200/40">
        <div className="flex items-center gap-4 mb-3">
          {isEditingProjectName ? (
            <div className="flex items-center gap-2">
              <input
                type="text"
                value={editedProjectName}
                onChange={(e) => setEditedProjectName(e.target.value)}
                onKeyDown={handleProjectNameKeyDown}
                onBlur={handleProjectNameSave}
                className="input input-bordered input-sm text-2xl font-bold bg-base-100 min-w-0 flex-1"
                autoFocus
                style={{ fontSize: '1.5rem', height: '2.5rem' }}
              />
            </div>
          ) : (
            <h1 
              className="text-2xl font-bold cursor-pointer hover:bg-base-200/50 rounded px-2 py-1 transition-colors"
              onDoubleClick={() => setIsEditingProjectName(true)}
              title="ダブルクリックで編集"
            >
              {editedProjectName}
            </h1>
          )}
          <div className={`badge badge-${project.color} badge-sm`}>
            {project.status}
          </div>
          <div className="flex items-center gap-2">
            <progress 
              className={`progress progress-${project.color} w-20`} 
              value={project.progress} 
              max="100"
            />
            <span className="text-sm font-medium">{project.progress}%</span>
          </div>
        </div>
        <p className="text-sm text-base-content/70 mb-3">{project.description}</p>
        
        {/* タブナビゲーション */}
        <div className="flex gap-1">
          <button
            onClick={() => setCurrentView('files')}
            className={`btn btn-sm ${currentView === 'files' ? 'btn-primary' : 'btn-ghost'}`}
          >
            <FileText size={14} />
            ファイル
          </button>
          <button
            onClick={() => setCurrentView('yaml')}
            className={`btn btn-sm ${currentView === 'yaml' ? 'btn-primary' : 'btn-ghost'}`}
          >
            <Settings size={14} />
            YAML設定
          </button>
          <button
            onClick={() => setCurrentView('versions')}
            className={`btn btn-sm ${currentView === 'versions' ? 'btn-primary' : 'btn-ghost'}`}
          >
            <History size={14} />
            バージョン履歴
          </button>
        </div>
      </div>

      {/* メインコンテンツエリア */}
      <div className="flex-1 overflow-hidden">
        {currentView === 'files' ? (
          <div className="flex h-full overflow-hidden">
            {/* 左: ファイル一覧 */}
            <div className="w-80 border-r border-base-content/10 bg-base-100 overflow-y-auto">
              <div className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold">プロジェクトファイル</h2>
                  <div className="flex gap-2">
                    <button className="btn btn-xs btn-outline gap-1">
                      <Play size={12} />
                    </button>
                    <button className="btn btn-xs btn-outline gap-1">
                      <Settings size={12} />
                    </button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  {getProjectFiles(projectName).map((file) => (
                    <div
                      key={file.name}
                      className={`p-3 rounded cursor-pointer transition-all hover:bg-base-200 ${
                        selectedFile?.name === file.name ? 'bg-primary/10 border border-primary/20' : 'bg-base-50'
                      }`}
                      onClick={() => handleFileClick(file)}
                    >
                      <div className="flex items-center gap-2 mb-1">
                        <FileText size={14} className={`text-${file.type === 'md' ? 'primary' : file.type === 'yaml' ? 'secondary' : 'accent'}`} />
                        <h3 className="font-medium text-sm truncate">{file.name}</h3>
                      </div>
                      <p className="text-xs text-base-content/60 truncate">{file.description}</p>
                      <div className="text-xs text-base-content/50 mt-1">
                        {file.lastModified}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 右: エディタプレビュー */}
            <div className="flex-1 flex flex-col overflow-hidden">
              {selectedFile ? (
                <>
                  {/* ファイルヘッダー */}
                  <div className="p-4 border-b border-base-content/10 bg-base-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <FileText size={16} className={`text-${selectedFile.type === 'md' ? 'primary' : selectedFile.type === 'yaml' ? 'secondary' : 'accent'}`} />
                        <h3 className="font-semibold">{selectedFile.name}</h3>
                        <span className="text-xs text-base-content/60">({selectedFile.type})</span>
                      </div>
                      <div className="flex gap-2">
                        <button
                          className={`btn btn-sm neo-hover ${!isEditMode ? 'btn-primary' : 'btn-outline'}`}
                          onClick={() => setIsEditMode(false)}
                          title="プレビューモード"
                        >
                          <Eye size={14} />
                        </button>
                        <button
                          className={`btn btn-sm neo-hover ${isEditMode ? 'btn-primary' : 'btn-outline'}`}
                          onClick={() => setIsEditMode(true)}
                          title="編集モード"
                        >
                          <Edit3 size={14} />
                        </button>
                      </div>
                    </div>
                    <p className="text-sm text-base-content/70 mt-1">{selectedFile.description}</p>
                  </div>
                  
                  {/* ファイル内容プレビュー */}
                  <div className="flex-1 overflow-hidden">
                    {isEditMode ? (
                      <div className="h-full">
                        <Editor />
                      </div>
                    ) : (
                      <div className="h-full p-4 overflow-y-auto bg-base-50">
                        <div className="prose prose-sm max-w-none">
                          <div>
                            <h2>{selectedFile.name}</h2>
                            <p>{selectedFile.description}</p>
                            <p>このファイルには{projectName}の{selectedFile.name}に関する詳細情報が記載されています。</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center text-base-content/60">
                  <div className="text-center">
                    <FileText size={48} className="mx-auto mb-4 opacity-40" />
                    <p>ファイルを選択してください</p>
                    <p className="text-sm">左のリストからファイルをクリックすると内容が表示されます</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : currentView === 'yaml' ? (
          <YamlEditor 
            initialContent={`# ${projectName} 設定ファイル
name: ${projectName}
description: ${project.description}
version: "0.1.0"
status: ${project.status}
progress: ${project.progress}

# プロジェクト設定
settings:
  build:
    target: production
    optimization: true
  dependencies:
    - react
    - typescript
  
# 開発環境
development:
  port: 3000
  hot_reload: true
  debug: true`}
            onSave={(content) => {
              console.log('YAML設定が保存されました:', content);
            }}
          />
        ) : currentView === 'versions' ? (
          <VersionHistory 
            projectId={projectName}
            onVersionRestore={(version) => {
              console.log('バージョンを復元:', version);
            }}
            currentContent="プロジェクトの現在の状態"
          />
        ) : null}
      </div>
    </div>
  );
}