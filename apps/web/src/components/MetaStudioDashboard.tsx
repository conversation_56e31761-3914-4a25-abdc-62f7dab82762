'use client';

import { useState, useEffect } from 'react';
import { Brain, Rocket, Users, Code, Database, Zap, TrendingUp, Clock, Star, Settings } from 'lucide-react';

export default function MetaStudioDashboard() {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const stats = [
    { label: '作成プロジェクト', value: '12', icon: Rocket, color: 'text-primary' },
    { label: '実行中タスク', value: '5', icon: Clock, color: 'text-warning' },
    { label: 'アクティブエージェント', value: '8', icon: Users, color: 'text-success' },
    { label: '完了率', value: '78%', icon: TrendingUp, color: 'text-info' }
  ];

  const recentProjects = [
    { name: '瞑想アプリ_projext', type: 'mobile', progress: 75, lastUpdate: '2時間前' },
    { name: '投資bot_projext', type: 'ai', progress: 90, lastUpdate: '5時間前' },
    { name: 'iS_streamer_projext', type: 'web', progress: 25, lastUpdate: '1日前' }
  ];

  const quickActions = [
    { label: '新規プロジェクト', icon: Rocket, action: 'create-project', color: 'btn-primary' },
    { label: 'エージェント管理', icon: Users, action: 'manage-agents', color: 'btn-secondary' },
    { label: 'ファイル管理', icon: Database, action: 'file-manager', color: 'btn-accent' },
    { label: '設定', icon: Settings, action: 'settings', color: 'btn-neutral' }
  ];

  return (
    <div className="h-full bg-gradient-to-br from-base-100 via-base-100 to-base-200/50 overflow-auto">
      <div className="max-w-7xl mx-auto p-6">
        {/* ヘッダー */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-2xl flex items-center justify-center shadow-lg">
                  <Brain size={32} className="text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-success rounded-full border-2 border-base-100"></div>
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                  メタスタジオ
                </h1>
                <p className="text-lg text-base-content/70 font-medium">
                  脳内現実化ツール進化版 + Projext統合開発フレームワーク
                </p>
                <p className="text-sm text-base-content/50 mt-1">
                  最終更新: {currentTime.toLocaleString('ja-JP')}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="stats shadow bg-base-200/50 backdrop-blur-sm">
                <div className="stat p-4">
                  <div className="stat-title text-xs">稼働時間</div>
                  <div className="stat-value text-lg">24h</div>
                  <div className="stat-desc">継続中</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 統計カード */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="card bg-base-100 shadow-xl border border-base-content/10">
              <div className="card-body p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-base-content/70 font-medium">{stat.label}</p>
                    <p className="text-3xl font-bold mt-1">{stat.value}</p>
                  </div>
                  <div className={`p-3 rounded-xl bg-base-200 ${stat.color}`}>
                    <stat.icon size={24} />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 最近のプロジェクト */}
          <div className="card bg-base-100 shadow-xl border border-base-content/10">
            <div className="card-body">
              <div className="flex items-center justify-between mb-4">
                <h2 className="card-title flex items-center gap-2">
                  <Rocket size={20} className="text-primary" />
                  最近のプロジェクト
                </h2>
                <button className="btn btn-ghost btn-sm">すべて表示</button>
              </div>
              <div className="space-y-3">
                {recentProjects.map((project, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-base-200/50 transition-colors cursor-pointer">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        project.type === 'mobile' ? 'bg-primary' :
                        project.type === 'ai' ? 'bg-secondary' : 'bg-accent'
                      }`}></div>
                      <div>
                        <p className="font-medium">{project.name}</p>
                        <p className="text-sm text-base-content/60">{project.lastUpdate}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="radial-progress text-sm" style={{"--value": project.progress} as React.CSSProperties}>
                        {project.progress}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* クイックアクション */}
          <div className="card bg-base-100 shadow-xl border border-base-content/10">
            <div className="card-body">
              <h2 className="card-title flex items-center gap-2 mb-4">
                <Zap size={20} className="text-warning" />
                クイックアクション
              </h2>
              <div className="grid grid-cols-2 gap-3">
                {quickActions.map((action, index) => (
                  <button key={index} className={`btn ${action.color} btn-lg h-20 flex-col gap-2`}>
                    <action.icon size={24} />
                    <span className="text-sm">{action.label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* システム状態 */}
        <div className="mt-8">
          <div className="card bg-base-100 shadow-xl border border-base-content/10">
            <div className="card-body">
              <h2 className="card-title flex items-center gap-2 mb-4">
                <Code size={20} className="text-info" />
                システム状態
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-3">
                  <h3 className="font-semibold">エージェント階層</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-2 bg-base-200/50 rounded">
                      <span className="flex items-center gap-2">
                        👑 <span>神 (CEO)</span>
                      </span>
                      <span className="badge badge-success">アクティブ</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-base-200/50 rounded">
                      <span className="flex items-center gap-2">
                        👸 <span>王 (COO)</span>
                      </span>
                      <span className="badge badge-success">アクティブ</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-base-200/50 rounded">
                      <span className="flex items-center gap-2">
                        ⚔️ <span>将 (Manager)</span>
                      </span>
                      <span className="badge badge-warning">待機中</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-base-200/50 rounded">
                      <span className="flex items-center gap-2">
                        ⚡ <span>兵 (Worker)</span>
                      </span>
                      <span className="badge badge-info">実行中</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="font-semibold">アクティブモジュール</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>ファイルエクスプローラー</span>
                      <span className="badge badge-success">実行中</span>
                    </div>
                    <div className="flex justify-between">
                      <span>iSシステム</span>
                      <span className="badge badge-success">実行中</span>
                    </div>
                    <div className="flex justify-between">
                      <span>ターミナル</span>
                      <span className="badge badge-success">実行中</span>
                    </div>
                    <div className="flex justify-between">
                      <span>VRMビューア</span>
                      <span className="badge badge-warning">待機中</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="font-semibold">リソース使用状況</h3>
                  <div className="space-y-2">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>CPU</span>
                        <span>23%</span>
                      </div>
                      <div className="progress progress-primary w-full h-2">
                        <div className="progress-bar" style={{width: '23%'}}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>メモリ</span>
                        <span>67%</span>
                      </div>
                      <div className="progress progress-warning w-full h-2">
                        <div className="progress-bar" style={{width: '67%'}}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>ストレージ</span>
                        <span>45%</span>
                      </div>
                      <div className="progress progress-info w-full h-2">
                        <div className="progress-bar" style={{width: '45%'}}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}