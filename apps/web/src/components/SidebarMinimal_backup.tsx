'use client';

import { useState, useEffect } from 'react';
import { Folder, FileText, Settings } from 'lucide-react';

interface SidebarProps {
  onDashboardClick?: () => void;
  onProjectClick?: (project: string) => void;
  onSystemClick?: (system: string) => void;
  onFileClick?: (fileName: string, filePath?: string) => void;
  onHomeClick?: () => void;
}

export default function SidebarMinimal({ 
  onDashboardClick, 
  onProjectClick, 
  onSystemClick, 
  onFileClick, 
  onHomeClick 
}: SidebarProps) {
  const [activeSection, setActiveSection] = useState('projects');

  return (
    <div className="h-full flex flex-col p-4 neo-glass">
      {/* タイトル */}
      <div className="mb-6">
        <div 
          className="flex items-center gap-3 mb-2 p-3 rounded-lg neo-depth cursor-pointer hover:neo-hover transition-all"
          onClick={onHomeClick}
          title="ダッシュボードに戻る"
        >
          <div className="text-2xl">🤖</div>
          <div>
            <h1 className="text-lg font-bold">メタスタジオ</h1>
            <p className="text-xs text-base-content/70">脳内現実化ツール</p>
          </div>
        </div>
      </div>

      {/* プロジェクト管理エリア */}
      <div className="flex-1">
        <div className="mb-4">
          <h2 className="text-sm font-semibold text-base-content/80 mb-2">
            プロジェクト
          </h2>
          <div className="space-y-1">
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-base-200/50"
              onClick={() => onProjectClick?.('テストプロジェクト')}
            >
              <Folder size={16} />
              <span>テストプロジェクト</span>
            </div>
          </div>
        </div>

        {/* ファイル管理エリア */}
        <div className="mb-4">
          <h2 className="text-sm font-semibold text-base-content/80 mb-2">
            ファイル
          </h2>
          <div className="space-y-1">
            <div 
              className="flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-base-200/50"
              onClick={() => onFileClick?.('README.md', '/README.md')}
            >
              <FileText size={16} />
              <span>README.md</span>
            </div>
          </div>
        </div>
      </div>

      {/* 設定ボタン */}
      <div className="border-t pt-4">
        <div 
          className="flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-base-200/50"
          onClick={() => onSystemClick?.('設定')}
        >
          <Settings size={16} />
          <span>設定</span>
        </div>
      </div>
    </div>
  );
}