'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, Shield, Swords, Users, Brain, Zap, Activity, Settings } from 'lucide-react';
import AgentPersonaConfig from './AgentPersonaConfig';

interface Agent {
  id: string;
  name: string;
  type: 'god' | 'king' | 'mother' | 'general' | 'soldier';
  status: 'active' | 'busy' | 'idle' | 'learning' | 'offline';
  level: number;
  experience: number;
  skills: string[];
  currentTask?: string;
  performance: number;
  assignedProject?: string;
}

interface PersonaConfig {
  worldView: string;
  godName: string;
  kingNames: string[];
  generalNames: string[];
  soldierNames: string[];
  customEnabled: boolean;
}

export default function AgentDashboard() {
  const [showPersonaConfig, setShowPersonaConfig] = useState(false);
  const [personaConfig, setPersonaConfig] = useState<PersonaConfig>({
    worldView: 'historical',
    godName: '神様',
    kingNames: ['王子', '王', '皇帝'],
    generalNames: ['将軍', '将'],
    soldierNames: ['兵士', '兵'],
    customEnabled: false
  });

  // ローカルストレージからペルソナ設定を読み込み
  useEffect(() => {
    const saved = localStorage.getItem('agentPersonaConfig');
    if (saved) {
      try {
        setPersonaConfig(JSON.parse(saved));
      } catch (e) {
        console.error('Failed to load persona config:', e);
      }
    }
  }, []);

  const [agents, setAgents] = useState<Agent[]>([
    // 神（God）- ユーザー
    {
      id: 'god-001',
      name: `あなた（${personaConfig.godName}）`,
      type: 'god',
      status: 'active',
      level: 999,
      experience: 100000,
      skills: ['創造', '指揮', '意思決定', '戦略'],
      performance: 100,
    },
    
    // 王（King）たち
    {
      id: 'king-001',
      name: `${personaConfig.kingNames[0] || '王'}-meditation`,
      type: 'king',
      status: 'busy',
      level: 85,
      experience: 12500,
      skills: ['瞑想', 'UX設計', 'プロダクト管理'],
      currentTask: '瞑想タイマー機能設計',
      performance: 92,
      assignedProject: '瞑想アプリ_projext'
    },
    {
      id: 'king-002', 
      name: `${personaConfig.kingNames[1] || '王'}-finance`,
      type: 'king',
      status: 'active',
      level: 78,
      experience: 9800,
      skills: ['金融', 'ML', 'データ分析'],
      currentTask: '投資アルゴリズム最適化',
      performance: 88,
      assignedProject: '投資bot_projext'
    },
    {
      id: 'king-003',
      name: `${personaConfig.kingNames[2] || '王'}-entertainment`, 
      type: 'king',
      status: 'learning',
      level: 65,
      experience: 7200,
      skills: ['エンタメ', '3D', 'Live2D'],
      currentTask: 'バーチャル配信技術習得',
      performance: 75,
      assignedProject: 'iS_streamer_projext'
    },

    // 母（Mother）- CTO
    {
      id: 'mother-001',
      name: 'M-CTO',
      type: 'mother',
      status: 'active',
      level: 95,
      experience: 18000,
      skills: ['技術統括', '人材育成', 'アーキテクチャ', '学習効率化'],
      currentTask: '新エージェント生産・教育',
      performance: 95,
    },

    // 将（General）たち
    {
      id: 'general-001',
      name: `${personaConfig.generalNames[0] || '将'}-meditation`,
      type: 'general', 
      status: 'busy',
      level: 68,
      experience: 5400,
      skills: ['React Native', 'Audio API', 'UI実装'],
      currentTask: 'タイマー画面開発',
      performance: 85,
      assignedProject: '瞑想アプリ_projext'
    },
    {
      id: 'general-002',
      name: `${personaConfig.generalNames[1] || '将'}-finance`,
      type: 'general',
      status: 'active', 
      level: 72,
      experience: 6100,
      skills: ['Python', 'FastAPI', '取引API'],
      currentTask: 'バックエンドAPI構築',
      performance: 90,
      assignedProject: '投資bot_projext'
    },

    // 兵（Soldier）たち
    {
      id: 'soldier-001',
      name: `${personaConfig.soldierNames[0] || '兵'}-UI-001`,
      type: 'soldier',
      status: 'busy',
      level: 45,
      experience: 2100,
      skills: ['React', 'CSS', 'デザイン'],
      currentTask: 'ボタンコンポーネント作成',
      performance: 82,
      assignedProject: '瞑想アプリ_projext'
    },
    {
      id: 'soldier-002',
      name: `${personaConfig.soldierNames[1] || '兵'}-API-001`, 
      type: 'soldier',
      status: 'idle',
      level: 38,
      experience: 1650,
      skills: ['Python', 'データベース', 'API'],
      performance: 78,
      assignedProject: '投資bot_projext'
    },
    {
      id: 'soldier-003',
      name: `${personaConfig.soldierNames[0] || '兵'}-Test-001`,
      type: 'soldier',
      status: 'active',
      level: 42,
      experience: 1900,
      skills: ['Testing', 'QA', 'Automation'],
      currentTask: 'ユニットテスト作成',
      performance: 80,
      assignedProject: '瞑想アプリ_projext'
    }
  ]);

  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);

  const getAgentIcon = (type: Agent['type']) => {
    switch (type) {
      case 'god': return Crown;
      case 'king': return Crown;
      case 'mother': return Brain;
      case 'general': return Shield;
      case 'soldier': return Swords;
      default: return Users;
    }
  };

  const getStatusColor = (status: Agent['status']) => {
    switch (status) {
      case 'active': return 'text-success';
      case 'busy': return 'text-warning';
      case 'idle': return 'text-base-content/50';
      case 'learning': return 'text-info';
      case 'offline': return 'text-error';
      default: return 'text-base-content';
    }
  };

  const getStatusBadge = (status: Agent['status']) => {
    switch (status) {
      case 'active': return 'badge-success';
      case 'busy': return 'badge-warning';
      case 'idle': return 'badge-neutral';
      case 'learning': return 'badge-info';
      case 'offline': return 'badge-error';
      default: return 'badge-neutral';
    }
  };

  const getTypeColor = (type: Agent['type']) => {
    switch (type) {
      case 'god': return 'text-warning';
      case 'king': return 'text-primary';
      case 'mother': return 'text-secondary';
      case 'general': return 'text-accent';
      case 'soldier': return 'text-info';
      default: return 'text-base-content';
    }
  };

  const agentsByType = {
    god: agents.filter(a => a.type === 'god'),
    mother: agents.filter(a => a.type === 'mother'),
    king: agents.filter(a => a.type === 'king'),
    general: agents.filter(a => a.type === 'general'),
    soldier: agents.filter(a => a.type === 'soldier'),
  };

  return (
    <div className="h-full flex flex-col neo-glass">
      {/* ヘッダー */}
      <div className="p-4 border-b border-base-content/10 bg-base-300/80">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">エージェント管理</h1>
            <p className="text-sm text-base-content/70">階層システム: 神 → 王/母 → 将 → 兵</p>
          </div>
          <div className="flex items-center gap-2">
            <button
              className="btn btn-outline btn-sm"
              onClick={() => setShowPersonaConfig(!showPersonaConfig)}
            >
              <Settings size={16} />
              ペルソナ設定
            </button>
            <div className="stats stats-horizontal shadow">
              <div className="stat p-2">
                <div className="stat-title text-xs">総エージェント</div>
                <div className="stat-value text-lg">{agents.length}</div>
              </div>
              <div className="stat p-2">
                <div className="stat-title text-xs">アクティブ</div>
                <div className="stat-value text-lg text-success">
                  {agents.filter(a => a.status === 'active' || a.status === 'busy').length}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 階層ビュー */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-6">
          {/* 神（God）レイヤー */}
          <div className="text-center">
            <h2 className="text-lg font-bold text-warning mb-4">{personaConfig.godName}（God）</h2>
            <div className="flex justify-center">
              {agentsByType.god.map((agent) => {
                const Icon = getAgentIcon(agent.type);
                return (
                  <div key={agent.id} className="card bg-warning/10 border-2 border-warning w-48 neo-depth">
                    <div className="card-body p-4 text-center">
                      <Icon size={32} className="mx-auto text-warning mb-2" />
                      <h3 className="font-bold">{agent.name}</h3>
                      <div className="text-xs text-base-content/70">レベル {agent.level}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* 王（King）& 母（Mother）レイヤー */}
          <div className="grid grid-cols-2 gap-8">
            {/* 王たち */}
            <div>
              <h2 className="text-lg font-bold text-primary mb-4 text-center">{personaConfig.kingNames[0] || '王'}（King）</h2>
              <div className="space-y-3">
                {agentsByType.king.map((agent) => {
                  const Icon = getAgentIcon(agent.type);
                  return (
                    <div 
                      key={agent.id} 
                      className="card bg-primary/10 border border-primary/30 neo-depth cursor-pointer hover:scale-105 transition-all"
                      onClick={() => setSelectedAgent(agent.id)}
                    >
                      <div className="card-body p-3">
                        <div className="flex items-center gap-3">
                          <Icon size={24} className="text-primary" />
                          <div className="flex-1">
                            <h4 className="font-semibold text-sm">{agent.name}</h4>
                            <div className="text-xs text-base-content/70">{agent.currentTask}</div>
                          </div>
                          <div className={`badge ${getStatusBadge(agent.status)} badge-sm`}>
                            {agent.status}
                          </div>
                        </div>
                        <div className="mt-2">
                          <div className="flex justify-between text-xs mb-1">
                            <span>パフォーマンス</span>
                            <span>{agent.performance}%</span>
                          </div>
                          <progress className="progress progress-primary h-1" value={agent.performance} max="100"></progress>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* 母 */}
            <div>
              <h2 className="text-lg font-bold text-secondary mb-4 text-center">母（Mother/CTO）</h2>
              <div className="space-y-3">
                {agentsByType.mother.map((agent) => {
                  const Icon = getAgentIcon(agent.type);
                  return (
                    <div 
                      key={agent.id} 
                      className="card bg-secondary/10 border border-secondary/30 neo-depth cursor-pointer hover:scale-105 transition-all"
                      onClick={() => setSelectedAgent(agent.id)}
                    >
                      <div className="card-body p-3">
                        <div className="flex items-center gap-3">
                          <Icon size={24} className="text-secondary" />
                          <div className="flex-1">
                            <h4 className="font-semibold text-sm">{agent.name}</h4>
                            <div className="text-xs text-base-content/70">{agent.currentTask}</div>
                          </div>
                          <div className={`badge ${getStatusBadge(agent.status)} badge-sm`}>
                            {agent.status}
                          </div>
                        </div>
                        <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                          <div>レベル: {agent.level}</div>
                          <div>経験値: {agent.experience}</div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* 将（General）レイヤー */}
          <div>
            <h2 className="text-lg font-bold text-accent mb-4 text-center">{personaConfig.generalNames[0] || '将'}（General）</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {agentsByType.general.map((agent) => {
                const Icon = getAgentIcon(agent.type);
                return (
                  <div 
                    key={agent.id} 
                    className="card bg-accent/10 border border-accent/30 neo-depth cursor-pointer hover:scale-105 transition-all"
                    onClick={() => setSelectedAgent(agent.id)}
                  >
                    <div className="card-body p-3">
                      <div className="flex items-center gap-2 mb-2">
                        <Icon size={20} className="text-accent" />
                        <h4 className="font-semibold text-sm">{agent.name}</h4>
                        <div className={`badge ${getStatusBadge(agent.status)} badge-xs`}>
                          {agent.status}
                        </div>
                      </div>
                      <div className="text-xs text-base-content/70 mb-2">{agent.currentTask}</div>
                      <div className="flex justify-between text-xs">
                        <span>Lv.{agent.level}</span>
                        <span>{agent.performance}%</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* 兵（Soldier）レイヤー */}
          <div>
            <h2 className="text-lg font-bold text-info mb-4 text-center">{personaConfig.soldierNames[0] || '兵'}（Soldier）</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {agentsByType.soldier.map((agent) => {
                const Icon = getAgentIcon(agent.type);
                return (
                  <div 
                    key={agent.id} 
                    className="card bg-info/10 border border-info/30 neo-depth cursor-pointer hover:scale-105 transition-all"
                    onClick={() => setSelectedAgent(agent.id)}
                  >
                    <div className="card-body p-2">
                      <div className="flex items-center gap-1 mb-1">
                        <Icon size={16} className="text-info" />
                        <h5 className="font-semibold text-xs">{agent.name}</h5>
                      </div>
                      <div className={`badge ${getStatusBadge(agent.status)} badge-xs mb-1`}>
                        {agent.status}
                      </div>
                      <div className="text-xs text-base-content/70">Lv.{agent.level}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* 詳細パネル */}
      {selectedAgent && (
        <div className="border-t border-base-content/10 p-4 bg-base-200/50">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold">エージェント詳細</h3>
            <button 
              className="btn btn-xs btn-ghost"
              onClick={() => setSelectedAgent(null)}
            >
              ✕
            </button>
          </div>
          {(() => {
            const agent = agents.find(a => a.id === selectedAgent);
            if (!agent) return null;
            return (
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="font-medium">{agent.name}</div>
                  <div className="text-base-content/70">タスク: {agent.currentTask || '待機中'}</div>
                </div>
                <div>
                  <div>スキル: {agent.skills.join(', ')}</div>
                  <div>プロジェクト: {agent.assignedProject || 'なし'}</div>
                </div>
              </div>
            );
          })()}
        </div>
      )}

      {/* ペルソナ設定モーダル */}
      {showPersonaConfig && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-4xl h-5/6 bg-base-100 rounded-lg shadow-xl overflow-hidden">
            <div className="flex justify-between items-center p-4 border-b border-base-content/10">
              <h2 className="text-xl font-bold">ペルソナ設定</h2>
              <button
                className="btn btn-ghost btn-sm"
                onClick={() => setShowPersonaConfig(false)}
              >
                ✕
              </button>
            </div>
            <div className="h-full">
              <AgentPersonaConfig
                currentConfig={personaConfig}
                onConfigChange={(newConfig) => {
                  setPersonaConfig(newConfig);
                  // エージェント名を動的に更新
                  setAgents(currentAgents => currentAgents.map(agent => {
                    if (agent.type === 'god') {
                      return { ...agent, name: `あなた（${newConfig.godName}）` };
                    } else if (agent.type === 'king') {
                      const kingIndex = currentAgents.filter(a => a.type === 'king').indexOf(agent);
                      const kingName = newConfig.kingNames[kingIndex] || '王';
                      return { ...agent, name: agent.name.replace(/^[^-]+/, kingName) };
                    } else if (agent.type === 'general') {
                      const generalIndex = currentAgents.filter(a => a.type === 'general').indexOf(agent);
                      const generalName = newConfig.generalNames[generalIndex] || '将';
                      return { ...agent, name: agent.name.replace(/^[^-]+/, generalName) };
                    } else if (agent.type === 'soldier') {
                      const soldierIndex = currentAgents.filter(a => a.type === 'soldier').indexOf(agent);
                      const soldierName = newConfig.soldierNames[soldierIndex % newConfig.soldierNames.length] || '兵';
                      return { ...agent, name: agent.name.replace(/^[^-]+/, soldierName) };
                    }
                    return agent;
                  }));
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}