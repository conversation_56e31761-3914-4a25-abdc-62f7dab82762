'use client';

import { useState } from 'react';
import { Crown, Shield, Swords, Users, Settings } from 'lucide-react';

interface WorldView {
  id: string;
  name: string;
  theme: string;
  roles: {
    god: string[];
    king: string[];
    general: string[];
    soldier: string[];
  };
}

interface PersonaConfig {
  worldView: string;
  godName: string;
  kingNames: string[];
  generalNames: string[];
  soldierNames: string[];
  customEnabled: boolean;
}

const worldViews: WorldView[] = [
  {
    id: 'historical',
    name: '歴史・武家世界観',
    theme: '古典的な日本の武家社会をベースとした階層システム',
    roles: {
      god: ['神様', '天帝', '主上', '御主'],
      king: ['王子', '王', '皇帝', '殿', '君主'],
      general: ['将軍', '将', '大将', '武将', '侍大将'],
      soldier: ['兵士', '兵', '足軽', '武士', '侍']
    }
  },
  {
    id: 'business',
    name: 'ビジネス・企業世界観',
    theme: '現代企業の組織構造をベースとした階層システム',
    roles: {
      god: ['CEO', 'Owner', 'グランドデザイナー', '創設者'],
      king: ['COO', 'President', 'ボス', 'VP'],
      general: ['マネージャー', 'CXO', 'ディレクター', 'チームリーダー'],
      soldier: ['ワーカー', 'スタッフ', 'メンバー', 'エンジニア']
    }
  },
  {
    id: 'family',
    name: '家族・親子世界観',
    theme: '温かい家族関係をベースとした階層システム',
    roles: {
      god: ['父', '母', 'おじいちゃん', 'おばあちゃん'],
      king: ['お兄ちゃん', 'お姉ちゃん', '長男', '長女'],
      general: ['子', '息子', '娘', 'きょうだい'],
      soldier: ['孫', 'ペット', '末っ子', 'チビ']
    }
  }
];

interface AgentPersonaConfigProps {
  onConfigChange?: (config: PersonaConfig) => void;
  currentConfig?: PersonaConfig;
}

export default function AgentPersonaConfig({ 
  onConfigChange, 
  currentConfig 
}: AgentPersonaConfigProps) {
  const [config, setConfig] = useState<PersonaConfig>(currentConfig || {
    worldView: 'historical',
    godName: '神様',
    kingNames: ['王子', '王', '皇帝'],
    generalNames: ['将軍', '将'],
    soldierNames: ['兵士', '兵'],
    customEnabled: false
  });

  const [showAdvanced, setShowAdvanced] = useState(false);

  const currentWorldView = worldViews.find(w => w.id === config.worldView);

  const updateConfig = (newConfig: Partial<PersonaConfig>) => {
    const updated = { ...config, ...newConfig };
    setConfig(updated);
    onConfigChange?.(updated);
  };

  const handleWorldViewChange = (worldViewId: string) => {
    const worldView = worldViews.find(w => w.id === worldViewId);
    if (!worldView) return;

    updateConfig({
      worldView: worldViewId,
      godName: worldView.roles.god[0],
      kingNames: worldView.roles.king.slice(0, 3),
      generalNames: worldView.roles.general.slice(0, 2),
      soldierNames: worldView.roles.soldier.slice(0, 2),
      customEnabled: false
    });
  };

  const addCustomRole = (type: keyof PersonaConfig, value: string) => {
    if (!value.trim()) return;
    
    const currentArray = config[type] as string[];
    if (Array.isArray(currentArray) && !currentArray.includes(value)) {
      updateConfig({
        [type]: [...currentArray, value]
      });
    }
  };

  const removeRole = (type: keyof PersonaConfig, index: number) => {
    const currentArray = config[type] as string[];
    if (Array.isArray(currentArray)) {
      updateConfig({
        [type]: currentArray.filter((_, i) => i !== index)
      });
    }
  };

  return (
    <div className="h-full flex flex-col neo-glass">
      {/* ヘッダー */}
      <div className="p-4 border-b border-base-content/10 bg-base-300/80">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">エージェントペルソナ設定</h1>
            <p className="text-sm text-base-content/70">世界観とエージェントの役割名をカスタマイズ</p>
          </div>
          <button
            className="btn btn-sm btn-outline"
            onClick={() => setShowAdvanced(!showAdvanced)}
          >
            <Settings size={16} />
            {showAdvanced ? '基本設定' : '詳細設定'}
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* 世界観選択 */}
        <div className="card bg-base-200 shadow-xl neo-depth">
          <div className="card-body">
            <h2 className="card-title">世界観テーマ選択</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {worldViews.map((worldView) => (
                <div
                  key={worldView.id}
                  className={`card cursor-pointer transition-all neo-hover ${
                    config.worldView === worldView.id
                      ? 'bg-primary/20 border-2 border-primary'
                      : 'bg-base-100 border border-base-content/20 hover:border-primary/50'
                  }`}
                  onClick={() => handleWorldViewChange(worldView.id)}
                >
                  <div className="card-body p-4">
                    <h3 className="font-bold text-sm">{worldView.name}</h3>
                    <p className="text-xs text-base-content/70">{worldView.theme}</p>
                    {config.worldView === worldView.id && (
                      <div className="badge badge-primary badge-sm mt-2">選択中</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 役割プレビュー */}
        {currentWorldView && (
          <div className="card bg-base-200 shadow-xl neo-depth">
            <div className="card-body">
              <h2 className="card-title">現在の階層設定プレビュー</h2>
              <div className="space-y-4">
                {/* 神 */}
                <div className="flex items-center gap-4 p-3 bg-warning/10 rounded-lg border border-warning/30">
                  <Crown size={24} className="text-warning" />
                  <div className="flex-1">
                    <h3 className="font-bold text-warning">神（God）</h3>
                    <p className="text-sm">{config.godName}</p>
                  </div>
                </div>

                {/* 王 */}
                <div className="flex items-center gap-4 p-3 bg-primary/10 rounded-lg border border-primary/30">
                  <Crown size={24} className="text-primary" />
                  <div className="flex-1">
                    <h3 className="font-bold text-primary">王（King/COO）</h3>
                    <p className="text-sm">{config.kingNames.join(', ')}</p>
                  </div>
                </div>

                {/* 将 */}
                <div className="flex items-center gap-4 p-3 bg-accent/10 rounded-lg border border-accent/30">
                  <Shield size={24} className="text-accent" />
                  <div className="flex-1">
                    <h3 className="font-bold text-accent">将（General）</h3>
                    <p className="text-sm">{config.generalNames.join(', ')}</p>
                  </div>
                </div>

                {/* 兵 */}
                <div className="flex items-center gap-4 p-3 bg-info/10 rounded-lg border border-info/30">
                  <Swords size={24} className="text-info" />
                  <div className="flex-1">
                    <h3 className="font-bold text-info">兵（Soldier）</h3>
                    <p className="text-sm">{config.soldierNames.join(', ')}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 詳細カスタマイズ */}
        {showAdvanced && (
          <div className="card bg-base-200 shadow-xl neo-depth">
            <div className="card-body">
              <h2 className="card-title">カスタム役割名</h2>
              <p className="text-sm text-base-content/70 mb-4">
                独自の呼び方を追加できます（例：⚪︎くん、⚪︎ちゃん など）
              </p>
              
              <div className="space-y-4">
                {/* 神のカスタマイズ */}
                <div>
                  <label className="label">
                    <span className="label-text font-semibold">神（God）の呼び方</span>
                  </label>
                  <input
                    type="text"
                    className="input input-bordered w-full"
                    value={config.godName}
                    onChange={(e) => updateConfig({ godName: e.target.value })}
                    placeholder="神様"
                  />
                </div>

                {/* 王のカスタマイズ */}
                <div>
                  <label className="label">
                    <span className="label-text font-semibold">王（King）の呼び方</span>
                  </label>
                  <div className="space-y-2">
                    {config.kingNames.map((name, index) => (
                      <div key={index} className="flex gap-2">
                        <input
                          type="text"
                          className="input input-bordered flex-1"
                          value={name}
                          onChange={(e) => {
                            const newNames = [...config.kingNames];
                            newNames[index] = e.target.value;
                            updateConfig({ kingNames: newNames });
                          }}
                        />
                        <button
                          className="btn btn-error btn-sm"
                          onClick={() => removeRole('kingNames', index)}
                        >
                          削除
                        </button>
                      </div>
                    ))}
                    <div className="flex gap-2">
                      <input
                        type="text"
                        className="input input-bordered flex-1"
                        placeholder="新しい王の呼び方"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            addCustomRole('kingNames', e.currentTarget.value);
                            e.currentTarget.value = '';
                          }
                        }}
                      />
                      <button className="btn btn-primary btn-sm">追加</button>
                    </div>
                  </div>
                </div>

                {/* 将のカスタマイズ */}
                <div>
                  <label className="label">
                    <span className="label-text font-semibold">将（General）の呼び方</span>
                  </label>
                  <div className="space-y-2">
                    {config.generalNames.map((name, index) => (
                      <div key={index} className="flex gap-2">
                        <input
                          type="text"
                          className="input input-bordered flex-1"
                          value={name}
                          onChange={(e) => {
                            const newNames = [...config.generalNames];
                            newNames[index] = e.target.value;
                            updateConfig({ generalNames: newNames });
                          }}
                        />
                        <button
                          className="btn btn-error btn-sm"
                          onClick={() => removeRole('generalNames', index)}
                        >
                          削除
                        </button>
                      </div>
                    ))}
                    <div className="flex gap-2">
                      <input
                        type="text"
                        className="input input-bordered flex-1"
                        placeholder="新しい将の呼び方"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            addCustomRole('generalNames', e.currentTarget.value);
                            e.currentTarget.value = '';
                          }
                        }}
                      />
                      <button className="btn btn-primary btn-sm">追加</button>
                    </div>
                  </div>
                </div>

                {/* 兵のカスタマイズ */}
                <div>
                  <label className="label">
                    <span className="label-text font-semibold">兵（Soldier）の呼び方</span>
                  </label>
                  <div className="space-y-2">
                    {config.soldierNames.map((name, index) => (
                      <div key={index} className="flex gap-2">
                        <input
                          type="text"
                          className="input input-bordered flex-1"
                          value={name}
                          onChange={(e) => {
                            const newNames = [...config.soldierNames];
                            newNames[index] = e.target.value;
                            updateConfig({ soldierNames: newNames });
                          }}
                        />
                        <button
                          className="btn btn-error btn-sm"
                          onClick={() => removeRole('soldierNames', index)}
                        >
                          削除
                        </button>
                      </div>
                    ))}
                    <div className="flex gap-2">
                      <input
                        type="text"
                        className="input input-bordered flex-1"
                        placeholder="新しい兵の呼び方"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            addCustomRole('soldierNames', e.currentTarget.value);
                            e.currentTarget.value = '';
                          }
                        }}
                      />
                      <button className="btn btn-primary btn-sm">追加</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 適用ボタン */}
        <div className="flex justify-end gap-2">
          <button
            className="btn btn-outline"
            onClick={() => {
              // デフォルト設定にリセット
              const defaultWorldView = worldViews[0];
              updateConfig({
                worldView: defaultWorldView.id,
                godName: defaultWorldView.roles.god[0],
                kingNames: defaultWorldView.roles.king.slice(0, 3),
                generalNames: defaultWorldView.roles.general.slice(0, 2),
                soldierNames: defaultWorldView.roles.soldier.slice(0, 2),
                customEnabled: false
              });
            }}
          >
            デフォルトに戻す
          </button>
          <button
            className="btn btn-primary"
            onClick={() => {
              // 設定を保存（実際の実装では localStorage や API に保存）
              localStorage.setItem('agentPersonaConfig', JSON.stringify(config));
              // 保存成功の通知
              const notification = document.createElement('div');
              notification.className = 'fixed top-4 right-4 z-50 bg-success text-success-content px-4 py-2 rounded-lg shadow-lg';
              notification.textContent = 'エージェントペルソナ設定を保存しました！';
              document.body.appendChild(notification);
              setTimeout(() => {
                notification.remove();
              }, 3000);
            }}
          >
            設定を保存
          </button>
        </div>
      </div>
    </div>
  );
}