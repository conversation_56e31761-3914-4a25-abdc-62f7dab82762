'use client';

import { useState } from 'react';
import { Crown, Swords, Users, Play } from 'lucide-react';
import { processPxCommand } from '@/lib/ai-agents';

export default function ZellijTerminal() {
  const [activeTab, setActiveTab] = useState('meditation');
  const [currentCommand, setCurrentCommand] = useState('');

  const tabs = [
    {
      id: 'meditation',
      name: '王:瞑想',
      icon: <Crown size={14} />,
      status: '神と対話',
      color: 'text-primary'
    },
    {
      id: 'investment',
      name: '王:投資',
      icon: <Crown size={14} />,
      status: 'Projext',
      color: 'text-secondary'
    },
    {
      id: 'is',
      name: '王:iS',
      icon: <Crown size={14} />,
      status: '企画中',
      color: 'text-accent'
    }
  ];

  const terminalContent = {
    meditation: [
      '> px chat "瞑想タイマーの要件を詰めたい"',
      'K: 瞑想タイマーについて詳しくお聞かせください。',
      'G: 5分、10分、15分のプリセットがほしい',
      'K: 承知いたしました。音声ガイドも必要でしょうか？',
      '> px agents generate',
      'M: 瞑想アプリ将と音声兵、UI兵を生産いたします...',
      '✅ 瞑想アプリ将が配属されました',
      '> px run T-001_create_timer_ui',
      'S: タイマーUIの実装を開始します...',
      '█'
    ],
    investment: [
      '> px init investment_bot',
      '📁 投資bot_projext/ を作成しました',
      '> px chat "株価予測機能について"',
      'K: どのような予測アルゴリズムをご希望ですか？',
      '> px review',
      'K: 現在の進捗を確認中...',
      '█'
    ],
    is: [
      '> px init is_streamer',
      '📁 iS_streamer_projext/ を作成しました',
      '> px chat "バーチャル配信者の人格設定"',
      'K: どのような個性をお求めでしょうか？',
      'G: 猫系ギャルで明るい感じ',
      'K: 「みけちゃん」として設計いたします',
      '█'
    ]
  };

  const executeCommand = async () => {
    if (!currentCommand.trim()) return;

    try {
      // AI-powered px コマンド処理
      const result = await processPxCommand(currentCommand);
      
      // 結果を表示（実際の実装では履歴に追加）
      alert(result);
    } catch (error) {
      console.error('Command execution error:', error);
      alert('コマンドの実行中にエラーが発生しました。');
    }

    setCurrentCommand('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      executeCommand();
    }
  };

  return (
    <div className="h-full flex flex-col bg-base-300/60 text-base-content min-h-[200px]">
      {/* タブバー */}
      <div className="flex border-b border-base-content/20 bg-base-300/40 flex-shrink-0">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center gap-2 px-4 py-2 text-sm border-r border-neutral-content/20 neo-hover transition-colors ${
              activeTab === tab.id ? 'bg-primary/20 neo-depth' : 'hover:bg-neutral-focus'
            }`}
          >
            <span className={tab.color}>{tab.icon}</span>
            <span>{tab.name}</span>
            <span className="text-xs opacity-60">{tab.status}</span>
          </button>
        ))}
        
        {/* 将・兵管理ボタン */}
        <div className="ml-auto flex">
          <button className="flex items-center gap-1 px-3 py-2 text-sm neo-hover hover:bg-neutral-focus">
            <Swords size={14} className="text-warning" />
            将→兵管理
          </button>
          <button className="flex items-center gap-1 px-3 py-2 text-sm neo-hover hover:bg-neutral-focus">
            <Play size={14} className="text-success" />
            px run
          </button>
        </div>
      </div>

      {/* ターミナルコンテンツ */}
      <div className="flex-1 p-4 overflow-y-auto neo-smooth min-h-0">
        <div className="font-mono text-sm space-y-1.5 leading-relaxed">
          {terminalContent[activeTab as keyof typeof terminalContent].map((line, index) => (
            <div key={index} className={index === terminalContent[activeTab as keyof typeof terminalContent].length - 1 ? 'animate-pulse' : ''}>
              {line}
            </div>
          ))}
        </div>
      </div>

      {/* コマンド入力エリア - 固定高さで見切れ防止 */}
      <div className="p-4 border-t border-base-content/20 bg-base-300/40 flex-shrink-0 min-h-[80px]">
        <div className="flex items-center gap-3">
          <span className="text-primary font-mono text-base font-semibold">px</span>
          <input
            type="text"
            placeholder="コマンドを入力... (px init, px chat, px run など)"
            className="input bg-base-200/80 border-base-content/20 flex-1 font-mono neo-depth text-base h-12"
            value={currentCommand}
            onChange={(e) => setCurrentCommand(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          <button 
            className="btn btn-primary neo-hover h-12 px-6"
            onClick={executeCommand}
          >
            実行
          </button>
        </div>
      </div>
    </div>
  );
}