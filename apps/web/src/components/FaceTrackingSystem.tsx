'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Camera, CameraOff, Settings, Wifi, WifiOff, AlertCircle, CheckCircle, Zap } from 'lucide-react';

interface FaceTrackingData {
  // MediaPipe基準の表情パラメータ（0-1の範囲）
  jawOpen: number;
  mouthSmileLeft: number;
  mouthSmileRight: number;
  mouthFrownLeft: number;
  mouthFrownRight: number;
  eyeBlinkLeft: number;
  eyeBlinkRight: number;
  browDownLeft: number;
  browDownRight: number;
  browInnerUp: number;
  browOuterUpLeft: number;
  browOuterUpRight: number;
  cheekPuff: number;
  noseSneerLeft: number;
  noseSneerRight: number;
  // 頭部回転（ラジアン）
  headRotationX: number;
  headRotationY: number;
  headRotationZ: number;
  // VRM標準表情（変換後）
  happy: number;
  angry: number;
  sad: number;
  relaxed: number;
  surprised: number;
  aa: number;
  ih: number;
  ou: number;
  ee: number;
  oh: number;
}

interface VTubeStudioConnection {
  isConnected: boolean;
  port: number;
  authToken?: string;
  modelId?: string;
}

interface FaceTrackingSystemProps {
  onTrackingData?: (data: FaceTrackingData) => void;
  vtubeStudioMode?: boolean;
  targetCharacterId?: string;
  className?: string;
}

export default function FaceTrackingSystem({
  onTrackingData,
  vtubeStudioMode = false,
  targetCharacterId,
  className = ''
}: FaceTrackingSystemProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isTracking, setIsTracking] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [vtubeStudio, setVtubeStudio] = useState<VTubeStudioConnection>({
    isConnected: false,
    port: 8001
  });
  const [trackingData, setTrackingData] = useState<FaceTrackingData | null>(null);
  const [isVideoVisible, setIsVideoVisible] = useState(true);

  // MediaPipe関連の状態
  const [mediaPipe, setMediaPipe] = useState<any>(null);
  const [faceDetection, setFaceDetection] = useState<any>(null);

  // MediaPipeの初期化
  const initializeMediaPipe = useCallback(async () => {
    try {
      // 動的インポートでMediaPipeを読み込み
      const { FaceLandmarker, FilesetResolver } = await import('@mediapipe/tasks-vision');
      
      const vision = await FilesetResolver.forVisionTasks(
        'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.0/wasm'
      );
      
      const faceLandmarker = await FaceLandmarker.createFromOptions(vision, {
        baseOptions: {
          modelAssetPath: 'https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task',
          delegate: 'GPU'
        },
        outputFaceBlendshapes: true,
        runningMode: 'VIDEO',
        numFaces: 1
      });

      setFaceDetection(faceLandmarker);
      setIsInitialized(true);
      console.log('🎯 MediaPipe Face Landmarker 初期化完了');
    } catch (err) {
      console.error('MediaPipe初期化エラー:', err);
      setError(`MediaPipe初期化失敗: ${err}`);
    }
  }, []);

  // カメラアクセス
  const initializeCamera = useCallback(async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          frameRate: { ideal: 30 }
        }
      });

      setStream(mediaStream);
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        videoRef.current.onloadedmetadata = () => {
          videoRef.current?.play();
        };
      }

      console.log('📷 カメラアクセス成功');
    } catch (err) {
      console.error('カメラアクセスエラー:', err);
      setError(`カメラアクセス失敗: ${err}`);
    }
  }, []);

  // VTube Studio WebSocket接続
  const connectToVTubeStudio = useCallback(async () => {
    if (!vtubeStudioMode) return;

    try {
      const ws = new WebSocket(`ws://localhost:${vtubeStudio.port}/`);
      
      ws.onopen = () => {
        console.log('🎭 VTube Studio接続成功');
        setVtubeStudio(prev => ({ ...prev, isConnected: true }));
        
        // 認証リクエスト
        ws.send(JSON.stringify({
          apiName: 'VTubeStudioPublicAPI',
          apiVersion: '1.0',
          requestID: 'auth_request',
          messageType: 'AuthenticationTokenRequest',
          data: {
            pluginName: 'Meta Studio Face Tracking',
            pluginDeveloper: 'Meta Studio',
            pluginIcon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAC0lEQVQIHWNgAAIAAAUAAY27m/MAAAAASUVORK5CYII='
          }
        }));
      };

      ws.onmessage = (event) => {
        const response = JSON.parse(event.data);
        if (response.messageType === 'AuthenticationTokenResponse') {
          setVtubeStudio(prev => ({ ...prev, authToken: response.data.authenticationToken }));
          console.log('🔐 VTube Studio認証完了');
        }
      };

      ws.onclose = () => {
        setVtubeStudio(prev => ({ ...prev, isConnected: false }));
        console.log('🔌 VTube Studio接続終了');
      };

      ws.onerror = (err) => {
        console.error('VTube Studio接続エラー:', err);
        setError('VTube Studio接続失敗');
      };

    } catch (err) {
      console.error('VTube Studio接続エラー:', err);
      setError(`VTube Studio接続失敗: ${err}`);
    }
  }, [vtubeStudioMode, vtubeStudio.port]);

  // ARKitからVRM表情への変換
  const convertARKitToVRM = useCallback((blendShapes: any): FaceTrackingData => {
    const get = (name: string) => blendShapes.find((bs: any) => bs.categoryName === name)?.score || 0;

    // MediaPipeのblend shapesからVRM表情への変換
    const jawOpen = get('jawOpen');
    const mouthSmileLeft = get('mouthSmileLeft');
    const mouthSmileRight = get('mouthSmileRight');
    const eyeBlinkLeft = get('eyeBlinkLeft');
    const eyeBlinkRight = get('eyeBlinkRight');

    return {
      // MediaPipe生データ
      jawOpen,
      mouthSmileLeft,
      mouthSmileRight,
      mouthFrownLeft: get('mouthFrownLeft'),
      mouthFrownRight: get('mouthFrownRight'),
      eyeBlinkLeft,
      eyeBlinkRight,
      browDownLeft: get('browDownLeft'),
      browDownRight: get('browDownRight'),
      browInnerUp: get('browInnerUp'),
      browOuterUpLeft: get('browOuterUpLeft'),
      browOuterUpRight: get('browOuterUpRight'),
      cheekPuff: get('cheekPuff'),
      noseSneerLeft: get('noseSneerLeft'),
      noseSneerRight: get('noseSneerRight'),
      headRotationX: 0, // 要計算
      headRotationY: 0, // 要計算
      headRotationZ: 0, // 要計算

      // VRM標準表情への変換
      happy: Math.max(mouthSmileLeft, mouthSmileRight),
      angry: Math.max(get('browDownLeft'), get('browDownRight')),
      sad: Math.max(get('mouthFrownLeft'), get('mouthFrownRight')),
      relaxed: 1 - Math.max(eyeBlinkLeft, eyeBlinkRight),
      surprised: get('browInnerUp'),
      aa: jawOpen,
      ih: get('mouthShrugUpper') || 0,
      ou: get('mouthPucker') || 0,
      ee: get('mouthStretchLeft') || get('mouthStretchRight') || 0,
      oh: get('mouthFunnel') || 0
    };
  }, []);

  // フレーム処理ループ
  const processFrame = useCallback(() => {
    if (!faceDetection || !videoRef.current || !isTracking) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    
    if (video.videoWidth === 0 || video.videoHeight === 0) return;

    try {
      const results = faceDetection.detectForVideo(video, Date.now());
      
      if (results.faceBlendshapes && results.faceBlendshapes.length > 0) {
        const blendShapes = results.faceBlendshapes[0].categories;
        const convertedData = convertARKitToVRM(blendShapes);
        
        setTrackingData(convertedData);
        onTrackingData?.(convertedData);

        // キャンバスに顔の検出状況を描画
        if (canvas) {
          const ctx = canvas.getContext('2d');
          if (ctx && results.faceLandmarks?.[0]) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.strokeStyle = '#00ff00';
            ctx.lineWidth = 1;
            
            // 顔のランドマークを描画
            const landmarks = results.faceLandmarks[0];
            landmarks.forEach((landmark: any) => {
              const x = landmark.x * canvas.width;
              const y = landmark.y * canvas.height;
              ctx.beginPath();
              ctx.arc(x, y, 1, 0, 2 * Math.PI);
              ctx.stroke();
            });
          }
        }
      }
    } catch (err) {
      console.error('フレーム処理エラー:', err);
    }

    if (isTracking) {
      requestAnimationFrame(processFrame);
    }
  }, [faceDetection, isTracking, onTrackingData, convertARKitToVRM]);

  // トラッキング開始
  const startTracking = useCallback(async () => {
    if (!isInitialized) {
      await initializeMediaPipe();
    }
    
    if (!stream) {
      await initializeCamera();
    }

    if (vtubeStudioMode && !vtubeStudio.isConnected) {
      await connectToVTubeStudio();
    }

    setIsTracking(true);
    setError(null);
  }, [isInitialized, stream, vtubeStudioMode, vtubeStudio.isConnected, initializeMediaPipe, initializeCamera, connectToVTubeStudio]);

  // トラッキング停止
  const stopTracking = useCallback(() => {
    setIsTracking(false);
    
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
  }, [stream]);

  // フレーム処理ループの開始
  useEffect(() => {
    if (isTracking && faceDetection) {
      requestAnimationFrame(processFrame);
    }
  }, [isTracking, faceDetection, processFrame]);

  return (
    <div className={`bg-base-100 rounded-lg border border-base-content/20 overflow-hidden ${className}`}>
      {/* ヘッダー */}
      <div className="p-3 border-b border-base-content/10 bg-base-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Camera size={16} className="text-primary" />
            <span className="text-sm font-medium">フェイストラッキング</span>
            {targetCharacterId && (
              <div className="badge badge-xs badge-outline">{targetCharacterId}</div>
            )}
          </div>
          <div className="flex items-center gap-1">
            {vtubeStudioMode && (
              <div className="flex items-center gap-1">
                {vtubeStudio.isConnected ? (
                  <Wifi size={12} className="text-success" />
                ) : (
                  <WifiOff size={12} className="text-error" />
                )}
                <span className="text-xs">VTS</span>
              </div>
            )}
            {isTracking && (
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                <span className="text-xs">LIVE</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* ビデオプレビュー */}
      <div className="relative">
        <div className="aspect-video bg-base-300 relative overflow-hidden">
          <video
            ref={videoRef}
            className={`w-full h-full object-cover ${isVideoVisible ? '' : 'opacity-50'}`}
            autoPlay
            muted
            playsInline
          />
          <canvas
            ref={canvasRef}
            className="absolute inset-0 w-full h-full pointer-events-none"
            width="640"
            height="480"
          />
          
          {/* オーバーレイ情報 */}
          <div className="absolute top-2 left-2 space-y-1">
            {error && (
              <div className="flex items-center gap-1 bg-error/80 text-error-content px-2 py-1 rounded text-xs">
                <AlertCircle size={12} />
                <span>エラー</span>
              </div>
            )}
            {isInitialized && (
              <div className="flex items-center gap-1 bg-success/80 text-success-content px-2 py-1 rounded text-xs">
                <CheckCircle size={12} />
                <span>MediaPipe</span>
              </div>
            )}
            {vtubeStudio.isConnected && (
              <div className="flex items-center gap-1 bg-primary/80 text-primary-content px-2 py-1 rounded text-xs">
                <Zap size={12} />
                <span>VTube Studio</span>
              </div>
            )}
          </div>

          {/* トラッキングデータ表示 */}
          {trackingData && (
            <div className="absolute top-2 right-2 bg-base-200/90 p-2 rounded text-xs space-y-1 font-mono">
              <div>😊 {(trackingData.happy * 100).toFixed(0)}%</div>
              <div>😠 {(trackingData.angry * 100).toFixed(0)}%</div>
              <div>👄 {(trackingData.aa * 100).toFixed(0)}%</div>
              <div>👁️ {((1 - Math.max(trackingData.eyeBlinkLeft, trackingData.eyeBlinkRight)) * 100).toFixed(0)}%</div>
            </div>
          )}
        </div>
      </div>

      {/* コントロールパネル */}
      <div className="p-3 space-y-2">
        <div className="flex gap-2">
          {!isTracking ? (
            <button
              className="btn btn-primary btn-sm flex-1"
              onClick={startTracking}
              disabled={!!error}
            >
              <Camera size={12} />
              トラッキング開始
            </button>
          ) : (
            <button
              className="btn btn-error btn-sm flex-1"
              onClick={stopTracking}
            >
              <CameraOff size={12} />
              停止
            </button>
          )}
          <button
            className="btn btn-ghost btn-sm"
            onClick={() => setIsVideoVisible(!isVideoVisible)}
          >
            <Settings size={12} />
          </button>
        </div>

        {error && (
          <div className="text-xs text-error bg-error/10 p-2 rounded">
            {error}
          </div>
        )}

        {/* VTube Studio設定 */}
        {vtubeStudioMode && (
          <div className="text-xs space-y-1">
            <div className="flex items-center justify-between">
              <span>VTube Studio:</span>
              <span className={vtubeStudio.isConnected ? 'text-success' : 'text-error'}>
                {vtubeStudio.isConnected ? '接続中' : '未接続'}
              </span>
            </div>
            <div className="text-base-content/60">ポート: {vtubeStudio.port}</div>
          </div>
        )}
      </div>
    </div>
  );
}