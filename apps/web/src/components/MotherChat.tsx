'use client';

import { useState, useEffect } from 'react';
import { Send, Mic, Settings, BarChart3, MicOff } from 'lucide-react';
import { chatWithMother } from '@/lib/ai-agents';
import { useContinuousVoiceInput } from '@/hooks/useContinuousVoiceInput';
import { useCharacter } from '../contexts/CharacterContext';
import AICharacterPanel from './AICharacterPanel';

export default function MotherChat() {
  const [message, setMessage] = useState('');
  const [currentTime, setCurrentTime] = useState('10:30');
  const [showSettings, setShowSettings] = useState(false);
  const [corePrompt, setCorePrompt] = useState('神からの指示を受けて、最適なエージェントを生成・管理します。要件定義から実装までを一貫してサポートします。');
  const [selectedAIModel, setSelectedAIModel] = useState('claude-3-haiku');
  const [showStats, setShowStats] = useState(false);
  const [showManagement, setShowManagement] = useState(false);

  // AIモデル設定
  const aiModels = [
    { id: 'claude-3-haiku', name: 'Claude 3 Haiku', provider: 'Anthropic', speed: '高速', cost: '低', quality: '標準' },
    { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', provider: 'Anthropic', speed: '中', cost: '中', quality: '高' },
    { id: 'claude-3-opus', name: 'Claude 3 Opus', provider: 'Anthropic', speed: '低', cost: '高', quality: '最高' },
    { id: 'gpt-4', name: 'GPT-4', provider: 'OpenAI', speed: '中', cost: '高', quality: '高' },
    { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', provider: 'OpenAI', speed: '高速', cost: '低', quality: '標準' }
  ];

  const currentModel = aiModels.find(model => model.id === selectedAIModel);

  // キャラクター連携
  const { playAnimation, setEmotion, updateLastMessage, setActive } = useCharacter();
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'mother',
      content: '神よ、何をお作りになりますか？新しいアイデアをお聞かせください。',
      timestamp: '10:30'
    },
    {
      id: 2,
      type: 'user',
      content: '瞑想アプリを作りたいんだ',
      timestamp: '10:31'
    },
    {
      id: 3,
      type: 'mother',
      content: '素晴らしいです！瞑想アプリ将と必要な兵たちを生産いたします。どのような瞑想機能をお求めでしょうか？',
      timestamp: '10:31'
    }
  ]);

  // 音声入力フック
  const {
    isListening,
    transcript,
    interimTranscript,
    isSupported: isVoiceSupported,
    error: voiceError,
    startListening,
    stopListening,
    resetTranscript,
    fullTranscript
  } = useContinuousVoiceInput({
    language: 'ja-JP',
    continuous: true,
    interimResults: true
  });

  // 音声入力の確定テキストをメッセージフィールドに反映
  useEffect(() => {
    if (transcript) {
      setMessage(prev => prev + transcript);
      resetTranscript();
    }
  }, [transcript, resetTranscript]);

  // 音声入力中のキャラクター連携
  useEffect(() => {
    if (isListening) {
      playAnimation('listen');
    } else {
      playAnimation('idle');
    }
  }, [isListening, playAnimation]);

  // クライアントサイドでのみ実行される時刻更新
  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      setCurrentTime(now.toLocaleTimeString('ja-JP', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }));
    };
    
    updateTime();
    const interval = setInterval(updateTime, 60000);
    
    return () => clearInterval(interval);
  }, []);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && e.altKey) {
      e.preventDefault();
      sendMessage();
    }
    // Allow normal Enter key to create new lines in textarea
  };

  const sendMessage = async () => {
    if (!message.trim()) return;

    // キャラクターを発話モードに
    playAnimation('speak');
    updateLastMessage(message);
    setActive(true);

    // ユーザーメッセージを追加
    const userMessage = {
      id: Date.now(),
      type: 'user' as const,
      content: message,
      timestamp: new Date().toLocaleTimeString('ja-JP', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    };

    setMessages(prev => [...prev, userMessage]);
    setMessage('');

    try {
      // キャラクターを思考モードに
      playAnimation('think');

      // AI応答を取得（選択されたモデルで）
      const aiResponse = await chatWithMother(message, {
        activeProjects: 3,
        activeAgents: 15,
        systemStatus: '正常',
        corePrompt: corePrompt,
        modelId: selectedAIModel
      });

      // モデル特性に応じたキャラクター反応
      if (currentModel?.quality === '最高') {
        setEmotion('confident');
      } else if (currentModel?.speed === '高速') {
        setEmotion('energetic');
      }

      // キャラクターを聞き取りモードに
      playAnimation('listen');
      updateLastMessage(aiResponse);

      // AI応答を追加
      const aiMessage = {
        id: Date.now() + 1,
        type: 'mother' as const,
        content: aiResponse,
        timestamp: new Date().toLocaleTimeString('ja-JP', { 
          hour: '2-digit', 
          minute: '2-digit' 
        })
      };

      setMessages(prev => [...prev, aiMessage]);

      // 応答内容によってキャラクターの感情を設定
      if (aiResponse.includes('素晴らしい') || aiResponse.includes('完璧') || aiResponse.includes('優秀')) {
        setEmotion('happy');
      } else if (aiResponse.includes('検討') || aiResponse.includes('分析') || aiResponse.includes('考えます')) {
        setEmotion('thinking');
      } else {
        setEmotion('neutral');
      }

    } catch (error) {
      console.error('Chat error:', error);
      // エラー時はキャラクターをアイドル状態に
      playAnimation('idle');
    }
  };

  return (
    <div className="h-full flex flex-col bg-base-200/60">
      {/* AIキャラクターパネル */}
      <AICharacterPanel />
      
      {/* ヘッダー */}
      <div className="p-4 border-b border-base-content/10 bg-base-200/40">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-8 h-8 bg-secondary/20 rounded-full flex items-center justify-center text-sm font-bold">M</div>
            <div>
              <h2 className="font-semibold">母（CTO）</h2>
              <p className="text-xs text-base-content/70">技術・人材統括</p>
            </div>
          </div>
          {/* モデル表示と設定 */}
          <div className="flex items-center gap-2">
            <div className="text-xs text-base-content/60 bg-base-200 px-2 py-1 rounded-full">
              {currentModel?.name} ({currentModel?.speed})
            </div>
            <button
              className="btn btn-ghost btn-circle btn-sm"
              onClick={() => setShowSettings(!showSettings)}
              title="チャット設定"
            >
              <Settings size={16} />
            </button>
          </div>
        </div>
        
        {/* 学習統計・人材管理 */}
        <div className="flex gap-2 mt-3">
          <button 
            className="btn btn-xs btn-outline neo-hover"
            onClick={() => setShowStats(!showStats)}
          >
            <BarChart3 size={12} />
            学習統計
          </button>
          <button 
            className="btn btn-xs btn-outline neo-hover"
            onClick={() => setShowManagement(!showManagement)}
          >
            <Settings size={12} />
            人材管理
          </button>
        </div>
      </div>

      {/* 学習統計パネル */}
      {showStats && (
        <div className="p-4 border-t border-base-content/10 bg-info/5 space-y-3">
          <h3 className="text-sm font-semibold flex items-center gap-2">
            <BarChart3 size={16} />
            学習統計ダッシュボード
          </h3>
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div className="bg-base-200 p-2 rounded">
              <div className="font-medium">今日の学習</div>
              <div className="text-primary text-lg font-bold">47分</div>
            </div>
            <div className="bg-base-200 p-2 rounded">
              <div className="font-medium">メッセージ数</div>
              <div className="text-secondary text-lg font-bold">128</div>
            </div>
            <div className="bg-base-200 p-2 rounded">
              <div className="font-medium">累積時間</div>
              <div className="text-accent text-lg font-bold">24.5h</div>
            </div>
            <div className="bg-base-200 p-2 rounded">
              <div className="font-medium">スキルレベル</div>
              <div className="text-warning text-lg font-bold">Lv.12</div>
            </div>
          </div>
          <div className="text-xs text-base-content/60">
            最新更新: {new Date().toLocaleTimeString()}
          </div>
        </div>
      )}
      
      {/* 人材管理パネル */}
      {showManagement && (
        <div className="p-4 border-t border-base-content/10 bg-secondary/5 space-y-3">
          <h3 className="text-sm font-semibold flex items-center gap-2">
            <Settings size={16} />
            AI人材管理システム
          </h3>
          <div className="space-y-2 text-xs">
            <div className="flex justify-between items-center p-2 bg-base-200 rounded">
              <span>コーディングエージェント</span>
              <div className="badge badge-success badge-xs">アクティブ</div>
            </div>
            <div className="flex justify-between items-center p-2 bg-base-200 rounded">
              <span>デザインエージェント</span>
              <div className="badge badge-warning badge-xs">待機</div>
            </div>
            <div className="flex justify-between items-center p-2 bg-base-200 rounded">
              <span>テストエージェント</span>
              <div className="badge badge-info badge-xs">練習中</div>
            </div>
          </div>
          <button className="btn btn-xs btn-primary w-full">
            + 新しいエージェントを生成
          </button>
        </div>
      )}

      {/* 設定パネル */}
      {showSettings && (
        <div className="p-4 border-t border-base-content/10 bg-base-300/50 space-y-4">
          {/* AIモデル選択 */}
          <div>
            <h3 className="text-sm font-semibold mb-2 flex items-center gap-2">
              🤖 AIモデル選択
              <span className="badge badge-sm badge-info">新機能</span>
            </h3>
            <select 
              className="select select-bordered select-sm w-full"
              value={selectedAIModel}
              onChange={(e) => setSelectedAIModel(e.target.value)}
            >
              {aiModels.map(model => (
                <option key={model.id} value={model.id}>
                  {model.name} ({model.provider}) - 速度:{model.speed} コスト:{model.cost} 品質:{model.quality}
                </option>
              ))}
            </select>
            {currentModel && (
              <div className="text-xs text-base-content/60 mt-1 p-2 bg-base-200 rounded">
                現在選択: <strong>{currentModel.name}</strong> ・ プロバイダー: {currentModel.provider} ・ 特性: 速度{currentModel.speed}/コスト{currentModel.cost}/品質{currentModel.quality}
              </div>
            )}
          </div>

          {/* コアプロンプト設定 */}
          <div>
            <h3 className="text-sm font-semibold mb-2">📝 コアプロンプト設定</h3>
            <textarea
              className="textarea textarea-bordered w-full text-xs"
              rows={3}
              placeholder="例: プロジェクト作成時は要件定義から始めて..."
              value={corePrompt}
              onChange={(e) => setCorePrompt(e.target.value)}
            />
          </div>
          
          <div className="flex justify-end gap-2 mt-2">
            <button className="btn btn-xs btn-ghost">リセット</button>
            <button className="btn btn-xs btn-primary">保存</button>
          </div>
        </div>
      )}

      {/* チャットエリア */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((msg) => (
          <div
            key={msg.id}
            className={`chat ${msg.type === 'user' ? 'chat-end' : 'chat-start'}`}
          >
            <div className="chat-image avatar">
              <div className="w-8 rounded-full">
                <div className="bg-primary/20 w-full h-full flex items-center justify-center text-sm">
                  {msg.type === 'user' ? 'U' : 'M'}
                </div>
              </div>
            </div>
            <div className="chat-header">
              {msg.type === 'user' ? '神' : '母'}
              <time className="text-xs opacity-50 ml-1">{msg.timestamp}</time>
            </div>
            <div className={`chat-bubble neo-depth ${msg.type === 'user' ? 'chat-bubble-primary' : 'chat-bubble-secondary'} max-w-xs md:max-w-md lg:max-w-lg whitespace-pre-wrap break-words`}>
              {msg.content}
            </div>
          </div>
        ))}
      </div>

      {/* 入力エリア */}
      <div className="p-4 border-t border-base-content/10">
        <div className="flex gap-2">
          <textarea
            placeholder={isListening 
              ? `聞いています... 「どうぞ」で停止 ${interimTranscript ? `(${interimTranscript})` : ''}` 
              : "母に指示を... (Alt+Enterで送信)"
            }
            className="textarea textarea-bordered flex-1 textarea-sm resize-y"
            rows={3}
            value={message + (isListening ? interimTranscript : '')}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            style={{ minHeight: '4rem', maxHeight: '12rem' }}
          />
          <div className="flex flex-col gap-1" style={{ minHeight: '4rem' }}>
            <button 
              className="btn btn-primary btn-sm neo-hover flex-1"
              onClick={sendMessage}
              title="メッセージを送信 (Alt+Enter)"
            >
              <Send size={16} />
            </button>
            <button
              className={`btn btn-sm neo-hover flex-1 ${
                isListening 
                  ? 'btn-error animate-pulse' 
                  : isVoiceSupported 
                    ? 'btn-outline' 
                    : 'btn-disabled'
              }`}
              onClick={() => {
                if (isListening) {
                  stopListening();
                } else {
                  startListening();
                }
              }}
              disabled={!isVoiceSupported}
              title={
                !isVoiceSupported 
                  ? '音声入力未対応: Chrome、Edge、Safariをお使いください' 
                  : isListening 
                    ? '音声入力停止 (「どうぞ」でも停止)' 
                    : '音声入力開始 (マイクアクセス許可が必要)'
              }
            >
              {voiceError ? '❌' : isListening ? <MicOff size={16} /> : <Mic size={16} />}
            </button>
          </div>
        </div>
        
        {/* 音声入力エラー表示 */}
        {voiceError && (
          <div className="mt-2 p-2 bg-error/10 border border-error/20 rounded text-xs text-error">
            🚨 {voiceError}
          </div>
        )}
        
        {/* 音声入力状態表示 */}
        {isListening && (
          <div className="mt-2 p-2 bg-primary/10 border border-primary/20 rounded text-xs text-primary">
            🎤 音声を聞いています... 「どうぞ」で停止
            {interimTranscript && (
              <div className="mt-1 text-primary/70">
                暫定: {interimTranscript}
              </div>
            )}
          </div>
        )}
        
        {/* KPI・進捗表示 */}
        <div className="mt-3 p-2 bg-base-300/60 rounded text-xs">
          <div className="text-base-content/70 mb-1">リアルタイム統計</div>
          <div className="grid grid-cols-2 gap-2">
            <div>実現率: 92%</div>
            <div>学習効率: +15%</div>
            <div>進行中: 3件</div>
            <div>完了: 127件</div>
          </div>
        </div>
      </div>
    </div>
  );
}