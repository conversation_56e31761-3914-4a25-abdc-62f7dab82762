'use client';

import { useState, useEffect } from 'react';
import { FileText, Edit3, Eye, Code, Download, Share } from 'lucide-react';
import Editor from './Editor';
import YamlVisualizationView from './YamlVisualizationView';

interface FileViewerProps {
  fileName: string;
  filePath?: string;
}

export default function FileViewer({ fileName, filePath }: FileViewerProps) {
  const [isEditMode, setIsEditMode] = useState(true); // デフォルトを編集モードに変更
  const [fileContent, setFileContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 実際のファイル内容を読み込む
  useEffect(() => {
    const loadFileContent = async () => {
      if (!filePath) {
        setFileContent(getDefaultFileContent(fileName));
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        const fileType = getFileType(fileName);
        
        // 画像・動画・音声・PDFファイルはバイナリなのでAPIからの読み込みをスキップ
        if (['image', 'video', 'audio', 'pdf'].includes(fileType)) {
          // これらのファイルはAPIエンドポイントから直接表示するのでcontentは不要
          setFileContent(''); 
          setLoading(false);
          return;
        }
        
        // テキストファイルのみJSONレスポンスを期待
        const response = await fetch(`/api/file-content?path=${encodeURIComponent(filePath)}`);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
          setFileContent(data.content);
        } else {
          setError(data.error || 'ファイルの読み込みに失敗しました');
          setFileContent(getDefaultFileContent(fileName));
        }
      } catch (err) {
        console.error('File loading error:', err);
        setError(err instanceof Error ? err.message : 'ファイルの読み込みに失敗しました');
        setFileContent(getDefaultFileContent(fileName));
      } finally {
        setLoading(false);
      }
    };

    loadFileContent();
  }, [fileName, filePath]);
  
  const getFileType = (name: string) => {
    const ext = name.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'md': return 'markdown';
      case 'yaml': case 'yml': return 'yaml';
      case 'json': return 'json';
      case 'js': case 'jsx': case 'ts': case 'tsx': return 'javascript';
      case 'css': return 'css';
      case 'html': return 'html';
      case 'png': case 'jpg': case 'jpeg': case 'gif': case 'webp': case 'svg': return 'image';
      case 'pdf': return 'pdf';
      case 'mp4': case 'mov': case 'avi': case 'webm': return 'video';
      case 'mp3': case 'wav': case 'ogg': return 'audio';
      default: return 'text';
    }
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'markdown': return '📝';
      case 'yaml': return '⚙️';
      case 'json': return '🔧';
      case 'javascript': return '💻';
      case 'css': return '🎨';
      case 'html': return '🌐';
      case 'image': return '🖼️';
      case 'pdf': return '📄';
      case 'video': return '🎬';
      case 'audio': return '🎵';
      default: return '📄';
    }
  };

  const getDefaultFileContent = (name: string) => {
    // フォールバック用のデフォルトコンテンツ
    const type = getFileType(name);
    switch (name) {
      case 'README.md':
        return `# Meta Studio

## 概要
Meta Studio（メタスタジオ）は脳内現実化ツールとして設計された統合開発プラットフォームです。

## 特徴
- 🎯 プロジェクト管理とファイル操作
- 🤖 AIキャラクター統合
- 📊 リアルタイムダッシュボード
- 🛠️ 開発ツール統合

## 使用方法
1. プロジェクトを作成
2. 要件定義ファイルを編集
3. AIアシスタントと連携して開発

## ライセンス
MIT License`;

      case 'task.md':
        return `# タスク管理

## 現在の実装状況
- ✅ プロジェクト管理機能
- 🔄 ファイル表示機能（実装中）
- ⏸️ VRMキャラクター機能

## 優先度
1. ファイル表示の修正
2. UI/UX改善
3. エージェント機能強化`;

      case 'test-yaml.yaml':
        return `# Meta Studio設定ファイル
name: meta-studio
version: "1.0.0"
description: "脳内現実化ツール"

settings:
  theme: metastudio
  locale: ja
  
features:
  - project-management
  - ai-integration
  - file-explorer
  
dependencies:
  next: "15.3.3"
  react: "19.0.0"
  typescript: "5.3.3"`;

      default:
        return `# ${name}

このファイルの内容がここに表示されます。

ファイルタイプ: ${type}
ファイル名: ${name}
パス: ${filePath || 'N/A'}

## 編集
右上の編集ボタンをクリックして内容を編集できます。`;
    }
  };

  const fileType = getFileType(fileName);
  const fileIcon = getFileIcon(fileType);

  return (
    <div className="flex-1 flex flex-col overflow-hidden bg-base-100">
      {/* ファイルヘッダー */}
      <div className="p-4 border-b border-base-content/10 bg-base-200/40">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="text-2xl">{fileIcon}</div>
            <div>
              <h1 className="text-xl font-bold">{fileName}</h1>
              <p className="text-sm text-base-content/70 capitalize">{fileType} ファイル</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* 表示/編集切り替え */}
            <button
              className={`btn btn-sm neo-hover ${!isEditMode ? 'btn-primary' : 'btn-outline'}`}
              onClick={() => setIsEditMode(false)}
              title="プレビューモード"
            >
              <Eye size={14} />
              プレビュー
            </button>
            <button
              className={`btn btn-sm neo-hover ${isEditMode ? 'btn-primary' : 'btn-outline'}`}
              onClick={() => setIsEditMode(true)}
              title="編集モード"
            >
              <Edit3 size={14} />
              編集
            </button>
            
            <div className="divider divider-horizontal"></div>
            
            {/* アクションボタン */}
            <button className="btn btn-sm btn-ghost neo-hover" title="ダウンロード">
              <Download size={14} />
            </button>
            <button className="btn btn-sm btn-ghost neo-hover" title="共有">
              <Share size={14} />
            </button>
          </div>
        </div>
        
        {filePath && (
          <div className="mt-2 text-xs text-base-content/50 font-mono bg-base-300/30 px-2 py-1 rounded">
            <div className="flex items-center gap-1">
              <span>📍</span>
              <span className="text-base-content/40">パス:</span>
              <span className="text-base-content/70">
                {/* パス表示の改善: フルパスを確実に表示 */}
                {(() => {
                  // ファイルパスの正規化と表示
                  let displayPath = filePath;
                  
                  // ファイル名のみの場合、フルパスが不明なのでそのまま表示
                  if (!displayPath.includes('/')) {
                    return (
                      <span className="text-warning">
                        {displayPath} (ファイル名のみ)
                      </span>
                    );
                  }
                  
                  // パスを正規化して表示
                  const normalizedPath = displayPath.startsWith('/') ? displayPath.slice(1) : displayPath;
                  const segments = normalizedPath.split('/').filter(Boolean);
                  
                  return segments.map((segment, index, array) => (
                    <span key={index}>
                      {index > 0 && <span className="text-base-content/30 mx-1">/</span>}
                      <span className={index === array.length - 1 ? 'text-primary' : 'text-base-content/60'}>
                        {segment}
                      </span>
                    </span>
                  ));
                })()
              }
              </span>
            </div>
          </div>
        )}
      </div>

      {/* ファイル内容表示エリア */}
      <div className="flex-1 overflow-hidden">
        {loading ? (
          <div className="h-full flex items-center justify-center">
            <div className="flex items-center gap-3 text-base-content/60">
              <div className="loading loading-spinner loading-md"></div>
              <span>ファイルを読み込み中...</span>
            </div>
          </div>
        ) : error ? (
          <div className="h-full flex items-center justify-center">
            <div className="alert alert-warning max-w-md">
              <span className="text-sm">⚠️ {error}</span>
            </div>
          </div>
        ) : isEditMode && fileType !== 'image' && fileType !== 'video' && fileType !== 'audio' && fileType !== 'pdf' ? (
          <div className="h-full">
            <Editor initialContent={fileContent} filePath={filePath} />
          </div>
        ) : (
          <div className="h-full">
            {fileType === 'yaml' ? (
              // YAMLファイル専用のyaml2table風ビューアー
              <YamlVisualizationView 
                yamlContent={fileContent} 
                filePath={filePath || fileName}
              />
            ) : fileType === 'markdown' ? (
              <div className="h-full p-6 overflow-y-auto bg-base-50">
                <div className="prose prose-sm max-w-none">
                  <pre className="whitespace-pre-wrap text-sm leading-relaxed">
                    {fileContent}
                  </pre>
                </div>
              </div>
            ) : fileType === 'json' ? (
              <div className="h-full p-6 overflow-y-auto bg-base-50">
                <div className="bg-base-200/50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Code size={16} className="text-accent" />
                    <span className="text-sm font-medium text-accent">
                      JSON 構造表示
                    </span>
                  </div>
                  <pre className="text-sm font-mono bg-base-300/30 p-4 rounded-lg overflow-x-auto">
                    {fileContent}
                  </pre>
                </div>
              </div>
            ) : fileType === 'image' ? (
              <div className="h-full p-6 overflow-y-auto bg-base-50 flex items-center justify-center">
                <div className="max-w-full max-h-full">
                  <div className="text-center">
                    <div className="mb-4">
                      <div className="text-6xl mb-2">🖼️</div>
                      <h3 className="text-lg font-medium text-base-content/80">画像ビューワー</h3>
                      <p className="text-sm text-base-content/60 mt-2">{fileName}</p>
                    </div>
                    {filePath && (
                      <div className="relative">
                        <img
                          src={`/api/file-content?path=${encodeURIComponent(filePath)}&type=image`}
                          alt={fileName}
                          className="max-w-full max-h-96 object-contain rounded-lg shadow-lg mx-auto"
                          onLoad={() => {
                            console.log('画像の読み込み成功:', filePath);
                          }}
                          onError={(e) => {
                            console.error('画像の読み込みに失敗:', { filePath, error: e });
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            
                            // 親要素にエラー表示を追加
                            const container = target.parentElement;
                            if (container && !container.querySelector('.image-error')) {
                              const errorDiv = document.createElement('div');
                              errorDiv.className = 'image-error text-center text-base-content/60 p-4 border-2 border-dashed border-base-300 rounded-lg';
                              errorDiv.innerHTML = `
                                <div class="text-4xl mb-2">❌</div>
                                <div class="text-sm font-medium">画像の読み込みに失敗しました</div>
                                <div class="text-xs text-base-content/40 mt-2">パス: ${filePath}</div>
                                <div class="text-xs text-base-content/30 mt-1">API: /api/file-content?path=${encodeURIComponent(filePath)}&type=image</div>
                                <div class="mt-3">
                                  <button class="btn btn-xs btn-outline" onclick="window.open('/api/file-content?path=${encodeURIComponent(filePath)}&type=image', '_blank')">
                                    直接確認
                                  </button>
                                </div>
                              `;
                              container.appendChild(errorDiv);
                            }
                          }}
                        />
                      </div>
                    )}
                    {!filePath && (
                      <div className="text-center text-base-content/60 p-4 border-2 border-dashed border-base-300 rounded-lg">
                        <div className="text-4xl mb-2">📂</div>
                        <div className="text-sm">ファイルパスが指定されていません</div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : fileType === 'video' ? (
              <div className="h-full p-6 overflow-y-auto bg-base-50 flex items-center justify-center">
                <div className="max-w-full max-h-full">
                  <video
                    src={filePath ? `/api/file-content?path=${encodeURIComponent(filePath)}` : ''}
                    controls
                    className="max-w-full max-h-full rounded-lg shadow-lg"
                  >
                    ご利用のブラウザは動画の再生に対応していません。
                  </video>
                  <div className="mt-4 text-center text-sm text-base-content/60">
                    <p>{fileName}</p>
                  </div>
                </div>
              </div>
            ) : fileType === 'audio' ? (
              <div className="h-full p-6 overflow-y-auto bg-base-50 flex items-center justify-center">
                <div className="w-full max-w-md">
                  <div className="text-center mb-6">
                    <div className="text-6xl mb-4">🎵</div>
                    <h3 className="text-lg font-medium">{fileName}</h3>
                  </div>
                  <audio
                    src={filePath ? `/api/file-content?path=${encodeURIComponent(filePath)}` : ''}
                    controls
                    className="w-full"
                  >
                    ご利用のブラウザは音声の再生に対応していません。
                  </audio>
                </div>
              </div>
            ) : fileType === 'pdf' ? (
              <div className="h-full p-6 overflow-y-auto bg-base-50">
                <div className="h-full">
                  <iframe
                    src={filePath ? `/api/file-content?path=${encodeURIComponent(filePath)}` : ''}
                    className="w-full h-full rounded-lg shadow-lg"
                    title={fileName}
                  >
                    PDFを表示できません。
                  </iframe>
                </div>
              </div>
            ) : (
              <div className="h-full p-6 overflow-y-auto bg-base-50">
                <div className="bg-base-200/30 rounded-lg p-4">
                  <pre className="text-sm font-mono whitespace-pre-wrap">
                    {fileContent}
                  </pre>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}