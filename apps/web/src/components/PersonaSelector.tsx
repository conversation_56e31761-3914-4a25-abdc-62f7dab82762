'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Z<PERSON>, <PERSON>, <PERSON>, <PERSON>rk<PERSON>, <PERSON>, Settings, Plus, ChevronDown } from 'lucide-react';
import { personaSystem, PersonaTheme, PersonaRole, PERSONA_THEMES } from '@/utils/agent-persona-system';

interface PersonaSelectorProps {
  onPersonaSelect?: (persona: PersonaRole, theme: PersonaTheme) => void;
  onThemeChange?: (theme: PersonaTheme) => void;
  selectedPersona?: PersonaRole | null;
  className?: string;
}

export default function PersonaSelector({ 
  onPersonaSelect, 
  onThemeChange,
  selectedPersona,
  className = "" 
}: PersonaSelectorProps) {
  const [currentTheme, setCurrentTheme] = useState<PersonaTheme>(personaSystem.getCurrentTheme());
  const [showThemeSelector, setShowThemeSelector] = useState(false);
  const [showCustomCreator, setShowCustomCreator] = useState(false);
  const [selectedRole, setSelectedRole] = useState<PersonaRole | null>(selectedPersona || null);

  useEffect(() => {
    personaSystem.loadSavedTheme();
    setCurrentTheme(personaSystem.getCurrentTheme());
  }, []);

  const handleThemeChange = (theme: PersonaTheme) => {
    personaSystem.setTheme(theme.id);
    setCurrentTheme(theme);
    setShowThemeSelector(false);
    onThemeChange?.(theme);
    
    // テーマ変更時に選択をリセット
    setSelectedRole(null);
  };

  const handlePersonaSelect = (persona: PersonaRole) => {
    setSelectedRole(persona);
    onPersonaSelect?.(persona, currentTheme);
  };

  const getLevelIcon = (level: number) => {
    switch (level) {
      case 1: return <Crown className="w-4 h-4" />;
      case 2: return <Star className="w-4 h-4" />;
      case 3: return <Zap className="w-4 h-4" />;
      case 4: return <Users className="w-4 h-4" />;
      default: return <Bot className="w-4 h-4" />;
    }
  };

  const getLevelColor = (level: number) => {
    switch (level) {
      case 1: return 'from-yellow-400 to-yellow-600'; // 神 - 金
      case 2: return 'from-purple-400 to-purple-600'; // 王 - 紫
      case 3: return 'from-blue-400 to-blue-600';     // 将 - 青
      case 4: return 'from-green-400 to-green-600';   // 兵 - 緑
      default: return 'from-gray-400 to-gray-600';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* テーマ選択 */}
      <div className="relative">
        <button
          onClick={() => setShowThemeSelector(!showThemeSelector)}
          className="w-full flex items-center justify-between p-3 bg-base-200 rounded-lg hover:bg-base-300 transition-colors"
        >
          <div className="flex items-center space-x-3">
            <div className="text-2xl">{currentTheme.roles[0]?.emoji || '🎭'}</div>
            <div className="text-left">
              <div className="font-medium">{currentTheme.name}</div>
              <div className="text-sm text-base-content/70">{currentTheme.description}</div>
            </div>
          </div>
          <ChevronDown className={`w-4 h-4 transition-transform ${showThemeSelector ? 'rotate-180' : ''}`} />
        </button>

        {showThemeSelector && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-base-100 border border-base-300 rounded-lg shadow-lg z-10">
            {PERSONA_THEMES.map((theme) => (
              <button
                key={theme.id}
                onClick={() => handleThemeChange(theme)}
                className={`w-full text-left p-3 hover:bg-base-200 transition-colors first:rounded-t-lg last:rounded-b-lg ${
                  currentTheme.id === theme.id ? 'bg-primary/20' : ''
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">{theme.roles[0]?.emoji || '🎭'}</div>
                  <div>
                    <div className="font-medium">{theme.name}</div>
                    <div className="text-sm text-base-content/70">{theme.worldView}</div>
                  </div>
                </div>
              </button>
            ))}
            
            <div className="border-t border-base-300">
              <button
                onClick={() => setShowCustomCreator(true)}
                className="w-full text-left p-3 hover:bg-base-200 transition-colors rounded-b-lg"
              >
                <div className="flex items-center space-x-3">
                  <Plus className="w-5 h-5" />
                  <span className="font-medium">カスタムペルソナセットを作成</span>
                </div>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* ペルソナ一覧 */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold flex items-center space-x-2">
          <Sparkles className="w-5 h-5" />
          <span>エージェント階層</span>
        </h3>
        
        <div className="grid gap-3">
          {currentTheme.roles.map((persona) => (
            <div
              key={persona.id}
              onClick={() => handlePersonaSelect(persona)}
              className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-lg ${
                selectedRole?.id === persona.id
                  ? 'border-primary bg-primary/10 shadow-md'
                  : 'border-base-300 bg-base-100 hover:border-primary/50'
              }`}
            >
              <div className="flex items-start space-x-4">
                {/* レベル表示 */}
                <div className={`flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-br ${getLevelColor(persona.level)} flex items-center justify-center text-white`}>
                  <div className="text-center">
                    <div className="text-lg">{persona.emoji}</div>
                  </div>
                </div>

                {/* ペルソナ詳細 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    {getLevelIcon(persona.level)}
                    <span className="font-bold text-lg">{persona.name}</span>
                    <span className="text-sm text-base-content/60">{persona.title}</span>
                  </div>
                  
                  <p className="text-sm text-base-content/80 mb-2">
                    {persona.description}
                  </p>
                  
                  <div className="space-y-2">
                    <div>
                      <span className="text-xs font-medium text-base-content/60">能力:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {persona.abilities.map((ability) => (
                          <span
                            key={ability}
                            className="px-2 py-1 text-xs bg-base-200 rounded-full"
                          >
                            {ability}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <span className="text-xs font-medium text-base-content/60">話し方:</span>
                      <p className="text-xs text-base-content/70 mt-1">{persona.speakingStyle}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 選択中のペルソナ表示 */}
      {selectedRole && (
        <div className="mt-4 p-3 bg-primary/10 border border-primary/30 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <div className="text-lg">{selectedRole.emoji}</div>
            <span className="font-medium">選択中:</span>
            <span className="font-bold">{selectedRole.name}</span>
          </div>
          <div className="text-sm text-base-content/70">
            レベル{selectedRole.level} | {currentTheme.name}テーマ
          </div>
        </div>
      )}

      {/* カスタムペルソナ作成モーダル */}
      {showCustomCreator && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-base-100 rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-bold mb-4">カスタムペルソナセット作成</h3>
            <p className="text-sm text-base-content/70 mb-4">
              独自の世界観とペルソナを作成できます。例: 〇くん/〇ちゃんなど
            </p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setShowCustomCreator(false)}
                className="btn btn-ghost"
              >
                キャンセル
              </button>
              <button
                onClick={() => {
                  // TODO: カスタム作成画面への遷移
                  setShowCustomCreator(false);
                  console.log('カスタムペルソナ作成機能は今後実装予定');
                }}
                className="btn btn-primary"
              >
                作成開始
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}