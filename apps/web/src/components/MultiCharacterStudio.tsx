'use client';

import { useState, useRef } from 'react';
import { Users, Video, Mic, Play, Pause, Square, Settings, Plus, Trash2, VolumeX, Volume2, Camera, User, Scan } from 'lucide-react';
import VRMViewer from './VRMViewer';
import FaceTrackingSystem from './FaceTrackingSystem';

interface Character {
  id: string;
  name: string;
  type: 'AI' | 'Human';
  avatar: string;
  voice: string;
  personality: string;
  isActive: boolean;
  isMuted: boolean;
  vrmUrl?: string;
  faceTrackingEnabled?: boolean;
}

interface FaceTrackingData {
  happy: number;
  angry: number;
  sad: number;
  relaxed: number;
  surprised: number;
  aa: number;
  ih: number;
  ou: number;
  ee: number;
  oh: number;
  eyeBlinkLeft: number;
  eyeBlinkRight: number;
  headRotationX: number;
  headRotationY: number;
  headRotationZ: number;
}

interface RecordingSession {
  id: string;
  title: string;
  duration: number;
  participants: string[];
  status: 'idle' | 'recording' | 'paused' | 'completed';
  createdAt: Date;
  videoUrl?: string;
}

export default function MultiCharacterStudio() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [characters, setCharacters] = useState<Character[]>([
    {
      id: 'ai-1',
      name: 'AITuber - アイ',
      type: 'AI',
      avatar: '🤖',
      voice: 'female',
      personality: '知的で親しみやすいAIアシスタント。最新技術について詳しく説明できる',
      isActive: true,
      isMuted: false,
      faceTrackingEnabled: false
    },
    {
      id: 'human-1',
      name: 'VTuber - あなた',
      type: 'Human',
      avatar: '👨‍💼',
      voice: 'male',
      personality: 'エネルギッシュなVTuber。視聴者との交流を大切にする',
      isActive: true,
      isMuted: false,
      faceTrackingEnabled: true
    }
  ]);

  // フェイストラッキング関連の状態
  const [faceTrackingData, setFaceTrackingData] = useState<FaceTrackingData | null>(null);
  const [selectedTrackingCharacter, setSelectedTrackingCharacter] = useState<string>('human-1');
  const [vtubeStudioMode, setVtubeStudioMode] = useState(false);

  const [currentSession, setCurrentSession] = useState<RecordingSession | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [sessions, setSessions] = useState<RecordingSession[]>([]);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const addCharacter = () => {
    const newCharacter: Character = {
      id: `char-${Date.now()}`,
      name: `キャラクター${characters.length + 1}`,
      type: 'AI',
      avatar: '🎭',
      voice: 'neutral',
      personality: 'カスタム',
      isActive: false,
      isMuted: false
    };
    setCharacters(prev => [...prev, newCharacter]);
  };

  const removeCharacter = (id: string) => {
    setCharacters(prev => prev.filter(char => char.id !== id));
  };

  const toggleCharacterActive = (id: string) => {
    setCharacters(prev => prev.map(char => 
      char.id === id ? { ...char, isActive: !char.isActive } : char
    ));
  };

  const toggleCharacterMute = (id: string) => {
    setCharacters(prev => prev.map(char => 
      char.id === id ? { ...char, isMuted: !char.isMuted } : char
    ));
  };

  const toggleFaceTracking = (id: string) => {
    setCharacters(prev => prev.map(char => 
      char.id === id ? { ...char, faceTrackingEnabled: !char.faceTrackingEnabled } : char
    ));
    
    // フェイストラッキング対象キャラクターの更新
    const character = characters.find(c => c.id === id);
    if (character?.faceTrackingEnabled) {
      setSelectedTrackingCharacter(id);
    }
  };

  const handleFaceTrackingData = (data: FaceTrackingData) => {
    setFaceTrackingData(data);
    
    // 選択されたキャラクターのVRMに適用
    const targetCharacter = characters.find(c => c.id === selectedTrackingCharacter);
    if (targetCharacter?.faceTrackingEnabled) {
      // VRMViewerにフェイストラッキングデータを送信
      // この部分は後でVRMViewerコンポーネントに実装
      console.log(`フェイストラッキングデータ送信 -> ${targetCharacter.name}:`, data);
    }
  };

  const startRecording = () => {
    const activeChars = characters.filter(char => char.isActive);
    if (activeChars.length < 2) {
      alert('対談には最低2人のキャラクターが必要です');
      return;
    }

    const newSession: RecordingSession = {
      id: Date.now().toString(),
      title: `対談 ${new Date().toLocaleString()}`,
      duration: 0,
      participants: activeChars.map(char => char.name),
      status: 'recording',
      createdAt: new Date()
    };

    setCurrentSession(newSession);
    setIsRecording(true);
    setRecordingTime(0);

    intervalRef.current = setInterval(() => {
      setRecordingTime(prev => prev + 1);
    }, 1000);
  };

  const pauseRecording = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    setIsRecording(false);
    if (currentSession) {
      setCurrentSession({
        ...currentSession,
        status: 'paused'
      });
    }
  };

  const stopRecording = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    setIsRecording(false);
    
    if (currentSession) {
      const completedSession = {
        ...currentSession,
        duration: recordingTime,
        status: 'completed' as const,
        videoUrl: `recording-${currentSession.id}.mp4`
      };
      setSessions(prev => [completedSession, ...prev]);
      setCurrentSession(null);
    }
    setRecordingTime(0);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="border-b border-base-content/10 bg-base-200/40">
      {/* ヘッダー */}
      <div className="p-3 border-b border-base-content/10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-secondary/20 rounded-full flex items-center justify-center text-xs">
              🎬
            </div>
            <h3 className="font-medium text-sm">マルチキャラクター対談スタジオ</h3>
          </div>
          <div className="flex items-center gap-1">
            {isRecording && (
              <div className="flex items-center gap-1 text-error">
                <div className="w-2 h-2 bg-error rounded-full animate-pulse"></div>
                <span className="text-xs font-mono">{formatTime(recordingTime)}</span>
              </div>
            )}
            <button
              className="btn btn-ghost btn-xs"
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              {isCollapsed ? '▼' : '▲'}
            </button>
          </div>
        </div>
      </div>

      {/* コンテンツ */}
      {!isCollapsed && (
        <div className="p-3 space-y-3">
          {/* キャラクター管理 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium">参加キャラクター</span>
              <button className="btn btn-xs btn-outline" onClick={addCharacter}>
                <Plus size={8} />
                追加
              </button>
            </div>

            <div className="grid grid-cols-1 gap-2 max-h-40 overflow-y-auto">
              {characters.map((character) => (
                <div
                  key={character.id}
                  className={`relative rounded-lg border transition-all duration-200 max-w-md ${
                    character.isActive 
                      ? 'border-primary bg-primary/10 shadow-md' 
                      : 'border-base-content/20 bg-base-100'
                  }`}
                >
                  {/* 参加ステータス表示 */}
                  {character.isActive && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-success rounded-full border-2 border-base-100 animate-pulse"></div>
                  )}
                  
                  <div className="p-2">
                    <div className="flex items-center gap-2">
                      {/* アバター */}
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-base ${
                        character.isActive ? 'bg-primary/30' : 'bg-base-200'
                      }`}>
                        {character.avatar}
                      </div>
                      
                      {/* キャラクター情報 */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-1">
                          <div className="text-sm font-semibold truncate">{character.name}</div>
                          <div className={`badge badge-xs ${
                            character.type === 'AI' ? 'badge-secondary' : 'badge-accent'
                          }`}>
                            {character.type}
                          </div>
                        </div>
                        <div className="text-xs text-base-content/60 truncate">
                          {character.personality}
                        </div>
                      </div>
                      
                      {/* コンパクトなコントロール */}
                      <div className="flex gap-1 flex-shrink-0">
                        <button
                          className={`btn btn-xs btn-circle ${
                            character.isMuted ? 'btn-error' : 'btn-success'
                          }`}
                          onClick={() => toggleCharacterMute(character.id)}
                          title={character.isMuted ? '音声オン' : '音声オフ'}
                        >
                          {character.isMuted ? <VolumeX size={8} /> : <Volume2 size={8} />}
                        </button>
                        
                        {character.type === 'Human' && (
                          <button
                            className={`btn btn-xs btn-circle ${
                              character.faceTrackingEnabled ? 'btn-info' : 'btn-outline'
                            }`}
                            onClick={() => toggleFaceTracking(character.id)}
                            title={character.faceTrackingEnabled ? 'フェイストラッキング停止' : 'フェイストラッキング開始'}
                          >
                            <Scan size={8} />
                          </button>
                        )}
                        
                        <button
                          className={`btn btn-xs btn-circle ${
                            character.isActive ? 'btn-primary' : 'btn-outline'
                          }`}
                          onClick={() => toggleCharacterActive(character.id)}
                          title={character.isActive ? '参加中' : '参加'}
                        >
                          {character.isActive ? '✓' : '+'}
                        </button>
                        
                        {characters.length > 2 && (
                          <button
                            className="btn btn-xs btn-circle btn-error btn-outline"
                            onClick={() => removeCharacter(character.id)}
                            title="削除"
                          >
                            <Trash2 size={8} />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* フェイストラッキングシステム */}
          {characters.some(char => char.faceTrackingEnabled) && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium">フェイストラッキング</span>
                <div className="flex items-center gap-1">
                  <select
                    className="select select-xs select-bordered"
                    value={selectedTrackingCharacter}
                    onChange={(e) => setSelectedTrackingCharacter(e.target.value)}
                  >
                    {characters
                      .filter(char => char.type === 'Human' && char.faceTrackingEnabled)
                      .map(char => (
                        <option key={char.id} value={char.id}>
                          {char.name}
                        </option>
                      ))
                    }
                  </select>
                  <button
                    className={`btn btn-xs ${vtubeStudioMode ? 'btn-primary' : 'btn-outline'}`}
                    onClick={() => setVtubeStudioMode(!vtubeStudioMode)}
                    title="VTube Studio統合"
                  >
                    VTS
                  </button>
                </div>
              </div>
              
              <FaceTrackingSystem
                onTrackingData={handleFaceTrackingData}
                vtubeStudioMode={vtubeStudioMode}
                targetCharacterId={selectedTrackingCharacter}
                className="max-w-sm"
              />
            </div>
          )}

          {/* 録画コントロール */}
          <div className="bg-base-100 rounded-lg p-3 space-y-2">
            <div className="text-xs font-medium">録画コントロール</div>
            
            {currentSession ? (
              <div className="space-y-2">
                <div className="text-xs">
                  <div className="font-medium">{currentSession.title}</div>
                  <div className="text-base-content/60">
                    参加者: {currentSession.participants.join(', ')}
                  </div>
                </div>
                <div className="flex gap-1 justify-center">
                  {isRecording ? (
                    <>
                      <button className="btn btn-xs btn-warning flex-1" onClick={pauseRecording}>
                        <Pause size={8} />
                        一時停止
                      </button>
                      <button className="btn btn-xs btn-error flex-1" onClick={stopRecording}>
                        <Square size={8} />
                        停止
                      </button>
                    </>
                  ) : (
                    <>
                      <button className="btn btn-xs btn-success flex-1" onClick={() => setIsRecording(true)}>
                        <Play size={8} />
                        再開
                      </button>
                      <button className="btn btn-xs btn-error flex-1" onClick={stopRecording}>
                        <Square size={8} />
                        終了
                      </button>
                    </>
                  )}
                </div>
              </div>
            ) : (
              <button 
                className="btn btn-xs btn-primary"
                onClick={startRecording}
                disabled={characters.filter(char => char.isActive).length < 2}
              >
                <Video size={8} />
                録画開始
              </button>
            )}
          </div>

          {/* 対談プレビュー - VRMキャラクター表示 */}
          <div className="bg-base-300/50 rounded-lg aspect-video relative border-2 border-dashed border-base-content/20 overflow-hidden">
            {currentSession ? (
              <div className="w-full h-full relative">
                {/* 録画ステータス */}
                {isRecording && (
                  <div className="absolute top-2 right-2 z-10 flex items-center gap-1 bg-error/90 rounded px-2 py-1">
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    <span className="text-xs font-mono text-white">{formatTime(recordingTime)}</span>
                  </div>
                )}
                
                {/* VRMキャラクター表示エリア */}
                <div className="grid grid-cols-2 gap-2 h-full p-2">
                  {characters.filter(char => char.isActive).slice(0, 2).map((character, index) => (
                    <div key={character.id} className="relative bg-base-200/30 rounded-lg overflow-hidden">
                      {/* キャラクター名表示 */}
                      <div className="absolute bottom-2 left-2 z-10 bg-base-200/80 rounded px-2 py-1">
                        <div className="text-xs font-medium">{character.name}</div>
                        <div className={`text-xs ${character.type === 'AI' ? 'text-secondary' : 'text-accent'}`}>
                          {character.type === 'AI' ? 'AITuber' : 'VTuber'}
                        </div>
                      </div>
                      
                      {/* VRMビューアー */}
                      <VRMViewer
                        vrmUrl={character.vrmUrl}
                        agentId={character.id}
                        isActive={character.isActive && !character.isMuted}
                        className="w-full h-full"
                        faceTrackingData={
                          character.id === selectedTrackingCharacter && character.faceTrackingEnabled
                            ? faceTrackingData
                            : null
                        }
                        onVRMUpload={(file) => {
                          // VRMアップロード処理
                          const url = URL.createObjectURL(file);
                          setCharacters(prev => prev.map(c => 
                            c.id === character.id 
                              ? { ...c, vrmUrl: url }
                              : c
                          ));
                          console.log(`VRMモデルをアップロード: ${file.name}`);
                        }}
                      />
                      
                      {/* マイクミュート表示 */}
                      {character.isMuted && (
                        <div className="absolute top-2 right-2 bg-error/80 rounded p-1">
                          <VolumeX size={12} className="text-white" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
                
                {/* 対談情報 */}
                <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-base-200/90 rounded px-3 py-1">
                  <div className="text-xs font-medium text-center">
                    🎬 {currentSession.participants.join(' × ')} 対談中
                  </div>
                </div>
              </div>
            ) : (
              <div className="w-full h-full flex flex-col items-center justify-center text-base-content/60">
                <Camera size={32} className="mb-3 opacity-50" />
                <div className="text-sm font-medium">VTUBER × AITUBER 対談スタジオ</div>
                <div className="text-xs mt-1">録画を開始して対談を始めましょう</div>
                <div className="text-xs mt-2 text-base-content/40">
                  💡 キャラクターにVRMモデルを設定すると3D表示されます
                </div>
              </div>
            )}
          </div>

          {/* 録画履歴 */}
          {sessions.length > 0 && (
            <div className="space-y-1">
              <div className="text-xs font-medium">録画履歴</div>
              <div className="max-h-20 overflow-y-auto space-y-1">
                {sessions.slice(0, 3).map((session) => (
                  <div key={session.id} className="bg-base-100 rounded p-2">
                    <div className="flex items-center justify-between">
                      <div className="text-xs">
                        <div className="font-medium truncate">{session.title}</div>
                        <div className="text-base-content/60">
                          {formatTime(session.duration)} • {session.participants.length}人
                        </div>
                      </div>
                      <div className="flex gap-1">
                        <button className="btn btn-ghost btn-xs">
                          <Play size={8} />
                        </button>
                        <button className="btn btn-ghost btn-xs">
                          <Settings size={8} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}