'use client';

import { useState, useEffect } from 'react';
import { Save, Eye, EyeOff, Plus, Trash2, Edit, Code2, FileText } from 'lucide-react';

interface YamlCell {
  id: string;
  key: string;
  value: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  level: number;
  isExpanded?: boolean;
}

interface YamlEditorProps {
  initialContent?: string;
  onSave?: (content: string) => void;
  readOnly?: boolean;
}

export default function YamlEditor({ initialContent = '', onSave, readOnly = false }: YamlEditorProps) {
  const [cells, setCells] = useState<YamlCell[]>([]);
  const [rawMode, setRawMode] = useState(false);
  const [rawContent, setRawContent] = useState(initialContent);
  const [previewMode, setPreviewMode] = useState(false);

  // YAML文字列を構造化データに変換
  const parseYamlToCells = (yamlString: string): YamlCell[] => {
    const lines = yamlString.split('\n').filter(line => line.trim());
    const cells: YamlCell[] = [];
    
    lines.forEach((line, index) => {
      const indent = line.match(/^(\s*)/)?.[1]?.length || 0;
      const level = Math.floor(indent / 2);
      const cleanLine = line.trim();
      
      if (cleanLine.includes(':')) {
        const [key, ...valueParts] = cleanLine.split(':');
        const value = valueParts.join(':').trim();
        
        let type: YamlCell['type'] = 'string';
        if (value === 'true' || value === 'false') type = 'boolean';
        else if (!isNaN(Number(value)) && value !== '') type = 'number';
        else if (value.startsWith('[') && value.endsWith(']')) type = 'array';
        else if (value === '' || value.startsWith('\n')) type = 'object';
        
        cells.push({
          id: `cell-${index}`,
          key: key.trim(),
          value: value,
          type,
          level,
          isExpanded: type === 'object' ? true : undefined
        });
      }
    });
    
    return cells;
  };

  // 構造化データをYAML文字列に変換
  const cellsToYaml = (cells: YamlCell[]): string => {
    return cells.map(cell => {
      const indent = '  '.repeat(cell.level);
      if (cell.type === 'object' && !cell.value) {
        return `${indent}${cell.key}:`;
      }
      return `${indent}${cell.key}: ${cell.value}`;
    }).join('\n');
  };

  useEffect(() => {
    if (initialContent && !rawMode) {
      setCells(parseYamlToCells(initialContent));
    }
  }, [initialContent, rawMode]);

  const addCell = (afterIndex?: number) => {
    const newCell: YamlCell = {
      id: `cell-${Date.now()}`,
      key: 'new_key',
      value: '',
      type: 'string',
      level: afterIndex !== undefined ? cells[afterIndex]?.level || 0 : 0
    };
    
    if (afterIndex !== undefined) {
      const newCells = [...cells];
      newCells.splice(afterIndex + 1, 0, newCell);
      setCells(newCells);
    } else {
      setCells([...cells, newCell]);
    }
  };

  const updateCell = (id: string, updates: Partial<YamlCell>) => {
    setCells(cells.map(cell => 
      cell.id === id ? { ...cell, ...updates } : cell
    ));
  };

  const deleteCell = (id: string) => {
    setCells(cells.filter(cell => cell.id !== id));
  };

  const handleSave = () => {
    const content = rawMode ? rawContent : cellsToYaml(cells);
    onSave?.(content);
  };

  const toggleMode = () => {
    if (rawMode) {
      // Raw -> Structured
      setCells(parseYamlToCells(rawContent));
    } else {
      // Structured -> Raw
      setRawContent(cellsToYaml(cells));
    }
    setRawMode(!rawMode);
  };

  const getTypeIcon = (type: YamlCell['type']) => {
    switch (type) {
      case 'string': return '📝';
      case 'number': return '🔢';
      case 'boolean': return '✅';
      case 'array': return '📋';
      case 'object': return '📁';
      default: return '📄';
    }
  };

  const getTypeColor = (type: YamlCell['type']) => {
    switch (type) {
      case 'string': return 'badge-info';
      case 'number': return 'badge-warning';
      case 'boolean': return 'badge-success';
      case 'array': return 'badge-primary';
      case 'object': return 'badge-secondary';
      default: return 'badge-neutral';
    }
  };

  return (
    <div className="h-full flex flex-col bg-base-100">
      {/* ヘッダー */}
      <div className="p-4 border-b border-base-content/10 bg-base-200/50">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-bold">📄 YAML エディタ</h2>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setPreviewMode(!previewMode)}
              className={`btn btn-sm ${previewMode ? 'btn-primary' : 'btn-outline'}`}
            >
              {previewMode ? <EyeOff size={16} /> : <Eye size={16} />}
              プレビュー
            </button>
            <button
              onClick={toggleMode}
              className={`btn btn-sm ${rawMode ? 'btn-warning' : 'btn-outline'}`}
            >
              {rawMode ? <FileText size={16} /> : <Code2 size={16} />}
              {rawMode ? '構造化' : 'Raw'}
            </button>
            {!readOnly && (
              <button
                onClick={handleSave}
                className="btn btn-sm btn-success"
              >
                <Save size={16} />
                保存
              </button>
            )}
          </div>
        </div>
        
        <div className="text-sm text-base-content/70">
          {rawMode ? 'Raw YAML編集モード' : 'セル構造編集モード'} • 
          {cells.length > 0 ? `${cells.length}個のキー` : 'データなし'}
        </div>
      </div>

      {/* コンテンツエリア */}
      <div className="flex-1 overflow-hidden">
        {previewMode ? (
          /* プレビューモード */
          <div className="h-full p-4 overflow-y-auto">
            <div className="bg-base-300/30 rounded-lg p-4">
              <h3 className="text-sm font-semibold mb-2">📋 プレビュー</h3>
              <pre className="text-sm whitespace-pre-wrap font-mono">
                {rawMode ? rawContent : cellsToYaml(cells)}
              </pre>
            </div>
          </div>
        ) : rawMode ? (
          /* Raw編集モード */
          <div className="h-full p-4">
            <textarea
              value={rawContent}
              onChange={(e) => setRawContent(e.target.value)}
              className="w-full h-full resize-none bg-base-100 border border-base-content/20 rounded-lg p-4 font-mono text-sm"
              placeholder="YAML内容を直接入力..."
              readOnly={readOnly}
            />
          </div>
        ) : (
          /* 構造化編集モード */
          <div className="h-full overflow-y-auto">
            <div className="p-4 space-y-2">
              {cells.length === 0 ? (
                <div className="text-center py-8 text-base-content/50">
                  <FileText size={48} className="mx-auto mb-2 opacity-50" />
                  <p>YAMLセルがありません</p>
                  <button
                    onClick={() => addCell()}
                    className="btn btn-sm btn-primary mt-2"
                    disabled={readOnly}
                  >
                    <Plus size={14} />
                    最初のセルを追加
                  </button>
                </div>
              ) : (
                <>
                  {cells.map((cell, index) => (
                    <div
                      key={cell.id}
                      className="bg-base-200/50 rounded-lg p-3 border border-base-content/10"
                      style={{ marginLeft: `${cell.level * 20}px` }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <div className={`badge ${getTypeColor(cell.type)} badge-sm`}>
                          {getTypeIcon(cell.type)} {cell.type}
                        </div>
                        {!readOnly && (
                          <div className="flex gap-1 ml-auto">
                            <button
                              onClick={() => addCell(index)}
                              className="btn btn-xs btn-ghost"
                              title="下にセル追加"
                            >
                              <Plus size={10} />
                            </button>
                            <button
                              onClick={() => deleteCell(cell.id)}
                              className="btn btn-xs btn-ghost text-error"
                              title="セル削除"
                            >
                              <Trash2 size={10} />
                            </button>
                          </div>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex gap-2">
                          <div className="flex-1">
                            <label className="text-xs text-base-content/70">キー</label>
                            <input
                              type="text"
                              value={cell.key}
                              onChange={(e) => updateCell(cell.id, { key: e.target.value })}
                              className="input input-sm w-full bg-base-100"
                              readOnly={readOnly}
                            />
                          </div>
                          <div className="w-24">
                            <label className="text-xs text-base-content/70">タイプ</label>
                            <select
                              value={cell.type}
                              onChange={(e) => updateCell(cell.id, { type: e.target.value as YamlCell['type'] })}
                              className="select select-sm w-full bg-base-100"
                              disabled={readOnly}
                            >
                              <option value="string">String</option>
                              <option value="number">Number</option>
                              <option value="boolean">Boolean</option>
                              <option value="array">Array</option>
                              <option value="object">Object</option>
                            </select>
                          </div>
                        </div>
                        
                        {cell.type !== 'object' && (
                          <div>
                            <label className="text-xs text-base-content/70">値</label>
                            {cell.type === 'boolean' ? (
                              <select
                                value={cell.value}
                                onChange={(e) => updateCell(cell.id, { value: e.target.value })}
                                className="select select-sm w-full bg-base-100"
                                disabled={readOnly}
                              >
                                <option value="true">true</option>
                                <option value="false">false</option>
                              </select>
                            ) : cell.type === 'array' ? (
                              <textarea
                                value={cell.value}
                                onChange={(e) => updateCell(cell.id, { value: e.target.value })}
                                className="textarea textarea-sm w-full bg-base-100"
                                placeholder="[item1, item2, item3]"
                                rows={2}
                                readOnly={readOnly}
                              />
                            ) : (
                              <input
                                type={cell.type === 'number' ? 'number' : 'text'}
                                value={cell.value}
                                onChange={(e) => updateCell(cell.id, { value: e.target.value })}
                                className="input input-sm w-full bg-base-100"
                                readOnly={readOnly}
                              />
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {!readOnly && (
                    <button
                      onClick={() => addCell()}
                      className="w-full p-3 border-2 border-dashed border-base-content/20 rounded-lg hover:border-primary/50 hover:bg-primary/5 transition-all text-base-content/50 hover:text-primary"
                    >
                      <Plus size={16} className="mx-auto mb-1" />
                      <div className="text-sm">新しいセルを追加</div>
                    </button>
                  )}
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}