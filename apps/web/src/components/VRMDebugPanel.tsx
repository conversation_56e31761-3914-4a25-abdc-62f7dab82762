'use client';

import { useEffect, useState } from 'react';

interface DebugLog {
  timestamp: string;
  level: string;
  message: string;
}

interface VRMDebugPanelProps {
  isVisible?: boolean;
  onToggle?: () => void;
}

export default function VRMDebugPanel({ isVisible = false, onToggle }: VRMDebugPanelProps) {
  const [logs, setLogs] = useState<DebugLog[]>([]);

  useEffect(() => {
    // コンソールログをインターセプト
    const originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info
    };

    ['log', 'warn', 'error', 'info'].forEach(method => {
      (console as any)[method] = (...args: any[]) => {
        const message = args.map(arg => {
          if (typeof arg === 'object' && arg !== null) {
            try {
              // circular参照をチェックして安全にJSONify
              return JSON.stringify(arg, (key, value) => {
                if (typeof value === 'object' && value !== null) {
                  // circular参照の可能性があるプロパティをスキップ
                  if (key === 'parser' || key === 'plugins' || key === 'parent' || key === 'scene') {
                    return '[Circular]';
                  }
                }
                return value;
              }, 2);
            } catch (error) {
              // JSON.stringify失敗時は文字列変換
              return `[Object: ${arg.constructor?.name || 'Unknown'}]`;
            }
          }
          return String(arg);
        }).join(' ');
        
        // VRM関連のログのみキャプチャ（エラーレベルのみ重要なもの）
        if ((method === 'error' && message.includes('VRM')) ||
            (method === 'warn' && message.includes('VRM')) ||
            (method === 'log' && (message.includes('✅') || message.includes('📦') || message.includes('🎯')))) {

          // 無限ループ防止のため、重複チェックを追加
          const newLog = {
            timestamp: new Date().toLocaleTimeString(),
            level: method,
            message
          };

          setLogs(prev => {
            // 重複ログを防止（同じメッセージが1秒以内に複数回来た場合は無視）
            const isDuplicate = prev.some(log =>
              log.message === newLog.message &&
              log.level === newLog.level &&
              Math.abs(new Date(`1970-01-01 ${log.timestamp}`).getTime() - new Date(`1970-01-01 ${newLog.timestamp}`).getTime()) < 1000
            );

            if (isDuplicate) return prev;

            return [...prev, newLog].slice(-50); // 最新50件のみ保持
          });
        }
        
        return (originalConsole as any)[method].apply(console, args);
      };
    });

    // postMessage を監視
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'VRM_DEBUG') {
        setLogs(prev => [...prev, {
          timestamp: new Date().toLocaleTimeString(),
          level: 'debug',
          message: `VRM Debug: ${JSON.stringify(event.data.data, null, 2)}`
        }].slice(-50));
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      // クリーンアップ
      Object.keys(originalConsole).forEach(method => {
        (console as any)[method] = (originalConsole as any)[method];
      });
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  // ログをテキスト形式でコピー
  const copyLogsToClipboard = () => {
    const logText = logs.map(log => 
      `[${log.timestamp}] [${log.level.toUpperCase()}] ${log.message}`
    ).join('\n');
    
    navigator.clipboard.writeText(logText).then(() => {
      console.log('✅ デバッグログをクリップボードにコピーしました');
    }).catch(() => {
      console.error('❌ クリップボードへのコピーに失敗しました');
    });
  };

  if (!isVisible) {
    return null; // MetaStudioLayoutで管理
  }

  return (
    <div className="fixed bottom-16 left-80 z-40 w-96 h-80 bg-base-100 border border-base-300 rounded-lg shadow-xl">
      <div className="p-2 border-b border-base-300 flex justify-between items-center">
        <h3 className="font-bold text-sm">🐛 VRM Debug Logs</h3>
        <div className="flex gap-1">
          <button
            className="btn btn-xs btn-ghost tooltip tooltip-top"
            data-tip="ログをコピー"
            onClick={copyLogsToClipboard}
          >
            📋
          </button>
          <button
            className="btn btn-xs btn-ghost tooltip tooltip-top"
            data-tip="ログをクリア"
            onClick={() => setLogs([])}
          >
            🗑️
          </button>
          <button
            className="btn btn-xs btn-ghost tooltip tooltip-top"
            data-tip="パネルを閉じる"
            onClick={onToggle}
          >
            ✕
          </button>
        </div>
      </div>
      
      <div className="p-2 h-64 overflow-y-auto text-xs font-mono">
        {logs.length === 0 ? (
          <div className="text-base-content/50">Waiting for VRM logs...</div>
        ) : (
          logs.map((log, index) => (
            <div 
              key={index} 
              className={`mb-1 p-1 rounded ${
                log.level === 'error' ? 'bg-error/10 text-error' :
                log.level === 'warn' ? 'bg-warning/10 text-warning' :
                log.level === 'debug' ? 'bg-info/10 text-info' :
                'bg-base-200'
              }`}
            >
              <div className="text-xs opacity-50">[{log.timestamp}] {log.level}</div>
              <div className="whitespace-pre-wrap break-words">{log.message}</div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}