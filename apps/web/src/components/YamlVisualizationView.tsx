'use client';

import { useState, useEffect } from 'react';
import { FileText, ChevronDown, ChevronRight, Eye, Code, Table } from 'lucide-react';

interface YamlNode {
  key: string;
  value: any;
  type: 'object' | 'array' | 'string' | 'number' | 'boolean' | 'null';
  path: string;
  children?: YamlNode[];
  isExpanded?: boolean;
}

interface YamlVisualizationViewProps {
  yamlContent?: string;
  filePath?: string;
}

export default function YamlVisualizationView({ 
  yamlContent, 
  filePath 
}: YamlVisualizationViewProps) {
  const [parsedData, setParsedData] = useState<YamlNode | null>(null);
  const [viewMode, setViewMode] = useState<'tree' | 'table' | 'raw'>('tree');
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // 複雑なYAML構造のダミーデータ（yaml2tableテスト用）
  const sampleYaml = `# Meta Studio プロジェクト設定ファイル
meta_studio:
  project:
    name: "MetaStudio Platform"
    version: "2.1.3"
    type: "full-stack-platform"
    status: "production"
    repository:
      url: "https://github.com/meta-studio/platform"
      branch: "main"
      commit: "a1b2c3d4"
    
  environment:
    development:
      database:
        host: "localhost"
        port: 5432
        name: "metastudio_dev"
        credentials:
          username: "dev_user"
          password: "dev_pass"
          ssl: false
      api:
        base_url: "http://localhost:8080"
        timeout: 30000
        retry_attempts: 3
      cache:
        redis:
          host: "127.0.0.1"
          port: 6379
          db: 0
    
    production:
      database:
        host: "prod-db.example.com"
        port: 5432
        name: "metastudio_prod"
        credentials:
          username: "prod_user"
          password: "ENCRYPTED"
          ssl: true
      api:
        base_url: "https://api.metastudio.com"
        timeout: 15000
        retry_attempts: 5
      cache:
        redis:
          host: "redis.example.com"
          port: 6379
          db: 1

agents:
  hierarchy:
    god:
      name: "グランドデザイナー"
      level: 0
      permissions: ["*"]
      capabilities:
        - "strategic_planning"
        - "resource_allocation"
        - "vision_setting"
      models:
        primary: "claude-3-opus"
        fallback: "gpt-4"
        settings:
          temperature: 0.7
          max_tokens: 4096
          top_p: 0.9
    
    king:
      name: "プロジェクトマネージャー"
      level: 1
      permissions: ["project:*", "team:*"]
      capabilities:
        - "project_management"
        - "team_coordination"
        - "quality_assurance"
      models:
        primary: "claude-3-sonnet"
        fallback: "gpt-3.5-turbo"
        settings:
          temperature: 0.5
          max_tokens: 2048
    
    general:
      name: "開発リーダー"
      level: 2
      permissions: ["code:*", "review:*"]
      capabilities:
        - "code_generation"
        - "code_review"
        - "architecture_design"
      models:
        primary: "claude-3-haiku"
        settings:
          temperature: 0.3
          max_tokens: 1024
    
    soldier:
      name: "実装エージェント"
      level: 3
      permissions: ["task:execute"]
      capabilities:
        - "task_execution"
        - "testing"
        - "documentation"

development:
  stack:
    frontend:
      framework: "Next.js"
      version: "15.0.0"
      language: "TypeScript"
      ui_library: "DaisyUI"
      state_management: "React Context"
      bundler: "Turbopack"
    
    backend:
      runtime: "Node.js"
      version: "22.0.0"
      framework: "Express"
      database: "PostgreSQL"
      orm: "Prisma"
      cache: "Redis"
    
    tools:
      package_manager: "bun"
      monorepo: "Turborepo"
      testing: "Vitest"
      e2e: "Playwright"
      linting: "ESLint"
      formatting: "Prettier"
      ci_cd: "GitHub Actions"

features:
  core_systems:
    tab_management:
      max_tabs: 18
      split_panes: true
      drag_drop: true
      persistence: true
      status: "implemented"
    
    file_management:
      explorer: true
      yaml_viewer: true
      markdown_editor: true
      real_time_editing: true
      status: "implemented"
    
    vrm_integration:
      model_upload: true
      animation: true
      lip_sync: true
      expressions: true
      status: "beta"
    
    ai_chat:
      models: ["claude-3", "gpt-4"]
      context_memory: true
      multi_character: true
      voice_input: true
      status: "development"

  advanced_features:
    browser_automation:
      engine: "playwright"
      visual_control: true
      llm_integration: true
      status: "planned"
    
    voice_streaming:
      real_time: true
      tiktok_integration: true
      obs_integration: true
      status: "planned"

infrastructure:
  deployment:
    platform: "Vercel"
    domains:
      - "metastudio.com"
      - "api.metastudio.com"
    ssl: true
    cdn: "Cloudflare"
  
  monitoring:
    analytics: "Google Analytics"
    error_tracking: "Sentry"
    performance: "Web Vitals"
    uptime: "Pingdom"
  
  security:
    authentication: "NextAuth.js"
    authorization: "RBAC"
    encryption: "AES-256"
    rate_limiting: true

budget:
  development:
    salaries: 2400000
    infrastructure: 150000
    tools: 80000
    total: 2630000
  
  marketing:
    advertising: 300000
    content_creation: 120000
    influencer: 200000
    total: 620000
  
  operations:
    server_costs: 50000
    third_party_apis: 30000
    maintenance: 40000
    total: 120000
  
  grand_total: 3370000

metadata:
  created_at: "2024-01-15T10:30:00Z"
  updated_at: "2024-12-17T15:45:30Z"
  created_by: "god-agent"
  updated_by: "king-agent"
  schema_version: "2.1"
  validation:
    required_fields: ["project.name", "project.version"]
    optional_fields: ["budget.marketing"]
    constraints:
      version_format: "semantic"
      status_values: ["development", "beta", "production"]`;

  // 改良されたYAML解析関数
  const parseYaml = (content: string): YamlNode => {
    try {
      const lines = content.split('\n')
        .map((line, index) => ({ content: line, lineNumber: index + 1 }))
        .filter(line => line.content.trim() && !line.content.trim().startsWith('#'));
      
      const result = parseLines(lines, 0, '');
      return result.node;
    } catch (err) {
      console.error('YAML parsing error:', err);
      setError(`YAML解析エラー (行 ${(err as any).lineNumber || '不明'}): ${err}`);
      throw err;
    }
  };

  const parseLines = (lines: Array<{content: string, lineNumber: number}>, startIndex: number, basePath: string): { node: YamlNode, nextIndex: number } => {
    const rootNode: YamlNode = {
      key: 'root',
      value: {},
      type: 'object',
      path: basePath,
      children: [],
      isExpanded: true
    };

    let i = startIndex;
    const baseIndent = i < lines.length ? getIndentLevel(lines[i].content) : 0;

    while (i < lines.length) {
      const lineObj = lines[i];
      const line = lineObj.content;
      const currentIndent = getIndentLevel(line);
      
      // インデントが基準より少ない場合は親レベルに戻る
      if (i > startIndex && currentIndent < baseIndent) {
        break;
      }

      // 同じレベルのアイテムのみ処理
      if (currentIndent !== baseIndent && i > startIndex) {
        i++;
        continue;
      }

      const trimmedLine = line.trim();
      
      try {
        if (trimmedLine.includes(':')) {
          const colonIndex = trimmedLine.indexOf(':');
          const key = trimmedLine.substring(0, colonIndex).trim();
          const value = trimmedLine.substring(colonIndex + 1).trim();
          const nodePath = basePath ? `${basePath}.${key}` : key;

          if (value === '' || value === '{}' || value === '[]') {
            // 子要素を持つオブジェクト
            const childResult = parseLines(lines, i + 1, nodePath);
            const childNode: YamlNode = {
              key: key,
              value: childResult.node.children || [],
              type: 'object',
              path: nodePath,
              children: childResult.node.children,
              isExpanded: false
            };
            rootNode.children!.push(childNode);
            i = childResult.nextIndex;
          } else {
            // プリミティブ値
            const nodeType = getValueType(value);
            const childNode: YamlNode = {
              key: key,
              value: parseValue(value, nodeType),
              type: nodeType,
              path: nodePath
            };
            rootNode.children!.push(childNode);
            i++;
          }
        } else if (trimmedLine.startsWith('-')) {
          // 配列アイテム
          const value = trimmedLine.substring(1).trim();
          const nodeType = getValueType(value);
          const childNode: YamlNode = {
            key: `[${rootNode.children!.length}]`,
            value: parseValue(value, nodeType),
            type: nodeType,
            path: `${basePath}[${rootNode.children!.length}]`
          };
          rootNode.children!.push(childNode);
          i++;
        } else {
          i++;
        }
      } catch (parseError) {
        console.warn(`Error parsing line ${lineObj.lineNumber}: ${line}`, parseError);
        i++;
      }
    }

    return { node: rootNode, nextIndex: i };
  };

  // インデントレベルを正確に計算
  const getIndentLevel = (line: string): number => {
    let indent = 0;
    for (const char of line) {
      if (char === ' ') indent++;
      else if (char === '\t') indent += 2; // タブは2スペース相当
      else break;
    }
    return indent;
  };

  const getValueType = (value: string): YamlNode['type'] => {
    if (value === 'null' || value === '') return 'null';
    if (value === 'true' || value === 'false') return 'boolean';
    if (/^-?\d+$/.test(value)) return 'number';
    if (/^-?\d+\.\d+$/.test(value)) return 'number';
    if (value.startsWith('"') && value.endsWith('"')) return 'string';
    if (value.startsWith('[') && value.endsWith(']')) return 'array';
    return 'string';
  };

  const parseValue = (value: string, type: YamlNode['type']): any => {
    switch (type) {
      case 'boolean': return value === 'true';
      case 'number': return parseFloat(value);
      case 'string': return value.replace(/^["']|["']$/g, '');
      case 'null': return null;
      default: return value;
    }
  };

  useEffect(() => {
    try {
      setError(null);
      const content = yamlContent || sampleYaml;
      
      if (!content.trim()) {
        setError('YAMLコンテンツが空です');
        return;
      }
      
      const parsed = parseYaml(content);
      setParsedData(parsed);
      console.log('YAML解析完了:', parsed);
      console.log('解析された子要素数:', parsed.children?.length || 0);
    } catch (err) {
      console.error('YAML parsing failed:', err);
      // 既にsetError()はparseYaml内で呼ばれている
    }
  }, [yamlContent]);

  const toggleExpanded = (path: string) => {
    const updateNode = (node: YamlNode): YamlNode => {
      if (node.path === path) {
        return { ...node, isExpanded: !node.isExpanded };
      }
      if (node.children) {
        return {
          ...node,
          children: node.children.map(updateNode)
        };
      }
      return node;
    };

    if (parsedData) {
      setParsedData(updateNode(parsedData));
    }
  };

  const renderTreeNode = (node: YamlNode, depth: number = 0): React.ReactNode => {
    const hasChildren = node.children && node.children.length > 0;
    const isExpanded = node.isExpanded;
    const indentStyle = { paddingLeft: `${depth * 20}px` };

    const getTypeColor = (type: YamlNode['type']) => {
      switch (type) {
        case 'object': return 'text-primary';
        case 'array': return 'text-secondary';
        case 'string': return 'text-success';
        case 'number': return 'text-warning';
        case 'boolean': return 'text-info';
        case 'null': return 'text-base-content/50';
        default: return 'text-base-content';
      }
    };

    const formatValue = (value: any, type: YamlNode['type']) => {
      if (type === 'string') return `"${value}"`;
      if (type === 'null') return 'null';
      return String(value);
    };

    return (
      <div key={node.path} className="select-none">
        <div 
          className="flex items-center gap-2 py-1 px-2 hover:bg-base-200 rounded cursor-pointer"
          style={indentStyle}
          onClick={() => hasChildren && toggleExpanded(node.path)}
        >
          {hasChildren ? (
            isExpanded ? (
              <ChevronDown size={16} className="text-base-content/50" />
            ) : (
              <ChevronRight size={16} className="text-base-content/50" />
            )
          ) : (
            <div className="w-4" />
          )}
          
          <span className="font-mono font-semibold text-accent">
            {node.key}
          </span>
          
          {!hasChildren && (
            <>
              <span className="text-base-content/50">:</span>
              <span className={`font-mono ${getTypeColor(node.type)}`}>
                {formatValue(node.value, node.type)}
              </span>
              <span className="text-xs text-base-content/30 ml-auto">
                {node.type}
              </span>
            </>
          )}
          
          {hasChildren && (
            <span className="text-xs text-base-content/30 ml-auto">
              {node.children?.length} items
            </span>
          )}
        </div>
        
        {hasChildren && isExpanded && (
          <div>
            {node.children?.map(child => renderTreeNode(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  // 編集可能なテーブルビュー（yaml2table風改善）
  const [editingCell, setEditingCell] = useState<{ path: string, field: string } | null>(null);
  const [editValue, setEditValue] = useState('');

  // セル編集ハンドラー（コンポーネントレベル）
  const handleCellEdit = (path: string, field: string, currentValue: any) => {
    setEditingCell({ path, field });
    setEditValue(String(currentValue));
  };

  const handleCellSave = () => {
    console.log('Saving cell:', editingCell, 'with value:', editValue);
    setEditingCell(null);
    setEditValue('');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') handleCellSave();
    if (e.key === 'Escape') {
      setEditingCell(null);
      setEditValue('');
    }
  };

  // 型別カラー・バッジ関数
  const getValueColor = (type: YamlNode['type']) => {
    switch (type) {
      case 'string': return 'text-success';
      case 'number': return 'text-warning';
      case 'boolean': return 'text-info';
      case 'null': return 'text-base-content/50';
      default: return 'text-base-content';
    }
  };

  const getTypeBadgeColor = (type: YamlNode['type']) => {
    switch (type) {
      case 'object': return 'badge-primary';
      case 'array': return 'badge-secondary';
      case 'string': return 'badge-success';
      case 'number': return 'badge-warning';
      case 'boolean': return 'badge-info';
      default: return 'badge-neutral';
    }
  };

  const formatValue = (value: any, type: YamlNode['type']) => {
    if (type === 'string') return `"${value}"`;
    if (type === 'null') return 'null';
    if (Array.isArray(value)) return `[${value.join(', ')}]`;
    return String(value);
  };

  // yaml2table風マスターピース実装：完璧なカテゴリ別グループ化テーブル
  const renderTableView = () => {
    if (!parsedData) return null;

    const renderArrayData = (items: any[], path: string, title: string) => {
      return (
        <div className="bg-gradient-to-br from-secondary/10 to-secondary/5 rounded-xl p-4 border border-secondary/20">
          <h4 className="font-semibold text-secondary mb-3 flex items-center gap-2">
            <span className="text-lg">📋</span>
            {title}
            <span className="badge badge-secondary badge-sm">{items.length} items</span>
          </h4>
          <div className="grid gap-2">
            {items.map((item, index) => (
              <div key={`${path}[${index}]`} className="bg-base-100/80 rounded-lg p-3 border border-secondary/10">
                <div className="flex items-center justify-between">
                  <span className="font-mono text-sm text-success">\"{ item}\"</span>
                  <span className="badge badge-xs badge-secondary">string</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    };

    const renderKeyValuePairs = (children: YamlNode[], categoryTitle: string, categoryIcon: string, categoryColor: string) => {
      const keyValuePairs = children.filter(child => !child.children || child.children.length === 0);
      const arrayData = children.filter(child => Array.isArray(child.value));
      const nestedObjects = children.filter(child => child.children && child.children.length > 0 && !Array.isArray(child.value));
      
      // 動的クラス名の問題を修正（Tailwind CSS の制限）
      const bgGradientClass = categoryColor === 'primary' ? 'bg-gradient-to-br from-primary/15 to-primary/5' :
                              categoryColor === 'secondary' ? 'bg-gradient-to-br from-secondary/15 to-secondary/5' :
                              categoryColor === 'accent' ? 'bg-gradient-to-br from-accent/15 to-accent/5' :
                              categoryColor === 'info' ? 'bg-gradient-to-br from-info/15 to-info/5' :
                              categoryColor === 'success' ? 'bg-gradient-to-br from-success/15 to-success/5' :
                              categoryColor === 'warning' ? 'bg-gradient-to-br from-warning/15 to-warning/5' :
                              'bg-gradient-to-br from-neutral/15 to-neutral/5';
                              
      const borderClass = categoryColor === 'primary' ? 'border-primary/20' :
                          categoryColor === 'secondary' ? 'border-secondary/20' :
                          categoryColor === 'accent' ? 'border-accent/20' :
                          categoryColor === 'info' ? 'border-info/20' :
                          categoryColor === 'success' ? 'border-success/20' :
                          categoryColor === 'warning' ? 'border-warning/20' :
                          'border-neutral/20';
      
      return (
        <div className={`${bgGradientClass} rounded-2xl p-6 border-2 ${borderClass} shadow-lg`}>
          {/* カテゴリヘッダー */}
          <div className="mb-5">
            <h3 className={`text-2xl font-bold flex items-center gap-3 mb-2 ${
              categoryColor === 'primary' ? 'text-primary' :
              categoryColor === 'secondary' ? 'text-secondary' :
              categoryColor === 'accent' ? 'text-accent' :
              categoryColor === 'info' ? 'text-info' :
              categoryColor === 'success' ? 'text-success' :
              categoryColor === 'warning' ? 'text-warning' :
              'text-neutral'
            }`}>
              <span className="text-3xl">{categoryIcon}</span>
              {categoryTitle}
              <span className={`badge badge-lg gap-1 ${
                categoryColor === 'primary' ? 'badge-primary' :
                categoryColor === 'secondary' ? 'badge-secondary' :
                categoryColor === 'accent' ? 'badge-accent' :
                categoryColor === 'info' ? 'badge-info' :
                categoryColor === 'success' ? 'badge-success' :
                categoryColor === 'warning' ? 'badge-warning' :
                'badge-neutral'
              }`}>
                📊 {children.length} items
              </span>
            </h3>
            <div className={`h-1 rounded-full ${
              categoryColor === 'primary' ? 'bg-gradient-to-r from-primary to-primary/30' :
              categoryColor === 'secondary' ? 'bg-gradient-to-r from-secondary to-secondary/30' :
              categoryColor === 'accent' ? 'bg-gradient-to-r from-accent to-accent/30' :
              categoryColor === 'info' ? 'bg-gradient-to-r from-info to-info/30' :
              categoryColor === 'success' ? 'bg-gradient-to-r from-success to-success/30' :
              categoryColor === 'warning' ? 'bg-gradient-to-r from-warning to-warning/30' :
              'bg-gradient-to-r from-neutral to-neutral/30'
            }`}></div>
          </div>

          {/* キー:値ペア表示 */}
          {keyValuePairs.length > 0 && (
            <div className="mb-6">
              <h4 className="font-semibold text-base-content/80 mb-3 flex items-center gap-2">
                <span>🔧</span> 設定項目
              </h4>
              <div className="overflow-hidden rounded-xl border border-base-content/10">
                <table className="table table-zebra w-full">
                  <thead>
                    <tr className={`${
                      categoryColor === 'primary' ? 'bg-primary/10' :
                      categoryColor === 'secondary' ? 'bg-secondary/10' :
                      categoryColor === 'accent' ? 'bg-accent/10' :
                      categoryColor === 'info' ? 'bg-info/10' :
                      categoryColor === 'success' ? 'bg-success/10' :
                      categoryColor === 'warning' ? 'bg-warning/10' :
                      'bg-neutral/10'
                    }`}>
                      <th className="w-1/3 font-bold text-base-content">🔑 キー</th>
                      <th className="w-1/2 font-bold text-base-content">💾 値</th>
                      <th className="w-1/6 font-bold text-base-content">🏷️ 型</th>
                    </tr>
                  </thead>
                  <tbody>
                    {keyValuePairs.map((child) => (
                      <tr key={child.path} className="hover:bg-base-100/50 transition-colors">
                        <td className="font-mono font-semibold text-accent">
                          <span 
                            className={editingCell?.path === child.path && editingCell?.field === 'key' ? '' : 'cursor-pointer hover:bg-accent/20 px-2 py-1 rounded transition-colors'}
                            onClick={() => handleCellEdit(child.path, 'key', child.key)}
                            title="クリックして編集"
                          >
                            {editingCell?.path === child.path && editingCell?.field === 'key' ? (
                              <input
                                type="text"
                                value={editValue}
                                onChange={(e) => setEditValue(e.target.value)}
                                onBlur={handleCellSave}
                                onKeyDown={handleKeyDown}
                                className="input input-xs w-full bg-transparent border border-accent/50 rounded"
                                autoFocus
                              />
                            ) : (
                              child.key
                            )}
                          </span>
                        </td>
                        <td className="font-mono">
                          <span 
                            className={editingCell?.path === child.path && editingCell?.field === 'value' ? 'w-full' : 'cursor-pointer hover:bg-success/20 px-2 py-1 rounded transition-colors'}
                            onClick={() => handleCellEdit(child.path, 'value', child.value)}
                            title="クリックして編集"
                          >
                            {editingCell?.path === child.path && editingCell?.field === 'value' ? (
                              <input
                                type="text"
                                value={editValue}
                                onChange={(e) => setEditValue(e.target.value)}
                                onBlur={handleCellSave}
                                onKeyDown={handleKeyDown}
                                className="input input-xs w-full bg-transparent border border-success/50 rounded"
                                autoFocus
                              />
                            ) : (
                              <span className={getValueColor(child.type)}>
                                {formatValue(child.value, child.type)}
                              </span>
                            )}
                          </span>
                        </td>
                        <td>
                          <span className={`badge badge-sm ${getTypeBadgeColor(child.type)}`}>
                            {child.type}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* 配列データ表示 */}
          {arrayData.length > 0 && (
            <div className="mb-6">
              <h4 className="font-semibold text-base-content/80 mb-3 flex items-center gap-2">
                <span>📋</span> リスト項目
              </h4>
              <div className="grid gap-4">
                {arrayData.map((child, index) => (
                  <div key={child.path || `array-${index}`}>
                    {renderArrayData(child.value, child.path, child.key || `array-${index}`)}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* ネストしたオブジェクト（再帰表示） */}
          {nestedObjects.length > 0 && (
            <div>
              <h4 className="font-semibold text-base-content/80 mb-3 flex items-center gap-2">
                <span>🗂️</span> サブカテゴリ
              </h4>
              <div className="space-y-4">
                {nestedObjects.map((child, index) => (
                  <div key={child.path || `nested-${index}`}>
                    {renderGroupedSection(child, 1)}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      );
    };

    const renderGroupedSection = (node: YamlNode, depth: number = 0): React.ReactNode => {
      if (!node.children) return null;

      // カテゴリーごとのアイコンと色を定義
      const getCategoryStyle = (key: string) => {
        const lowerKey = key.toLowerCase();
        if (lowerKey.includes('meta') || lowerKey.includes('project')) return { icon: '🎯', color: 'primary' };
        if (lowerKey.includes('agent') || lowerKey.includes('hierarchy')) return { icon: '🤖', color: 'secondary' };
        if (lowerKey.includes('development') || lowerKey.includes('stack')) return { icon: '⚙️', color: 'accent' };
        if (lowerKey.includes('feature') || lowerKey.includes('system')) return { icon: '✨', color: 'info' };
        if (lowerKey.includes('budget') || lowerKey.includes('cost')) return { icon: '💰', color: 'warning' };
        if (lowerKey.includes('metadata') || lowerKey.includes('info')) return { icon: '📄', color: 'neutral' };
        if (lowerKey.includes('environment') || lowerKey.includes('config')) return { icon: '🌍', color: 'success' };
        return { icon: '📁', color: 'primary' };
      };

      const { icon, color } = getCategoryStyle(node.key);

      if (depth === 0) {
        // トップレベルカテゴリ：フルカード表示
        return renderKeyValuePairs(node.children, node.key, icon, color);
      } else {
        // サブカテゴリ：コンパクト表示
        return (
          <div key={node.path} className="bg-base-100/60 rounded-xl p-4 border border-base-content/10">
            <h5 className={`font-semibold mb-3 flex items-center gap-2 ${
              color === 'primary' ? 'text-primary' :
              color === 'secondary' ? 'text-secondary' :
              color === 'accent' ? 'text-accent' :
              color === 'info' ? 'text-info' :
              color === 'success' ? 'text-success' :
              color === 'warning' ? 'text-warning' :
              'text-neutral'
            }`}>
              <span>{icon}</span>
              {node.key}
              <span className={`badge badge-sm ${
                color === 'primary' ? 'badge-primary' :
                color === 'secondary' ? 'badge-secondary' :
                color === 'accent' ? 'badge-accent' :
                color === 'info' ? 'badge-info' :
                color === 'success' ? 'badge-success' :
                color === 'warning' ? 'badge-warning' :
                'badge-neutral'
              }`}>
                {node.children.length} items
              </span>
            </h5>
            <div className="overflow-hidden rounded-lg">
              <table className="table table-sm w-full">
                <tbody>
                  {node.children.map(child => (
                    <tr key={child.path} className="hover:bg-base-200/50">
                      <td className="w-1/3 font-mono font-semibold text-accent text-sm">
                        {child.key}
                      </td>
                      <td className="w-2/3 font-mono text-sm">
                        <div className="flex items-center justify-between">
                          <span className={getValueColor(child.type)}>
                            {formatValue(child.value, child.type)}
                          </span>
                          <span className={`badge badge-xs ${getTypeBadgeColor(child.type)}`}>
                            {child.type}
                          </span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );
      }
    };

    return (
      <div className="space-y-8 p-6">
        {parsedData.children && parsedData.children.map(child => (
          <div key={child.path}>
            {renderGroupedSection(child, 0)}
          </div>
        ))}
        
        {/* マスターピース・フッター */}
        <div className="mt-8 p-4 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl border border-primary/20">
          <div className="text-center">
            <h4 className="font-bold text-primary mb-2 flex items-center justify-center gap-2">
              <span className="text-xl">🏆</span>
              yaml2table マスターピース実装
              <span className="text-xl">🏆</span>
            </h4>
            <p className="text-sm text-base-content/70">
              完璧なカテゴリ別グループ化・編集可能セル・視覚的階層表示を実現
            </p>
          </div>
        </div>
      </div>
    );
  };

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center neo-glass">
        <div className="text-center">
          <div className="text-error text-xl mb-2">⚠️</div>
          <h3 className="text-lg font-semibold text-error mb-2">YAML解析エラー</h3>
          <p className="text-sm text-base-content/70">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col neo-glass">
      {/* ヘッダー（yaml2table風改善） */}
      <div className="p-4 border-b border-base-content/10 bg-gradient-to-r from-base-300/80 to-base-200/60">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <div className="bg-primary/20 p-2 rounded-lg">
                <FileText size={24} className="text-primary" />
              </div>
              <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                YAML構造ビューアー
              </span>
              <span className="text-xs bg-gradient-to-r from-success/30 to-info/30 text-success px-3 py-1 rounded-full font-bold shadow-md">
                🏆 yaml2table マスターピース
              </span>
            </h1>
            <p className="text-sm text-base-content/70 ml-14">
              📁 {filePath || 'サンプル設定ファイル'} - 構造化データの視覚化表示
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {/* 検索（改善版） */}
            <div className="form-control">
              <div className="input-group input-group-sm">
                <input
                  type="text"
                  placeholder="🔍 キー・値・パス検索..."
                  className="input input-sm input-bordered w-48"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                {searchTerm && (
                  <button 
                    className="btn btn-sm btn-square"
                    onClick={() => setSearchTerm('')}
                  >
                    ✕
                  </button>
                )}
              </div>
            </div>
            
            {/* ビューモード切り替え（改善版） */}
            <div className="btn-group">
              <button
                className={`btn btn-sm gap-1 ${viewMode === 'tree' ? 'btn-primary' : 'btn-outline'}`}
                onClick={() => setViewMode('tree')}
                title="ツリービュー - 階層構造を表示"
              >
                🌳 ツリー
              </button>
              <button
                className={`btn btn-sm gap-1 ${viewMode === 'table' ? 'btn-primary' : 'btn-outline'}`}
                onClick={() => setViewMode('table')}
                title="テーブルビュー - 編集可能なセル表示"
              >
                📊 テーブル
              </button>
              <button
                className={`btn btn-sm gap-1 ${viewMode === 'raw' ? 'btn-primary' : 'btn-outline'}`}
                onClick={() => setViewMode('raw')}
                title="RAWビュー - 元のYAMLテキスト"
              >
                📝 RAW
              </button>
            </div>
          </div>
        </div>
        
        {/* 統計情報バー */}
        <div className="mt-3 flex items-center justify-between">
          <div className="flex items-center gap-4 text-sm">
            <div className="badge badge-outline gap-1">
              📊 ノード数: {parsedData ? JSON.stringify(parsedData).match(/[{}]/g)?.length || 0 : 0}
            </div>
            <div className="badge badge-outline gap-1">
              📏 深度: {parsedData ? String(parsedData.path).split('.').length : 0}
            </div>
            {searchTerm && (
              <div className="badge badge-success gap-1">
                🔍 検索結果: {(() => {
                  const flatData = parsedData ? (() => {
                    const result: any[] = [];
                    const flatten = (node: YamlNode): void => {
                      if (node.children) {
                        node.children.forEach(child => flatten(child));
                      } else {
                        result.push(node);
                      }
                    };
                    flatten(parsedData);
                    return result;
                  })() : [];
                  return flatData.filter(item => 
                    item.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    item.path.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    String(item.value).toLowerCase().includes(searchTerm.toLowerCase())
                  ).length;
                })()}件
              </div>
            )}
          </div>
          <div className="text-xs text-base-content/50">
            {viewMode === 'table' ? '💡 セルをクリックして編集' : 
             viewMode === 'tree' ? '🌳 フォルダーをクリックして展開' : 
             '📝 元のYAMLファイル内容'}
          </div>
        </div>
      </div>

      {/* コンテンツ */}
      <div className="flex-1 overflow-y-auto p-4">
        {viewMode === 'tree' && parsedData && (
          <div className="bg-base-200 rounded-lg p-4 font-mono text-sm">
            {parsedData.children?.map(child => renderTreeNode(child))}
          </div>
        )}

        {viewMode === 'table' && (
          <div className="bg-base-200 rounded-lg p-4">
            {renderTableView()}
          </div>
        )}

        {viewMode === 'raw' && (
          <div className="bg-base-200 rounded-lg p-4">
            <pre className="font-mono text-sm whitespace-pre-wrap">
              {yamlContent || sampleYaml}
            </pre>
          </div>
        )}
      </div>

      {/* 統計情報 */}
      <div className="p-4 border-t border-base-content/10 bg-base-300/80">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-4">
            <span>総ノード数: {parsedData ? JSON.stringify(parsedData).match(/[{}]/g)?.length || 0 : 0}</span>
            <span>深度: {parsedData ? String(parsedData.path).split('.').length : 0}</span>
          </div>
          <div className="text-base-content/50">
            YAML2Table風 構造化ビューアー
          </div>
        </div>
      </div>
    </div>
  );
}