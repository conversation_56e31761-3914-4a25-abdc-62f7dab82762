'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { Mi<PERSON>, MicO<PERSON>, Volume2, VolumeX, Settings } from 'lucide-react';

interface VoiceProcessingSystemProps {
  onVolumeChange?: (normalizedVolume: number) => void;
  onTranscriptChange?: (transcript: string) => void;
  isEnabled?: boolean;
  className?: string;
}

interface AudioQueueItem {
  id: string;
  audioData: ArrayBuffer;
  text: string;
  timestamp: number;
}

export default function VoiceProcessingSystem({
  onVolumeChange,
  onTranscriptChange,
  isEnabled = true,
  className = ''
}: VoiceProcessingSystemProps) {
  // LocalAIVtuber参考: リアルタイム音声処理
  const [isListening, setIsListening] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentVolume, setCurrentVolume] = useState(0);
  const [audioQueue, setAudioQueue] = useState<AudioQueueItem[]>([]);
  const [processingQueue, setProcessingQueue] = useState<string[]>([]);
  
  // aituber-kit参考: 音声認識とTTS統合
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const audioQueueRef = useRef<AudioQueueItem[]>([]);
  const playbackThreadRef = useRef<boolean>(false);
  
  // LocalAIVtuber参考: 音量正規化
  const [maxRMS, setMaxRMS] = useState(0);
  const [normalizedVolume, setNormalizedVolume] = useState(0);
  
  // 音声処理設定
  const [settings, setSettings] = useState({
    chunkSize: 1024,
    sampleRate: 44100,
    channels: 1,
    enableNormalization: true,
    enableRealTimeProcessing: true,
    queueMaxSize: 10
  });

  // LocalAIVtuber参考: リアルタイム音量監視
  const monitorAudioLevel = useCallback(() => {
    if (!analyserRef.current || !isListening) return;

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
    analyserRef.current.getByteFrequencyData(dataArray);
    
    // RMS計算（LocalAIVtuber方式）
    let sum = 0;
    for (let i = 0; i < dataArray.length; i++) {
      sum += dataArray[i] * dataArray[i];
    }
    const rms = Math.sqrt(sum / dataArray.length);
    
    // 正規化（LocalAIVtuber参考）
    const normalized = settings.enableNormalization && maxRMS > 0 
      ? Math.min(rms / maxRMS, 1.0) 
      : rms / 255;
    
    setCurrentVolume(rms);
    setNormalizedVolume(normalized);
    
    // VRM口パク同期用
    onVolumeChange?.(normalized);
    
    if (isListening) {
      requestAnimationFrame(monitorAudioLevel);
    }
  }, [isListening, maxRMS, settings.enableNormalization, onVolumeChange]);

  // aituber-kit参考: 音声認識開始
  const startListening = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          sampleRate: settings.sampleRate,
          channelCount: settings.channels,
          echoCancellation: true,
          noiseSuppression: true
        } 
      });
      
      // AudioContext初期化
      audioContextRef.current = new AudioContext({ sampleRate: settings.sampleRate });
      const source = audioContextRef.current.createMediaStreamSource(stream);
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 2048;
      source.connect(analyserRef.current);
      
      // MediaRecorder設定
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      const audioChunks: Blob[] = [];
      
      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunks.push(event.data);
        }
      };
      
      mediaRecorderRef.current.onstop = async () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
        const arrayBuffer = await audioBlob.arrayBuffer();
        
        // 音声処理キューに追加（LocalAIVtuber方式）
        const queueItem: AudioQueueItem = {
          id: `audio-${Date.now()}`,
          audioData: arrayBuffer,
          text: '', // 音声認識結果で更新
          timestamp: Date.now()
        };
        
        addToAudioQueue(queueItem);
      };
      
      mediaRecorderRef.current.start(100); // 100ms間隔
      setIsListening(true);
      
      // リアルタイム音量監視開始
      monitorAudioLevel();
      
    } catch (error) {
      console.error('音声入力開始エラー:', error);
    }
  }, [settings, monitorAudioLevel]);

  // 音声認識停止
  const stopListening = useCallback(() => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }
    
    if (audioContextRef.current) {
      audioContextRef.current.close();
    }
    
    setIsListening(false);
    setCurrentVolume(0);
    setNormalizedVolume(0);
  }, []);

  // LocalAIVtuber参考: 音声キュー管理
  const addToAudioQueue = useCallback((item: AudioQueueItem) => {
    setAudioQueue(prev => {
      const newQueue = [...prev, item];
      // キューサイズ制限
      if (newQueue.length > settings.queueMaxSize) {
        return newQueue.slice(-settings.queueMaxSize);
      }
      return newQueue;
    });
    
    audioQueueRef.current.push(item);
    processAudioQueue();
  }, [settings.queueMaxSize]);

  // LocalAIVtuber参考: 音声キュー処理
  const processAudioQueue = useCallback(async () => {
    if (playbackThreadRef.current || audioQueueRef.current.length === 0) {
      return;
    }
    
    playbackThreadRef.current = true;
    setIsPlaying(true);
    
    while (audioQueueRef.current.length > 0) {
      const item = audioQueueRef.current.shift();
      if (!item) break;
      
      try {
        // 音声認識処理（Web Speech API使用）
        const transcript = await processAudioToText(item.audioData);
        
        // 結果をコールバックで通知
        onTranscriptChange?.(transcript);
        
        // キューから削除
        setAudioQueue(prev => prev.filter(q => q.id !== item.id));
        
      } catch (error) {
        console.error('音声処理エラー:', error);
      }
    }
    
    playbackThreadRef.current = false;
    setIsPlaying(false);
  }, [onTranscriptChange]);

  // 音声認識処理（簡易版）
  const processAudioToText = async (audioData: ArrayBuffer): Promise<string> => {
    // 実際の実装では、Web Speech APIやWhisper APIを使用
    return new Promise((resolve) => {
      // ダミー実装
      setTimeout(() => {
        resolve('音声認識結果のテキスト');
      }, 1000);
    });
  };

  // LocalAIVtuber参考: 最大RMS更新
  useEffect(() => {
    if (currentVolume > maxRMS) {
      setMaxRMS(currentVolume);
    }
  }, [currentVolume, maxRMS]);

  // クリーンアップ
  useEffect(() => {
    return () => {
      stopListening();
    };
  }, [stopListening]);

  return (
    <div className={`voice-processing-system ${className}`}>
      {/* メイン制御パネル */}
      <div className="flex items-center gap-3 p-3 bg-base-200/50 rounded-lg">
        {/* 音声入力ボタン */}
        <button
          className={`btn btn-circle ${isListening ? 'btn-error' : 'btn-primary'}`}
          onClick={isListening ? stopListening : startListening}
          disabled={!isEnabled}
          title={isListening ? '音声入力停止' : '音声入力開始'}
        >
          {isListening ? <MicOff size={20} /> : <Mic size={20} />}
        </button>
        
        {/* 音量表示 */}
        <div className="flex items-center gap-2 flex-1">
          <Volume2 size={16} className="text-base-content/60" />
          <div className="flex-1 h-2 bg-base-300 rounded-full overflow-hidden">
            <div 
              className="h-full bg-primary transition-all duration-100"
              style={{ width: `${normalizedVolume * 100}%` }}
            />
          </div>
          <span className="text-xs text-base-content/60 min-w-[3rem]">
            {Math.round(normalizedVolume * 100)}%
          </span>
        </div>
        
        {/* 処理状態 */}
        <div className="flex items-center gap-1">
          {isPlaying && (
            <div className="loading loading-spinner loading-xs"></div>
          )}
          <span className="text-xs text-base-content/60">
            {isListening ? '録音中' : isPlaying ? '処理中' : '待機中'}
          </span>
        </div>
      </div>
      
      {/* キュー状態表示 */}
      {(audioQueue.length > 0 || processingQueue.length > 0) && (
        <div className="mt-2 p-2 bg-base-100/50 rounded text-xs">
          <div className="flex justify-between">
            <span>音声キュー: {audioQueue.length}</span>
            <span>処理キュー: {processingQueue.length}</span>
          </div>
        </div>
      )}
      
      {/* デバッグ情報 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-2 p-2 bg-base-300/20 rounded text-xs space-y-1">
          <div>現在音量: {Math.round(currentVolume)}</div>
          <div>正規化音量: {Math.round(normalizedVolume * 100)}%</div>
          <div>最大RMS: {Math.round(maxRMS)}</div>
          <div>サンプルレート: {settings.sampleRate}Hz</div>
        </div>
      )}
    </div>
  );
}
