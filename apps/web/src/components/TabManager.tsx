'use client';

import { X, Pin, SplitSquareHorizontal } from 'lucide-react';
import { useState, useEffect } from 'react';
import React from 'react';

interface Tab {
  id: string;
  title: string;
  type: 'project' | 'launcher' | 'dashboard' | 'home' | 'system' | 'file';
  content?: string;
  isPinned?: boolean;
}

interface SplitPane {
  id: string;
  tabs: Tab[];
  activeTabId: string;
  size: number;
  direction?: 'horizontal' | 'vertical';
}

interface DragOverTabEvent {
  tabId: string;
  position: 'before' | 'after' | 'split-left' | 'split-right' | 'split-top' | 'split-bottom';
}

interface TabManagerProps {
  children: React.ReactNode;
  activeTab?: Tab;
  onTabChange?: (tab: Tab) => void;
}

export default function TabManager({ children, activeTab, onTabChange }: TabManagerProps) {
  const [tabs, setTabs] = useState<Tab[]>([
    { id: 'home', title: 'ダッシュボード', type: 'home', isPinned: true }
  ]);
  const [currentTabId, setCurrentTabId] = useState('home');
  const [splitPanes, setSplitPanes] = useState<SplitPane[]>([]);
  const [draggedTab, setDraggedTab] = useState<Tab | null>(null);
  const [dropTarget, setDropTarget] = useState<DragOverTabEvent | null>(null);
  const [isSplitMode, setIsSplitMode] = useState(false);
  const [paneResizing, setPaneResizing] = useState<{ paneId: string; startX: number; startY: number; startSize: number } | null>(null);
  const [paneDropZone, setPaneDropZone] = useState<{ position: 'left' | 'right' | 'top' | 'bottom' | null }>({ position: null });

  // セッション復元
  useEffect(() => {
    const savedTabs = localStorage.getItem('meta-studio-tabs');
    if (savedTabs) {
      try {
        const parsedTabs = JSON.parse(savedTabs);
        if (Array.isArray(parsedTabs) && parsedTabs.length > 0) {
          setTabs(parsedTabs);
        }
      } catch (error) {
        console.log('タブセッション復元に失敗:', error);
      }
    }
  }, []);

  useEffect(() => {
    if (!activeTab) {
      console.log('TabManager: useEffect - activeTab is null/undefined');
      return;
    }
    
    console.log('TabManager: useEffect triggered - activeTab:', activeTab);
    console.log('TabManager: useEffect - current tabs:', tabs.map(t => ({id: t.id, title: t.title, type: t.type})));
    console.log('TabManager: useEffect - currentTabId:', currentTabId);
    
    const existingTab = tabs.find(t => t.id === activeTab.id);
    
    if (!existingTab) {
      console.log('TabManager: Adding new tab:', activeTab);
      // タブが18個以上の場合、ピンされていない古いタブを削除
      let newTabs = [...tabs];
      
      if (newTabs.length >= 18) {
        console.log('TabManager: Too many tabs, removing unpinned ones');
        // ピンされていないタブを找して削除（複数削除）
        while (newTabs.length >= 18) {
          const unpinnedIndex = newTabs.findIndex(t => !t.isPinned && t.id !== 'home');
          if (unpinnedIndex !== -1) {
            console.log('TabManager: Removing unpinned tab:', newTabs[unpinnedIndex].title);
            newTabs.splice(unpinnedIndex, 1);
          } else {
            // ピンされていないタブがない場合は追加を諦める
            console.log('TabManager: No unpinned tabs to remove');
            break;
          }
        }
      }
      
      newTabs.push(activeTab);
      console.log('TabManager: Setting new tabs array:', newTabs.map(t => ({id: t.id, title: t.title, type: t.type})));
      setTabs(newTabs);
      console.log('TabManager: Setting currentTabId to:', activeTab.id);
      setCurrentTabId(activeTab.id);
      // ローカルストレージに保存
      localStorage.setItem('meta-studio-tabs', JSON.stringify(newTabs));
      console.log('TabManager: NEW TAB - Not calling onTabChange for new tab');
    } else {
      console.log('TabManager: Switching to existing tab:', activeTab.id);
      setCurrentTabId(activeTab.id);
      // 既存タブをクリックした時もコンテンツを更新
      console.log('TabManager: EXISTING TAB - Calling onTabChange');
      onTabChange?.(activeTab);
    }
  }, [activeTab?.id]); // activeTab.idだけを依存関係に

  const handleCloseTab = (tabId: string) => {
    if (tabId === 'home') return; // ホームタブは閉じられない
    
    const tab = tabs.find(t => t.id === tabId);
    if (tab?.isPinned) {
      // ピンされたタブは閉じられない - 視覚的フィードバック
      console.log('📌 ピンされたタブは閉じることができません');
      return; 
    }
    
    const newTabs = tabs.filter(t => t.id !== tabId);
    setTabs(newTabs);
    localStorage.setItem('meta-studio-tabs', JSON.stringify(newTabs));
    
    if (currentTabId === tabId) {
      const newActiveTab = newTabs[newTabs.length - 1];
      setCurrentTabId(newActiveTab.id);
      onTabChange?.(newActiveTab);
    }
  };

  const handleTabClick = (tab: Tab, paneId?: string) => {
    if (isSplitMode && paneId) {
      // 分割モードでペイン内のタブクリック
      setSplitPanes(prev => prev.map(pane => 
        pane.id === paneId ? { ...pane, activeTabId: tab.id } : pane
      ));
    } else {
      setCurrentTabId(tab.id);
    }
    onTabChange?.(tab);
  };

  const handlePinTab = (tabId: string) => {
    setTabs(prev => {
      const newTabs = prev.map(tab => 
        tab.id === tabId ? { ...tab, isPinned: !tab.isPinned } : tab
      );
      // ローカルストレージに保存（セッション記憶用）
      localStorage.setItem('meta-studio-tabs', JSON.stringify(newTabs));
      return newTabs;
    });
  };

  const handleCloseAllTabs = () => {
    const homeTab = tabs.find(t => t.id === 'home');
    const pinnedTabs = tabs.filter(t => t.isPinned || t.id === 'home');
    if (homeTab) {
      setTabs(pinnedTabs);
      setCurrentTabId('home');
      setSplitPanes([]);
      setIsSplitMode(false);
      onTabChange?.(homeTab);
    }
  };

  // ドラッグ&ドロップハンドラー
  const handleDragStart = (e: React.DragEvent, tab: Tab) => {
    setDraggedTab(tab);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent, targetTab: Tab) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    
    if (!draggedTab || draggedTab.id === targetTab.id) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    const width = rect.width;
    const height = rect.height;
    
    // より詳細なドロップゾーン判定
    let position: DragOverTabEvent['position'];
    
    // エリアを4分割して判定
    const centerThreshold = 0.3; // 中央30%の範囲
    const leftEdge = width * centerThreshold;
    const rightEdge = width * (1 - centerThreshold);
    const topEdge = height * centerThreshold;
    const bottomEdge = height * (1 - centerThreshold);
    
    if (x < leftEdge) {
      position = 'split-left';
    } else if (x > rightEdge) {
      position = 'split-right';
    } else if (y < topEdge) {
      position = 'split-top';
    } else if (y > bottomEdge) {
      position = 'split-bottom';
    } else {
      // 中央エリアの場合は、左右どちらに近いかで判定
      position = x < width / 2 ? 'before' : 'after';
    }
    
    setDropTarget({ tabId: targetTab.id, position });
  };

  const handleDrop = (e: React.DragEvent, targetTab?: Tab) => {
    e.preventDefault();
    if (!draggedTab || !dropTarget) return;
    
    const target = targetTab || tabs.find(t => t.id === dropTarget.tabId);
    if (!target) return;
    
    if (dropTarget.position.startsWith('split-')) {
      // 分割モード作成
      createSplitPane(draggedTab, target, dropTarget.position as 'split-left' | 'split-right' | 'split-top' | 'split-bottom');
    } else {
      // タブ並び替え
      reorderTabs(draggedTab, target, dropTarget.position as 'before' | 'after');
    }
    
    setDraggedTab(null);
    setDropTarget(null);
  };
  
  const createSplitPane = (sourceTab: Tab, targetTab: Tab, position: 'split-left' | 'split-right' | 'split-top' | 'split-bottom') => {
    const direction = position.includes('left') || position.includes('right') ? 'horizontal' : 'vertical';
    
    const pane1: SplitPane = {
      id: `pane-${Date.now()}-1`,
      tabs: position === 'split-right' || position === 'split-bottom' ? [targetTab] : [sourceTab],
      activeTabId: position === 'split-right' || position === 'split-bottom' ? targetTab.id : sourceTab.id,
      size: 50,
      direction
    };
    
    const pane2: SplitPane = {
      id: `pane-${Date.now()}-2`,
      tabs: position === 'split-right' || position === 'split-bottom' ? [sourceTab] : [targetTab],
      activeTabId: position === 'split-right' || position === 'split-bottom' ? sourceTab.id : targetTab.id,
      size: 50,
      direction
    };
    
    setSplitPanes([pane1, pane2]);
    setIsSplitMode(true);
  };
  
  const reorderTabs = (sourceTab: Tab, targetTab: Tab, position: 'before' | 'after') => {
    const newTabs = tabs.filter(t => t.id !== sourceTab.id);
    const targetIndex = newTabs.findIndex(t => t.id === targetTab.id);
    const insertIndex = position === 'before' ? targetIndex : targetIndex + 1;
    
    newTabs.splice(insertIndex, 0, sourceTab);
    setTabs(newTabs);
    localStorage.setItem('meta-studio-tabs', JSON.stringify(newTabs));
  };

  const handleCloseSplit = () => {
    setIsSplitMode(false);
    setSplitPanes([]);
    setDropTarget(null);
    setPaneDropZone({ position: null });
  };

  // ペインエリアでのドラッグオーバー処理
  const handlePaneDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    
    if (!draggedTab) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    const width = rect.width;
    const height = rect.height;
    
    // エリアを4分割してドロップゾーンを判定
    const edgeThreshold = 0.25; // 端から25%の範囲
    let position: 'left' | 'right' | 'top' | 'bottom' | null = null;
    
    if (x < width * edgeThreshold) {
      position = 'left';
    } else if (x > width * (1 - edgeThreshold)) {
      position = 'right';
    } else if (y < height * edgeThreshold) {
      position = 'top';
    } else if (y > height * (1 - edgeThreshold)) {
      position = 'bottom';
    }
    
    setPaneDropZone({ position });
  };

  // ペインエリアでのドロップ処理
  const handlePaneDrop = (e: React.DragEvent) => {
    e.preventDefault();
    if (!draggedTab || !paneDropZone.position) return;
    
    // 現在のアクティブタブを取得
    const currentTab = tabs.find(t => t.id === currentTabId);
    if (!currentTab) return;
    
    // 分割ペインを作成
    const direction = (paneDropZone.position === 'left' || paneDropZone.position === 'right') ? 'horizontal' : 'vertical';
    
    const pane1: SplitPane = {
      id: `pane-${Date.now()}-1`,
      tabs: paneDropZone.position === 'right' || paneDropZone.position === 'bottom' ? [currentTab] : [draggedTab],
      activeTabId: paneDropZone.position === 'right' || paneDropZone.position === 'bottom' ? currentTab.id : draggedTab.id,
      size: 50,
      direction
    };
    
    const pane2: SplitPane = {
      id: `pane-${Date.now()}-2`,
      tabs: paneDropZone.position === 'right' || paneDropZone.position === 'bottom' ? [draggedTab] : [currentTab],
      activeTabId: paneDropZone.position === 'right' || paneDropZone.position === 'bottom' ? draggedTab.id : currentTab.id,
      size: 50,
      direction
    };
    
    setSplitPanes([pane1, pane2]);
    setIsSplitMode(true);
    setDraggedTab(null);
    setPaneDropZone({ position: null });
  };
  
  // ペインのリサイズ処理
  const handlePaneResizeStart = (e: React.MouseEvent, paneId: string) => {
    const pane = splitPanes.find(p => p.id === paneId);
    if (!pane) return;
    
    setPaneResizing({
      paneId,
      startX: e.clientX,
      startY: e.clientY,
      startSize: pane.size
    });
  };
  
  useEffect(() => {
    if (!paneResizing) return;
    
    const handleMouseMove = (e: MouseEvent) => {
      const { paneId, startX, startY, startSize } = paneResizing;
      const pane = splitPanes.find(p => p.id === paneId);
      if (!pane) return;
      
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;
      const delta = pane.direction === 'horizontal' ? deltaX : deltaY;
      
      // 画面サイズに対する割合で計算
      const containerSize = pane.direction === 'horizontal' ? window.innerWidth : window.innerHeight;
      const deltaPercent = (delta / containerSize) * 100;
      
      const newSize = Math.max(10, Math.min(90, startSize + deltaPercent));
      
      setSplitPanes(prev => prev.map(p => {
        if (p.id === paneId) {
          return { ...p, size: newSize };
        }
        // 隣接ペインのサイズも調整
        if (prev.length === 2) {
          const otherPane = prev.find(other => other.id !== paneId);
          if (otherPane && p.id === otherPane.id) {
            return { ...p, size: 100 - newSize };
          }
        }
        return p;
      }));
    };
    
    const handleMouseUp = () => {
      setPaneResizing(null);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [paneResizing, splitPanes]);

  const renderTabContent = (tab: Tab) => {
    // 分割ペインでは、各タブに対応する独立したコンテンツを表示する必要がある
    // onTabChangeを呼び出してコンテンツを更新してから、childrenを返す
    console.log('TabManager: renderTabContent called for tab:', tab);
    
    // 重要: 分割ペイン用にタブ変更を通知（コンテンツ更新のため）
    if (onTabChange) {
      onTabChange(tab);
    }
    
    // 各ペインで独立したコンテンツを表示するため、ユニークキーと属性を設定
    const content = React.cloneElement(children as React.ReactElement, { 
      key: `split-pane-${tab.id}-${tab.type}-${Date.now()}`,
      'data-tab-id': tab.id,
      'data-tab-type': tab.type,
      'data-is-split-pane': 'true'
    });
    
    console.log('TabManager: Split pane content rendered for tab:', tab.id);
    return content;
  };

  // タブを二段に分ける関数（シンプル版）
  const getTabRows = () => {
    // ダッシュボード（home）タブを分離
    const homeTab = tabs.find(t => t.id === 'home');
    const otherTabs = tabs.filter(t => t.id !== 'home');
    
    // ピンされたタブとそうでないタブを分ける（homeタブ以外）
    const pinnedTabs = otherTabs.filter(t => t.isPinned).sort((a, b) => a.title.localeCompare(b.title));
    const unpinnedTabs = otherTabs.filter(t => !t.isPinned);
    
    // ダッシュボードタブを最初に、その後ピンタブ、未ピンタブの順
    const sortedTabs = homeTab ? [homeTab, ...pinnedTabs, ...unpinnedTabs] : [...pinnedTabs, ...unpinnedTabs];
    
    // 最大18個まで表示（1段目9個、2段目9個）
    const limitedTabs = sortedTabs.slice(0, 18);
    
    // 改善されたタブ分割ロジック：安定した2段表示
    const rows: Tab[][] = [];
    
    if (limitedTabs.length <= 9) {
      // 9個以下は1段にすべて表示
      rows.push(limitedTabs);
    } else {
      // 10個以上は2段に分割
      const firstRowCount = Math.min(9, limitedTabs.length);
      const secondRowCount = Math.min(9, limitedTabs.length - firstRowCount);
      
      // 1段目：最大9個
      rows.push(limitedTabs.slice(0, firstRowCount));
      
      // 2段目：残りのタブ（最大9個）
      if (secondRowCount > 0) {
        rows.push(limitedTabs.slice(firstRowCount, firstRowCount + secondRowCount));
      }
    }
    
    return rows;
  };

  const tabRows = getTabRows();

  return (
    <div className="flex-1 flex flex-col bg-base-100 min-w-0">
      {/* タブバー */}
      <div className="border-b border-base-content/10 bg-base-200/50">
        {!isSplitMode ? (
          // 通常モード
          tabRows.map((row, rowIndex) => (
            <div key={rowIndex} className="flex min-h-[40px]">
              <div className="flex-1 flex items-end">
                {row.map(tab => (
                  <div
                    key={tab.id}
                    className={`
                      flex items-center gap-1 px-2 py-2 cursor-pointer transition-all
                      border-r border-base-content/10 whitespace-nowrap
                      min-w-[80px] max-w-[200px] flex-shrink-1 relative
                      ${currentTabId === tab.id 
                        ? 'bg-base-100 border-t-2 border-t-primary' 
                        : 'hover:bg-base-100/50 border-t-2 border-t-transparent'
                      }
                      ${tab.isPinned ? 'ring-2 ring-warning/40 ring-inset' : ''}
                      ${draggedTab && tab.id === currentTabId ? 'bg-gradient-to-r from-primary/20 to-primary/10' : ''}
                    `}
                    draggable={tab.id !== 'home'}
                    onDragStart={(e) => handleDragStart(e, tab)}
                    onDragOver={(e) => handleDragOver(e, tab)}
                    onDrop={(e) => handleDrop(e, tab)}
                    onClick={() => handleTabClick(tab)}
                  >
                    <span className="text-sm font-medium truncate flex-1 flex items-center gap-1.5 overflow-hidden">
                      {tab.type === 'project' && <span className="text-xs">📁</span>}
                      {tab.type === 'home' && <span className="text-xs">🏠</span>}
                      {tab.type === 'dashboard' && <span className="text-xs">📊</span>}
                      {tab.type === 'launcher' && <span className="text-xs">🚀</span>}
                      {tab.type === 'system' && <span className="text-xs">⚙️</span>}
                      {tab.type === 'file' && (() => {
                        const fileName = tab.title.toLowerCase();
                        const getFileIcon = () => {
                          if (fileName.endsWith('.md')) return { icon: '📝', color: 'text-info' };
                          if (fileName.endsWith('.yaml') || fileName.endsWith('.yml')) return { icon: '📋', color: 'text-warning' };
                          if (fileName.endsWith('.json')) return { icon: '📄', color: 'text-success' };
                          if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) return { icon: '🔧', color: 'text-primary' };
                          if (fileName.endsWith('.js') || fileName.endsWith('.jsx')) return { icon: '⚡', color: 'text-accent' };
                          if (fileName.endsWith('.css')) return { icon: '🎨', color: 'text-secondary' };
                          if (fileName.endsWith('.html')) return { icon: '🌐', color: 'text-info' };
                          if (fileName.endsWith('.py')) return { icon: '🐍', color: 'text-warning' };
                          if (fileName.endsWith('.toml')) return { icon: '⚙️', color: 'text-neutral' };
                          if (fileName.endsWith('.txt')) return { icon: '📄', color: 'text-base-content' };
                          if (fileName.endsWith('.env')) return { icon: '🔐', color: 'text-error' };
                          if (fileName.endsWith('.gitignore') || fileName.includes('git')) return { icon: '📋', color: 'text-neutral' };
                          if (fileName.endsWith('.lock')) return { icon: '🔒', color: 'text-error' };
                          if (fileName.includes('package') || fileName.includes('config')) return { icon: '📦', color: 'text-primary' };
                          if (fileName.includes('readme')) return { icon: '📖', color: 'text-info' };
                          if (fileName.includes('license')) return { icon: '📜', color: 'text-neutral' };
                          return { icon: '📄', color: 'text-base-content' };
                        };
                        const { icon, color } = getFileIcon();
                        return <span className={`text-xs ${color}`}>{icon}</span>;
                      })()}
                      <span className={tab.type === 'file' ? (() => {
                        const fileName = tab.title.toLowerCase();
                        if (fileName.endsWith('.md')) return 'text-info';
                        if (fileName.endsWith('.yaml') || fileName.endsWith('.yml')) return 'text-warning';
                        if (fileName.endsWith('.json')) return 'text-success';
                        if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) return 'text-primary';
                        if (fileName.endsWith('.js') || fileName.endsWith('.jsx')) return 'text-accent';
                        if (fileName.endsWith('.css')) return 'text-secondary';
                        if (fileName.endsWith('.html')) return 'text-info';
                        if (fileName.endsWith('.py')) return 'text-warning';
                        if (fileName.endsWith('.env')) return 'text-error';
                        return 'text-base-content';
                      })() : ''}>{tab.title}</span>
                    </span>
                    
                    {/* タブアクション */}
                    {tab.id !== 'home' && (
                      <div className="flex items-center gap-1">
                        
                        <button
                          className={`btn btn-xs btn-ghost btn-circle flex-shrink-0 ${tab.isPinned ? 'text-warning hover:bg-warning/20' : 'hover:bg-base-300'}`}
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePinTab(tab.id);
                          }}
                          title={tab.isPinned ? 'ピンを外す' : 'タブを固定'}
                        >
                          <Pin size={10} className={tab.isPinned ? 'fill-current' : ''} />
                        </button>
                        
                        {!tab.isPinned && (
                          <button
                            className="btn btn-xs btn-ghost btn-circle hover:bg-error/20 flex-shrink-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCloseTab(tab.id);
                            }}
                            title="タブを閉じる"
                          >
                            <X size={14} />
                          </button>
                        )}
                      </div>
                    )}
                    
                    {/* ドロップゾーンインジケーター */}
                    {draggedTab && dropTarget?.tabId === tab.id && (
                      <div className={`absolute inset-0 transition-all pointer-events-none ${
                        dropTarget.position === 'split-left' ? 'border-l-4 border-l-primary bg-primary/20 border-2 border-dashed' :
                        dropTarget.position === 'split-right' ? 'border-r-4 border-r-primary bg-primary/20 border-2 border-dashed' :
                        dropTarget.position === 'split-top' ? 'border-t-4 border-t-primary bg-primary/20 border-2 border-dashed' :
                        dropTarget.position === 'split-bottom' ? 'border-b-4 border-b-primary bg-primary/20 border-2 border-dashed' :
                        dropTarget.position === 'before' ? 'border-l-2 border-l-accent bg-accent/10' :
                        'border-r-2 border-r-accent bg-accent/10'
                      }`}>
                        {dropTarget.position.startsWith('split-') && (
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="bg-primary text-primary-content px-2 py-1 rounded text-xs font-bold">
                              {dropTarget.position === 'split-left' ? '← 左分割' :
                               dropTarget.position === 'split-right' ? '右分割 →' :
                               dropTarget.position === 'split-top' ? '↑ 上分割' :
                               '下分割 ↓'}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
              {rowIndex === 0 && tabs.length > 1 && (
                <div className="flex items-center p-2 flex-shrink-0 bg-base-200/50">
                  <button
                    className="btn btn-xs btn-ghost btn-circle hover:bg-error/20 text-error"
                    onClick={handleCloseAllTabs}
                    title="全タブを閉じる"
                  >
                    💀
                  </button>
                </div>
              )}
            </div>
          ))
        ) : (
          // 分割モード
          <div className="flex">
            {splitPanes.map((pane, index) => (
              <div key={pane.id} className="flex-1 flex border-r border-base-content/10">
                <div className="flex-1 flex">
                  {pane.tabs.map(tab => (
                    <div
                      key={tab.id}
                      className={`flex items-center gap-1 px-2 py-2 cursor-pointer transition-all border-r border-base-content/10 flex-1 ${
                        pane.activeTabId === tab.id ? 'bg-base-100 border-t-2 border-t-primary' : 'hover:bg-base-100/50 border-t-2 border-t-transparent'
                      }`}
                      onClick={() => handleTabClick(tab, pane.id)}
                    >
                      <span className="text-sm font-medium truncate flex-1 flex items-center gap-1.5">
                        {tab.type === 'project' && <span className="text-xs">📁</span>}
                        {tab.type === 'home' && <span className="text-xs">🏠</span>}
                        {tab.type === 'dashboard' && <span className="text-xs">📊</span>}
                        {tab.type === 'launcher' && <span className="text-xs">🚀</span>}
                        {tab.type === 'system' && <span className="text-xs">⚙️</span>}
                        {tab.type === 'file' && (() => {
                          const fileName = tab.title.toLowerCase();
                          const getFileIcon = () => {
                            if (fileName.endsWith('.md')) return { icon: '📝', color: 'text-info' };
                            if (fileName.endsWith('.yaml') || fileName.endsWith('.yml')) return { icon: '📋', color: 'text-warning' };
                            if (fileName.endsWith('.json')) return { icon: '📄', color: 'text-success' };
                            if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) return { icon: '🔧', color: 'text-primary' };
                            if (fileName.endsWith('.js') || fileName.endsWith('.jsx')) return { icon: '⚡', color: 'text-accent' };
                            if (fileName.endsWith('.css')) return { icon: '🎨', color: 'text-secondary' };
                            if (fileName.endsWith('.html')) return { icon: '🌐', color: 'text-info' };
                            if (fileName.endsWith('.py')) return { icon: '🐍', color: 'text-warning' };
                            if (fileName.endsWith('.toml')) return { icon: '⚙️', color: 'text-neutral' };
                            if (fileName.endsWith('.txt')) return { icon: '📄', color: 'text-base-content' };
                            if (fileName.endsWith('.env')) return { icon: '🔐', color: 'text-error' };
                            if (fileName.endsWith('.gitignore') || fileName.includes('git')) return { icon: '📋', color: 'text-neutral' };
                            if (fileName.endsWith('.lock')) return { icon: '🔒', color: 'text-error' };
                            if (fileName.includes('package') || fileName.includes('config')) return { icon: '📦', color: 'text-primary' };
                            if (fileName.includes('readme')) return { icon: '📖', color: 'text-info' };
                            if (fileName.includes('license')) return { icon: '📜', color: 'text-neutral' };
                            return { icon: '📄', color: 'text-base-content' };
                          };
                          const { icon, color } = getFileIcon();
                          return <span className={`text-xs ${color}`}>{icon}</span>;
                        })()}
                        <span className={tab.type === 'file' ? (() => {
                          const fileName = tab.title.toLowerCase();
                          if (fileName.endsWith('.md')) return 'text-info';
                          if (fileName.endsWith('.yaml') || fileName.endsWith('.yml')) return 'text-warning';
                          if (fileName.endsWith('.json')) return 'text-success';
                          if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) return 'text-primary';
                          if (fileName.endsWith('.js') || fileName.endsWith('.jsx')) return 'text-accent';
                          if (fileName.endsWith('.css')) return 'text-secondary';
                          if (fileName.endsWith('.html')) return 'text-info';
                          if (fileName.endsWith('.py')) return 'text-warning';
                          if (fileName.endsWith('.env')) return 'text-error';
                          return 'text-base-content';
                        })() : ''}>{tab.title}</span>
                      </span>
                    </div>
                  ))}
                </div>
                {index === 0 && (
                  <button
                    className="btn btn-xs btn-ghost btn-circle hover:bg-error/20 text-error"
                    onClick={handleCloseSplit}
                    title="分割を解除"
                  >
                    <X size={14} />
                  </button>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* コンテンツ表示 */}
      {!isSplitMode ? (
        <div 
          className="flex-1 relative"
          onDragOver={handlePaneDragOver}
          onDrop={handlePaneDrop}
          onDragLeave={() => setPaneDropZone({ position: null })}
          style={{ height: 'calc(100vh - 120px)' }} /* タブバー分を除いた高さ */
        >
          {children}
          
          {/* ペイン分割ドロップゾーンの視覚的フィードバック */}
          {draggedTab && paneDropZone.position && (
            <div className="absolute inset-0 pointer-events-none z-50">
              <div className={`absolute transition-all duration-200 ${
                paneDropZone.position === 'left' ? 'left-0 top-0 w-1/2 h-full border-r-4 border-r-primary bg-primary/20' :
                paneDropZone.position === 'right' ? 'right-0 top-0 w-1/2 h-full border-l-4 border-l-primary bg-primary/20' :
                paneDropZone.position === 'top' ? 'left-0 top-0 w-full h-1/2 border-b-4 border-b-primary bg-primary/20' :
                'left-0 bottom-0 w-full h-1/2 border-t-4 border-t-primary bg-primary/20'
              }`}>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="bg-primary text-primary-content px-4 py-2 rounded-lg text-sm font-bold">
                    {paneDropZone.position === 'left' ? '← 左分割' :
                     paneDropZone.position === 'right' ? '右分割 →' :
                     paneDropZone.position === 'top' ? '↑ 上分割' :
                     '下分割 ↓'}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className={`flex-1 flex ${splitPanes[0]?.direction === 'vertical' ? 'flex-col' : 'flex-row'}`}>
          {splitPanes.map((pane, index) => {
            const activeTab = pane.tabs.find(t => t.id === pane.activeTabId);
            const isHorizontal = pane.direction === 'horizontal';
            return (
              <div key={pane.id} className="relative" style={{
                [isHorizontal ? 'width' : 'height']: `${pane.size}%`,
                height: isHorizontal ? 'calc(100vh - 120px)' : `${pane.size}%`
              }}>
                {activeTab && renderTabContent(activeTab)}
                
                {/* リサイズハンドル */}
                {index < splitPanes.length - 1 && (
                  <div
                    className={`absolute ${isHorizontal ? 'right-0 top-0 h-full w-1 cursor-col-resize' : 'bottom-0 left-0 w-full h-1 cursor-row-resize'} bg-base-content/20 hover:bg-primary/50 transition-colors z-10`}
                    onMouseDown={(e) => handlePaneResizeStart(e, pane.id)}
                    title="ドラッグしてリサイズ"
                  />
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}