'use client';

import { useState } from 'react';
import { <PERSON>bul<PERSON>, <PERSON>, <PERSON>rk<PERSON>, <PERSON>R<PERSON>, <PERSON>, Setting<PERSON>, Code } from 'lucide-react';

interface SystemPlaceholderProps {
  title: string;
  description?: string;
  icon?: string;
  features?: string[];
  plannedFeatures?: string[];
  estimatedCompletion?: string;
  priority?: 'high' | 'medium' | 'low';
}

export default function SystemPlaceholder({ 
  title, 
  description, 
  icon, 
  features = [],
  plannedFeatures = [],
  estimatedCompletion,
  priority = 'medium'
}: SystemPlaceholderProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const priorityColors = {
    high: 'border-error/30 bg-error/5',
    medium: 'border-warning/30 bg-warning/5', 
    low: 'border-info/30 bg-info/5'
  };

  const priorityIcons = {
    high: '🔥',
    medium: '⭐',
    low: '💡'
  };

  return (
    <div className="h-full flex flex-col items-center justify-center p-8 text-center bg-base-100">
      {/* メインコンテンツ */}
      <div className={`max-w-2xl w-full rounded-2xl border-2 ${priorityColors[priority]} p-8 shadow-lg backdrop-blur-sm`}>
        {/* ヘッダー */}
        <div className="flex items-center justify-center gap-3 mb-6">
          <div className="text-6xl animate-pulse">
            {icon || '⚙️'}
          </div>
          <div className="text-2xl">
            {priorityIcons[priority]}
          </div>
        </div>
        
        <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
          {title}
        </h1>
        
        {description && (
          <p className="text-lg text-base-content/80 mb-6 leading-relaxed">
            {description}
          </p>
        )}

        {/* ステータス */}
        <div className="flex items-center justify-center gap-4 mb-6">
          <div className="flex items-center gap-2 px-4 py-2 bg-base-200/50 rounded-full">
            <Clock size={16} />
            <span className="text-sm font-medium">開発中</span>
          </div>
          
          {estimatedCompletion && (
            <div className="flex items-center gap-2 px-4 py-2 bg-base-200/50 rounded-full">
              <Sparkles size={16} />
              <span className="text-sm font-medium">{estimatedCompletion}</span>
            </div>
          )}
          
          <div className="flex items-center gap-2 px-4 py-2 bg-base-200/50 rounded-full">
            <span className="text-sm font-medium capitalize">{priority} Priority</span>
          </div>
        </div>

        {/* 詳細展開ボタン */}
        <button 
          className="btn btn-outline btn-sm mb-6"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <Lightbulb size={16} />
          {isExpanded ? '詳細を隠す' : '詳細を表示'}
          <ArrowRight size={16} className={`transition-transform ${isExpanded ? 'rotate-90' : ''}`} />
        </button>

        {/* 展開コンテンツ */}
        {isExpanded && (
          <div className="space-y-6 border-t border-base-content/10 pt-6">
            {/* 現在の機能 */}
            {features.length > 0 && (
              <div className="text-left">
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <Code size={18} />
                  実装済み機能
                </h3>
                <ul className="space-y-2">
                  {features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-success text-sm mt-1">✓</span>
                      <span className="text-sm text-base-content/80">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* 予定機能 */}
            {plannedFeatures.length > 0 && (
              <div className="text-left">
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <Sparkles size={18} />
                  予定機能
                </h3>
                <ul className="space-y-2">
                  {plannedFeatures.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-warning text-sm mt-1">⏳</span>
                      <span className="text-sm text-base-content/60">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* アクション */}
            <div className="flex flex-wrap gap-3 justify-center pt-4">
              <button className="btn btn-xs btn-ghost">
                <Bell size={14} />
                完成通知を受け取る
              </button>
              <button className="btn btn-xs btn-ghost">
                <Settings size={14} />
                要望を送る
              </button>
              <button className="btn btn-xs btn-ghost">
                <Code size={14} />
                開発に参加
              </button>
            </div>
          </div>
        )}

      </div>

      {/* プログレスバー */}
      <div className="mt-8 w-full max-w-md">
        <div className="text-xs text-base-content/60 mb-2 flex justify-between">
          <span>開発進捗</span>
          <span>{features.length > 0 ? Math.round((features.length / (features.length + plannedFeatures.length)) * 100) : 0}%</span>
        </div>
        <div className="w-full bg-base-200 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-primary to-secondary h-2 rounded-full transition-all duration-500 ease-out"
            style={{
              width: `${features.length > 0 ? Math.round((features.length / (features.length + plannedFeatures.length)) * 100) : 5}%`
            }}
          ></div>
        </div>
      </div>
    </div>
  );
}