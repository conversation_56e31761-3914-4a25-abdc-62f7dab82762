/**
 * 統一モデルローダー
 * 全モデル形式を統一的に読み込むためのローダーシステム
 */

import * as THREE from 'three';
import { 
  UniversalModel, 
  ModelFormat, 
  ModelType, 
  ModelLoadResult, 
  ModelValidationResult,
  ModelLoaderOptions,
  ModelCapabilities,
  ModelMetadata
} from '@/types/UniversalModel';

// ローダープラグインインターフェース
export interface ModelLoaderPlugin {
  format: ModelFormat;
  supportedExtensions: string[];
  mimeTypes: string[];
  
  validate(data: ArrayBuffer, filename: string): ModelValidationResult;
  load(data: ArrayBuffer, filename: string, options?: ModelLoaderOptions): Promise<UniversalModel>;
  getDefaultCapabilities(): ModelCapabilities;
}

// 統一モデルローダー
export class UniversalModelLoader {
  private plugins: Map<ModelFormat, ModelLoaderPlugin> = new Map();
  private loadingCache: Map<string, Promise<ModelLoadResult>> = new Map();
  private isInitialized = false;

  constructor() {
    // 初期化は非同期で実行
    this.initialize();
  }

  /**
   * 非同期初期化
   */
  private async initialize(): Promise<void> {
    if (this.isInitialized) return;

    await this.initializeDefaultPlugins();
    this.isInitialized = true;
  }

  /**
   * 初期化完了を待機
   */
  async waitForInitialization(): Promise<void> {
    while (!this.isInitialized) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }
  
  /**
   * プラグインを登録
   */
  registerPlugin(plugin: ModelLoaderPlugin): void {
    this.plugins.set(plugin.format, plugin);
    console.log(`📦 モデルローダープラグイン登録: ${plugin.format}`);
  }
  
  /**
   * サポートされている形式を取得
   */
  getSupportedFormats(): ModelFormat[] {
    return Array.from(this.plugins.keys());
  }
  
  /**
   * ファイル形式を検出
   */
  detectFormat(data: ArrayBuffer, filename: string): ModelValidationResult {
    let bestResult: ModelValidationResult = {
      isValid: false,
      format: ModelFormat.UNKNOWN,
      confidence: 0
    };
    
    // 各プラグインで検証
    for (const plugin of this.plugins.values()) {
      try {
        const result = plugin.validate(data, filename);
        if (result.confidence > bestResult.confidence) {
          bestResult = result;
        }
      } catch (error) {
        console.warn(`プラグイン検証エラー (${plugin.format}):`, error);
      }
    }
    
    console.log(`🔍 ファイル形式検出: ${filename} → ${bestResult.format} (信頼度: ${bestResult.confidence})`);
    return bestResult;
  }
  
  /**
   * モデルを読み込み
   */
  async loadModel(
    data: ArrayBuffer,
    filename: string,
    options?: ModelLoaderOptions
  ): Promise<ModelLoadResult> {
    // 初期化完了を待機
    await this.waitForInitialization();

    const cacheKey = `${filename}_${data.byteLength}`;

    // キャッシュチェック
    if (this.loadingCache.has(cacheKey)) {
      return await this.loadingCache.get(cacheKey)!;
    }
    
    const loadPromise = this.performLoad(data, filename, options);
    this.loadingCache.set(cacheKey, loadPromise);
    
    try {
      const result = await loadPromise;
      return result;
    } finally {
      // 読み込み完了後はキャッシュから削除
      this.loadingCache.delete(cacheKey);
    }
  }
  
  /**
   * 実際の読み込み処理
   */
  private async performLoad(
    data: ArrayBuffer, 
    filename: string, 
    options?: ModelLoaderOptions
  ): Promise<ModelLoadResult> {
    try {
      console.log(`🚀 モデル読み込み開始: ${filename} (${data.byteLength} bytes)`);
      
      // 形式検出
      const validation = this.detectFormat(data, filename);
      
      if (!validation.isValid || validation.format === ModelFormat.UNKNOWN) {
        return {
          success: false,
          error: `サポートされていないファイル形式: ${filename}`
        };
      }
      
      // 対応プラグインを取得
      const plugin = this.plugins.get(validation.format);
      if (!plugin) {
        return {
          success: false,
          error: `プラグインが見つかりません: ${validation.format}`
        };
      }
      
      // モデル読み込み
      const model = await plugin.load(data, filename, options);
      
      // 基本情報を設定
      model.fileName = filename;
      model.fileSize = data.byteLength;
      model.arrayBuffer = data;
      model.isLoaded = true;
      
      console.log(`✅ モデル読み込み完了: ${model.name} (${model.format})`);
      console.log(`📊 モデル機能:`, model.capabilities);
      
      return {
        success: true,
        model
      };
      
    } catch (error) {
      console.error(`❌ モデル読み込みエラー: ${filename}`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * ファイル拡張子から形式を推測
   */
  getFormatFromExtension(filename: string): ModelFormat {
    const ext = filename.toLowerCase().split('.').pop() || '';
    
    const extensionMap: Record<string, ModelFormat> = {
      'vrm': ModelFormat.VRM,
      'glb': ModelFormat.GLB,
      'gltf': ModelFormat.GLTF,
      'fbx': ModelFormat.FBX,
      'obj': ModelFormat.OBJ,
      'pmx': ModelFormat.PMX,
      'pmd': ModelFormat.MMD,
      'model3': ModelFormat.LIVE2D,
      'moc3': ModelFormat.LIVE2D
    };
    
    return extensionMap[ext] || ModelFormat.UNKNOWN;
  }
  
  /**
   * デフォルトプラグインを初期化
   */
  private async initializeDefaultPlugins(): Promise<void> {
    try {
      // VRMローダープラグインを登録
      const { VRMLoaderPlugin } = await import('@/lib/plugins/VRMLoaderPlugin');
      this.registerPlugin(new VRMLoaderPlugin());

      // GLTFローダープラグインを登録
      const { GLTFLoaderPlugin, GLBLoaderPlugin } = await import('@/lib/plugins/GLTFLoaderPlugin');
      this.registerPlugin(new GLTFLoaderPlugin());
      this.registerPlugin(new GLBLoaderPlugin());

      // LIVE2Dローダープラグインを登録
      const { Live2DLoaderPlugin } = await import('@/lib/plugins/Live2DLoaderPlugin');
      this.registerPlugin(new Live2DLoaderPlugin());

      console.log('🔧 統一モデルローダー初期化完了');
      console.log(`📦 登録済みプラグイン: ${this.plugins.size}個`);
      console.log(`📋 対応形式: ${this.getSupportedFormats().join(', ')}`);

    } catch (error) {
      console.error('❌ デフォルトプラグイン初期化エラー:', error);
    }
  }
  
  /**
   * キャッシュをクリア
   */
  clearCache(): void {
    this.loadingCache.clear();
    console.log('🧹 モデルローダーキャッシュをクリア');
  }
  
  /**
   * 統計情報を取得
   */
  getStats() {
    return {
      registeredPlugins: this.plugins.size,
      supportedFormats: this.getSupportedFormats(),
      activeLoads: this.loadingCache.size
    };
  }
}

// ユーティリティ関数
export class ModelLoaderUtils {
  /**
   * ファイルヘッダーを解析
   */
  static analyzeFileHeader(data: ArrayBuffer): {
    magic: string;
    bytes: Uint8Array;
    isGLTF: boolean;
    isBinary: boolean;
  } {
    const bytes = new Uint8Array(data, 0, Math.min(12, data.byteLength));
    const magic = Array.from(bytes.slice(0, 4))
      .map(b => String.fromCharCode(b))
      .join('');
    
    return {
      magic,
      bytes,
      isGLTF: magic === 'glTF',
      isBinary: bytes[0] === 0x67 && bytes[1] === 0x6C && bytes[2] === 0x54 && bytes[3] === 0x46
    };
  }
  
  /**
   * ファイルサイズを人間が読める形式に変換
   */
  static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }
  
  /**
   * ユニークIDを生成
   */
  static generateModelId(): string {
    return `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// シングルトンインスタンス
export const universalModelLoader = new UniversalModelLoader();
