import { createClient } from '@supabase/supabase-js';

// TODO: 実際の環境では環境変数を使用
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-key';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// データベーススキーマ型定義（将来の実装用）
export interface Project {
  id: string;
  name: string;
  description: string;
  status: 'planning' | 'development' | 'testing' | 'deployed' | 'paused';
  progress: number;
  king_id?: string;
  general_id?: string;
  soldiers_count: number;
  technologies: string[];
  created_at: string;
  updated_at: string;
}

export interface Agent {
  id: string;
  name: string;
  type: 'god' | 'king' | 'mother' | 'general' | 'soldier';
  status: 'active' | 'busy' | 'idle' | 'learning' | 'offline';
  level: number;
  experience: number;
  skills: string[];
  current_task?: string;
  performance: number;
  assigned_project_id?: string;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'god' | 'user';
  settings: {
    theme: string;
    notifications: boolean;
    language: string;
  };
  created_at: string;
  updated_at: string;
}