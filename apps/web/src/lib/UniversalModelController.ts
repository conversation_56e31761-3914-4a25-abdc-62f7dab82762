/**
 * 統一モデルコントローラー
 * 全モデル形式を統一的に制御するためのコントローラー
 */

import * as THREE from 'three';
import { 
  UniversalModel, 
  ModelFormat, 
  ModelController,
  ExpressionState,
  VisemeData,
  AnimationState,
  ModelEvent,
  ModelEventListener
} from '@/types/UniversalModel';

export class UniversalModelController implements ModelController {
  private model: UniversalModel;
  private scene?: THREE.Scene;
  private eventListeners: Map<string, ModelEventListener[]> = new Map();
  
  // アニメーション状態
  private animationStates: Map<string, AnimationState> = new Map();
  private currentExpressions: ExpressionState = {};
  
  // まばたき制御
  private autoBlinking = true;
  private blinkTimer = 0;
  private lastBlinkTime = 0;
  private blinkFrequency = 3000; // 3秒間隔
  
  // 視線制御
  private lookAtTarget?: THREE.Vector3;
  
  constructor(model: UniversalModel, scene?: THREE.Scene) {
    this.model = model;
    this.scene = scene;
    
    this.initializeController();
  }
  
  /**
   * コントローラーを初期化
   */
  private initializeController(): void {
    console.log(`🎮 モデルコントローラー初期化: ${this.model.name} (${this.model.format})`);
    
    // モデルをシーンに追加
    if (this.scene && this.model.data.scene) {
      this.scene.add(this.model.data.scene);
    }
    
    // アニメーションミキサーを初期化
    if (this.model.data.animations && this.model.data.animations.length > 0) {
      this.initializeAnimations();
    }
    
    this.emitEvent('initialized', {});
  }
  
  /**
   * 表示/非表示を設定
   */
  setVisible(visible: boolean): void {
    this.model.isVisible = visible;
    
    if (this.model.data.scene) {
      this.model.data.scene.visible = visible;
    }
    
    this.emitEvent('visibilityChanged', { visible });
  }
  
  /**
   * 位置を設定
   */
  setPosition(position: THREE.Vector3): void {
    this.model.position = position.clone();
    
    if (this.model.data.scene) {
      this.model.data.scene.position.copy(position);
    }
    
    this.emitEvent('positionChanged', { position });
  }
  
  /**
   * 回転を設定
   */
  setRotation(rotation: THREE.Euler): void {
    this.model.rotation = rotation.clone();
    
    if (this.model.data.scene) {
      this.model.data.scene.rotation.copy(rotation);
    }
    
    this.emitEvent('rotationChanged', { rotation });
  }
  
  /**
   * スケールを設定
   */
  setScale(scale: THREE.Vector3): void {
    this.model.scale = scale.clone();
    
    if (this.model.data.scene) {
      this.model.data.scene.scale.copy(scale);
    }
    
    this.emitEvent('scaleChanged', { scale });
  }
  
  /**
   * アニメーションを再生
   */
  playAnimation(name: string, loop = true): void {
    if (!this.model.capabilities.animations) {
      console.warn('このモデルはアニメーションをサポートしていません');
      return;
    }
    
    switch (this.model.format) {
      case ModelFormat.VRM:
        this.playVRMAnimation(name, loop);
        break;
      case ModelFormat.LIVE2D:
        this.playLive2DAnimation(name, loop);
        break;
      default:
        this.playGenericAnimation(name, loop);
        break;
    }
    
    this.emitEvent('animationStarted', { name, loop });
  }
  
  /**
   * アニメーションを停止
   */
  stopAnimation(name: string): void {
    const state = this.animationStates.get(name);
    if (state) {
      state.playing = false;
      this.animationStates.set(name, state);
    }
    
    this.emitEvent('animationStopped', { name });
  }
  
  /**
   * アニメーションの重みを設定
   */
  setAnimationWeight(name: string, weight: number): void {
    const state = this.animationStates.get(name);
    if (state) {
      state.weight = Math.max(0, Math.min(1, weight));
      this.animationStates.set(name, state);
    }
  }
  
  /**
   * 表情を設定
   */
  setExpression(name: string, weight: number): void {
    if (!this.model.capabilities.expressions) {
      console.warn('このモデルは表情をサポートしていません');
      return;
    }
    
    this.currentExpressions[name] = Math.max(0, Math.min(1, weight));
    
    switch (this.model.format) {
      case ModelFormat.VRM:
        this.setVRMExpression(name, weight);
        break;
      case ModelFormat.LIVE2D:
        this.setLive2DExpression(name, weight);
        break;
    }
    
    this.emitEvent('expressionChanged', { name, weight });
  }
  
  /**
   * 複数の表情を設定
   */
  setExpressions(expressions: ExpressionState): void {
    for (const [name, weight] of Object.entries(expressions)) {
      this.setExpression(name, weight);
    }
  }
  
  /**
   * リップシンクを設定
   */
  setLipSync(visemes: VisemeData): void {
    if (!this.model.capabilities.lipSync) {
      console.warn('このモデルはリップシンクをサポートしていません');
      return;
    }
    
    switch (this.model.format) {
      case ModelFormat.VRM:
        this.setVRMLipSync(visemes);
        break;
      case ModelFormat.LIVE2D:
        this.setLive2DLipSync(visemes);
        break;
    }
    
    this.emitEvent('lipSyncChanged', { visemes });
  }
  
  /**
   * まばたきを設定
   */
  setBlink(weight: number): void {
    if (!this.model.capabilities.blinking) {
      return;
    }
    
    this.setExpression('blink', weight);
    this.setExpression('blinkLeft', weight);
    this.setExpression('blinkRight', weight);
  }
  
  /**
   * 自動まばたきを設定
   */
  setAutoBlinking(enabled: boolean): void {
    this.autoBlinking = enabled;
    console.log(`👁️ 自動まばたき: ${enabled ? 'ON' : 'OFF'}`);
  }
  
  /**
   * 視線を設定
   */
  setLookAt(target: THREE.Vector3): void {
    if (!this.model.capabilities.eyeTracking) {
      return;
    }
    
    this.lookAtTarget = target.clone();
    
    switch (this.model.format) {
      case ModelFormat.VRM:
        this.setVRMLookAt(target);
        break;
    }
    
    this.emitEvent('lookAtChanged', { target });
  }
  
  /**
   * 更新処理
   */
  update(deltaTime: number): void {
    // 自動まばたき更新
    if (this.autoBlinking && this.model.capabilities.blinking) {
      this.updateAutoBlinking(deltaTime);
    }
    
    // アニメーション更新
    if (this.model.data.mixer) {
      this.model.data.mixer.update(deltaTime);
    }
    
    // VRM固有の更新
    if (this.model.format === ModelFormat.VRM && this.model.data.vrm) {
      this.updateVRM(deltaTime);
    }
    
    // LIVE2D固有の更新
    if (this.model.format === ModelFormat.LIVE2D && this.model.data.live2dModel) {
      this.updateLive2D(deltaTime);
    }
  }
  
  /**
   * リソースを破棄
   */
  dispose(): void {
    // シーンから削除
    if (this.scene && this.model.data.scene) {
      this.scene.remove(this.model.data.scene);
    }
    
    // アニメーションミキサーを破棄
    if (this.model.data.mixer) {
      this.model.data.mixer.stopAllAction();
    }
    
    // VRMリソースを破棄
    if (this.model.data.vrm && this.model.data.vrm.dispose) {
      this.model.data.vrm.dispose();
    }
    
    // イベントリスナーをクリア
    this.eventListeners.clear();
    
    this.emitEvent('disposed', {});
    console.log(`🗑️ モデルコントローラー破棄: ${this.model.name}`);
  }
  
  /**
   * イベントリスナーを追加
   */
  addEventListener(type: string, listener: ModelEventListener): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, []);
    }
    this.eventListeners.get(type)!.push(listener);
  }
  
  /**
   * イベントリスナーを削除
   */
  removeEventListener(type: string, listener: ModelEventListener): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  }
  
  /**
   * イベントを発火
   */
  private emitEvent(type: string, data: any): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      const event: ModelEvent = {
        type,
        model: this.model,
        data
      };
      
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('イベントリスナーエラー:', error);
        }
      });
    }
  }
  
  // 以下、形式固有の実装メソッド（後で実装）
  private initializeAnimations(): void {
    // アニメーション初期化の実装
  }
  
  private playVRMAnimation(name: string, loop: boolean): void {
    // VRMアニメーション再生の実装
  }
  
  private playLive2DAnimation(name: string, loop: boolean): void {
    // LIVE2Dアニメーション再生の実装
  }
  
  private playGenericAnimation(name: string, loop: boolean): void {
    // 汎用アニメーション再生の実装
  }
  
  private setVRMExpression(name: string, weight: number): void {
    // VRM表情設定の実装
    if (this.model.data.vrm?.expressionManager) {
      this.model.data.vrm.expressionManager.setValue(name, weight);
    }
  }
  
  private setLive2DExpression(name: string, weight: number): void {
    // LIVE2D表情設定の実装
  }
  
  private setVRMLipSync(visemes: VisemeData): void {
    // VRMリップシンクの実装
  }
  
  private setLive2DLipSync(visemes: VisemeData): void {
    // LIVE2Dリップシンクの実装
  }
  
  private setVRMLookAt(target: THREE.Vector3): void {
    // VRM視線制御の実装
    if (this.model.data.vrm?.lookAt) {
      this.model.data.vrm.lookAt.target = target;
    }
  }
  
  private updateAutoBlinking(deltaTime: number): void {
    const now = Date.now();
    
    if (this.blinkTimer === 0 && now - this.lastBlinkTime > this.blinkFrequency + Math.random() * 2000) {
      this.blinkTimer = 0.01; // まばたき開始
    }
    
    if (this.blinkTimer > 0) {
      const blinkProgress = this.blinkTimer / 0.3; // 0.3秒でまばたき完了
      let blinkIntensity = 0;
      
      if (blinkProgress < 0.5) {
        blinkIntensity = Math.sin(blinkProgress * Math.PI);
      } else {
        blinkIntensity = Math.sin((1 - blinkProgress) * Math.PI);
      }
      
      this.setBlink(blinkIntensity);
      this.blinkTimer += deltaTime;
      
      if (this.blinkTimer >= 0.3) {
        this.blinkTimer = 0;
        this.lastBlinkTime = now;
        this.setBlink(0);
      }
    }
  }
  
  private updateVRM(deltaTime: number): void {
    const vrm = this.model.data.vrm;
    if (!vrm) return;
    
    // VRM表情マネージャーの更新
    if (vrm.expressionManager) {
      vrm.expressionManager.update();
    }
    
    // VRMヒューマノイドの更新
    if (vrm.humanoid) {
      vrm.humanoid.update();
    }
    
    // VRM視線制御の更新
    if (vrm.lookAt) {
      vrm.lookAt.update(deltaTime);
    }
    
    // VRM物理演算の更新
    if (vrm.springBoneManager) {
      vrm.springBoneManager.update(deltaTime);
    }
  }
  
  private updateLive2D(deltaTime: number): void {
    const model = this.model.data.live2dModel;
    if (!model) return;
    
    // LIVE2Dモデルの更新
    if (model.update) {
      model.update(deltaTime);
    }
  }
}
