// サーバーサイド専用のAI機能をクライアントから呼び出し
import { claudeConfigCommands } from '@/utils/claude-terminal-commands';

// Claude設定コマンドのインポート
const { 
  'export-claude-config': exportClaudeConfig,
  'import-claude-config': importClaudeConfig,
  'claude-permission': setClaudePermission,
  'claude-permissions': getClaudePermissions,
  'claude-allow': claudeAllow,
  'claude-config': getClaudeConfig
} = claudeConfigCommands;

// クイックリファレンス機能
async function getQuickReference(type?: string): Promise<string> {
  try {
    const url = type ? `/api/quick-reference?type=${type}` : '/api/quick-reference?type=quick'
    const response = await fetch(url)
    const data = await response.json()
    
    if (data.success) {
      if (type === 'components') {
        return `📦 主要コンポーネント一覧:\n${data.components.map((comp: any) => 
          `• ${comp.name}: ${comp.purpose}\n  機能: ${comp.keyFeatures.join(', ')}`
        ).join('\n\n')}`
      } else if (type === 'apis') {
        return `🔗 APIエンドポイント一覧:\n${data.apis.map((api: any) => 
          `• ${api.method} ${api.path}\n  用途: ${api.purpose}`
        ).join('\n\n')}`
      } else if (type === 'commands') {
        return `⚡ ターミナルコマンド一覧:\n${data.commands.map((cmd: any) => 
          `• ${cmd.command}: ${cmd.description}\n  使用法: ${cmd.usage}`
        ).join('\n\n')}`
      } else if (type === 'tasks') {
        return `📋 頻出タスク一覧:\n${Object.entries(data.tasks).map(([key, task]: [string, any]) => 
          `• ${key}: ${task.description}\n  手順: ${task.steps?.join(' → ') || task.commands?.join(', ')}`
        ).join('\n\n')}`
      } else {
        return data.reference || data.quickReference
      }
    } else {
      return `❌ リファレンス取得エラー: ${data.error}`
    }
  } catch (error) {
    return `❌ リファレンス取得失敗: ${error}`
  }
}

async function searchReference(query: string): Promise<string> {
  try {
    const response = await fetch(`/api/quick-reference?type=search&query=${encodeURIComponent(query)}`)
    const data = await response.json()
    
    if (data.success) {
      const { results } = data
      let output = `🔍 検索結果: "${query}"\n\n`
      
      if (results.components.length > 0) {
        output += `📦 コンポーネント:\n${results.components.map((comp: any) => 
          `• ${comp.name}: ${comp.purpose}`
        ).join('\n')}\n\n`
      }
      
      if (results.apis.length > 0) {
        output += `🔗 API:\n${results.apis.map((api: any) => 
          `• ${api.path}: ${api.purpose}`
        ).join('\n')}\n\n`
      }
      
      if (results.commands.length > 0) {
        output += `⚡ コマンド:\n${results.commands.map((cmd: any) => 
          `• ${cmd.command}: ${cmd.description}`
        ).join('\n')}\n\n`
      }
      
      if (Object.keys(results.tasks).length > 0) {
        output += `📋 タスク:\n${Object.entries(results.tasks).map(([key, task]: [string, any]) => 
          `• ${key}: ${task.description}`
        ).join('\n')}\n\n`
      }
      
      return output || `検索結果なし: "${query}"`
    } else {
      return `❌ 検索エラー: ${data.error}`
    }
  } catch (error) {
    return `❌ 検索失敗: ${error}`
  }
}

// ペルソナシステム管理
async function getPersonaSystem(type?: string): Promise<string> {
  try {
    const response = await fetch('/api/persona-system')
    const data = await response.json()
    
    if (data.success) {
      if (type === 'themes') {
        return `🎭 利用可能なテーマ:\n${data.themes.map((theme: any) => 
          `• ${theme.name}: ${theme.description}\n  世界観: ${theme.worldView}`
        ).join('\n\n')}`
      } else if (type === 'current') {
        const current = data.currentTheme
        return `🎯 現在のテーマ: ${current.name}\n${current.description}\n\n階層構造:\n${current.roles.map((role: any) => 
          `${role.level}. ${role.emoji} ${role.name} (${role.title})\n   ${role.description}`
        ).join('\n\n')}`
      } else {
        return `🎭 エージェントペルソナシステム\n\n現在のテーマ: ${data.currentTheme.name}\n利用可能テーマ数: ${data.themes.length}個\n\nコマンド:\n• persona themes - テーマ一覧\n• persona current - 現在の設定\n• persona-set [テーマID] - テーマ変更`
      }
    } else {
      return `❌ ペルソナシステムエラー: ${data.error}`
    }
  } catch (error) {
    return `❌ ペルソナシステム取得失敗: ${error}`
  }
}

async function setPersonaTheme(themeId: string): Promise<string> {
  if (!themeId) {
    return `使用法: persona-set [テーマID]\n\n利用可能なテーマ:\n• feudal - 戦国時代風\n• business - モダンビジネス風\n• family - 家族風`
  }
  
  try {
    const response = await fetch('/api/persona-system', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'setTheme', themeId })
    })
    
    const data = await response.json()
    
    if (data.success) {
      return `✅ ペルソナテーマを変更しました\n\n新しいテーマ: ${data.theme.name}\n${data.theme.description}\n\n階層構造:\n${data.theme.roles.map((role: any) => 
        `${role.level}. ${role.emoji} ${role.name} - ${role.description}`
      ).join('\n')}`
    } else {
      return `❌ テーマ変更エラー: ${data.error}`
    }
  } catch (error) {
    return `❌ テーマ変更失敗: ${error}`
  }
}

// 母AIとの対話（APIルート経由）
export async function chatWithMother(message: string, context?: any) {
  try {
    const response = await fetch('/api/ai/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message,
        context,
        agent: 'mother'
      }),
    });

    if (!response.ok) {
      throw new Error('API request failed');
    }

    const data = await response.json();
    return data.response;
  } catch (error) {
    console.error('Mother AI Error:', error);
    return 'システムエラーが発生しました。しばらく後でお試しください。';
  }
}

// px CLIコマンド処理（統合型・Projext機能内蔵・Claude Code統合）
export async function processPxCommand(command: string): Promise<string> {
  const [cmd, ...args] = command.split(' ');
  
  try {
    switch (cmd) {
      case 'px':
        return await handlePxCommand(args);
      case 'claude':
      case 'claude-api':
      case 'claude-cli':
        return await handleClaudeCommand(args, cmd);
      case 'cost':
        return await getApiCost();
      case 'auth':
      case 'login':
        return await handleClaudeCodeAuth();
      case 'logout':
        return await handleClaudeCodeLogout();
      case 'export-claude-config':
        return await exportClaudeConfig();
      case 'import-claude-config':
        return await importClaudeConfig(args.join(' '));
      case 'claude-permission':
        return await setClaudePermission(args[0], args[1] === 'true');
      case 'claude-permissions':
        return await getClaudePermissions();
      case 'claude-allow':
        return await claudeAllow(args[0] || '.', true);
      case 'claude-config':
        return await getClaudeConfig();
      case 'ref':
      case 'reference':
        return await getQuickReference(args[0]);
      case 'search':
        return await searchReference(args.join(' '));
      case 'persona':
      case 'personas':
        return await getPersonaSystem(args[0]);
      case 'persona-set':
        return await setPersonaTheme(args[0]);
      case 'help':
        return getPxHelp();
      case 'status':
        return getSystemStatus();
      case 'agents':
        return getAgentStatus();
      case 'projext':
        return await handleProjectextCommand(args);
      default:
        // 自然言語として Claude に送信
        return await handleClaudeCommand([command]);
    }
  } catch (error) {
    return `Error: ${error}`;
  }
}

// ストリーミング対応のpx CLIコマンド処理
export async function processPxCommandStreaming(command: string, terminal: any): Promise<string> {
  const [cmd, ...args] = command.split(' ');
  
  try {
    switch (cmd) {
      case 'claude':
      case 'claude-api':
      case 'claude-cli':
        return await handleClaudeCommand(args, cmd, terminal);
      default:
        // 自然言語として Claude に送信（ストリーミング）
        return await handleClaudeCommand([command], 'claude', terminal);
    }
  } catch (error) {
    return `Error: ${error}`;
  }
}

async function handlePxCommand(args: string[]): Promise<string> {
  if (args.length === 0) {
    return getPxHelp();
  }
  
  const [subCommand, ...subArgs] = args;
  
  switch (subCommand) {
    case 'init':
      return await initializeProjectext(subArgs[0] || 'new-project');
    case 'generate':
      return await generateProjectext(subArgs.join(' '));
    case 'deploy':
      return await deployProject(subArgs[0]);
    case 'agents':
      return await manageAgents(subArgs);
    case 'run':
      return await executeProjectTask(subArgs.join(' '));
    default:
      return `Unknown px command: ${subCommand}. Type 'px help' for available commands.`;
  }
}

async function handleProjectextCommand(args: string[]): Promise<string> {
  const [action, ...params] = args;
  
  switch (action) {
    case 'create':
      return await createProjectext(params.join(' '));
    case 'list':
      return await listProjectexts();
    case 'show':
      return await showProjectext(params[0]);
    case 'delete':
      return await deleteProjectext(params[0]);
    default:
      return `Unknown projext command: ${action}`;
  }
}

async function initializeProjectext(name: string): Promise<string> {
  const timestamp = new Date().toISOString();
  
  return `
╭─ Projext Initialization ─────────────────────────
├─ Project: ${name}
├─ Timestamp: ${timestamp}
├─ Structure:
│  ├─ 📋 vision.md (ビジョン・目標設定)
│  ├─ 📝 requirements.yaml (機能要件定義)
│  ├─ 🎨 design.json (設計仕様)
│  ├─ 🤖 agents.config (エージェント配置)
│  └─ 📦 artifacts/ (成果物管理)
├─ Agents:
│  ├─ 👑 神 (God): 全体統括・意思決定
│  ├─ 🏰 王 (King): プロジェクト管理・調整
│  ├─ ⚔️ 将 (General): 開発チーム統率
│  └─ 🛡️ 兵 (Soldier): 実装・テスト実行
╰─────────────────────────────────────────────────

✅ Projext "${name}" initialized successfully!
🚀 Next: px generate "your project idea"
`;
}

async function generateProjectext(idea: string): Promise<string> {
  if (!idea) {
    return '❌ Error: Project idea is required. Usage: px generate "meditation app with custom timer"';
  }
  
  const projectType = detectProjectType(idea);
  const requirements = extractRequirements(idea);
  
  return `╭─ AI-Driven Projext Generation ─────────────────────────────╮
│                                                             │
│  Input: "${idea}"
│  Detected Type: ${projectType}
│                                                             │
│  Requirements Generated:
${requirements.map(req => `│    ✓ ${req}`).join('\n')}
│                                                             │
│  Agents Assigned:
│    👑 神: ビジョン策定・最終承認
│    🏰 王: プロジェクト計画・リソース配分  
│    ⚔️ 将: 開発戦略・技術選定
│    🛡️ 兵: コード実装・テスト実行
│                                                             │
│  Estimated Timeline: ${estimateTimeline(requirements)}
│                                                             │
╰─────────────────────────────────────────────────────────────╯

🎯 Projext generation complete!
📋 Files created in projext/ directory
🤖 Agents standing by for deployment`;
}

function detectProjectType(idea: string): string {
  const lower = idea.toLowerCase();
  if (lower.includes('app') || lower.includes('mobile') || lower.includes('アプリ')) return 'Mobile App';
  if (lower.includes('web') || lower.includes('site') || lower.includes('サイト')) return 'Web Application';
  if (lower.includes('ai') || lower.includes('bot') || lower.includes('機械学習')) return 'AI/ML Project';
  if (lower.includes('game') || lower.includes('ゲーム')) return 'Game Development';
  return 'General Software';
}

function extractRequirements(idea: string): string[] {
  const requirements = ['基本機能実装', 'UI/UXデザイン', 'テスト実装'];
  
  if (idea.includes('timer') || idea.includes('タイマー')) requirements.push('タイマー機能');
  if (idea.includes('custom') || idea.includes('カスタム')) requirements.push('カスタマイゼーション機能');
  if (idea.includes('meditation') || idea.includes('瞑想')) requirements.push('瞑想ガイド機能');
  if (idea.includes('voice') || idea.includes('音声')) requirements.push('音声機能');
  if (idea.includes('real-time') || idea.includes('リアルタイム')) requirements.push('リアルタイム処理');
  
  return requirements;
}

function estimateTimeline(requirements: string[]): string {
  const baseWeeks = 2;
  const additionalWeeks = Math.floor(requirements.length / 3);
  const totalWeeks = baseWeeks + additionalWeeks;
  
  return `${totalWeeks}週間 (${requirements.length}機能)`;
}

async function getAgentStatus(): Promise<string> {
  return `
╭─ エージェント状態 ────────────────────────────────
├─ 👑 神 (God): 稼働中 - 戦略的監督
│  └─ 現在: ビジョン検証・最終承認
├─ 🏰 王 (King): 稼働中 - プロジェクト調整  
│  └─ 現在: リソース配分・タイムライン管理
├─ ⚔️ 将 (General): 待機中 - 開発リーダーシップ
│  └─ 準備完了: アーキテクチャ設計・チーム調整
├─ 🛡️ 兵 (Soldier): 待機中 - 実装準備完了
│  └─ 準備完了: コード実行・テスト
├─ システム負荷: 最適
├─ 利用可能コア: 4/4
├─ メモリ使用量: 1.2GB / 8GB
╰───────────────────────────────────────────────────
`;
}

function getSystemStatus(): Promise<string> {
  return Promise.resolve(`
╭─ Meta Studio システム状態 ──────────────────────
├─ 🟢 ターミナル: xterm.js + Claude Code SDK
├─ 🟢 インボックス: AI振り分け + Projext生成  
├─ 🟢 エージェント: 4階層システム稼働中
├─ 🟢 VRM: キャラクター同期 + 音声統合
├─ 🟡 ブラウザ: iframeサンドボックス (フェーズ1)
├─ 🔴 YOYO: バージョン管理 (実装中)
├─ 
├─ 現在のセッション:
│  ├─ アクティブタブ: 3
│  ├─ 実行中プロジェクト: 1  
│  ├─ 保留タスク: 7
│  └─ Projexts: 2個稼働中
╰───────────────────────────────────────────────────
`);
}

function getPxHelp(): string {
  return `╭─ Meta Studio CLI コマンド一覧 ──────────────────────────────╮
│                                                             │
│  🤖 CLAUDE CODE (Max Plan + API デュアルサポート)          │
│  claude [プロンプト]      Max Plan CLI (コスト0)           │
│  claude-api [プロンプト]  API SDK (従量課金)               │
│  claude-cli [プロンプト]  CLI強制実行                      │
│  cost                     API使用料確認                    │
│  auth / login             Max Plan認証設定                │
│  logout                   Max Plan認証解除                │
│                                                             │
│  ⚙️  CLAUDE 設定管理                                       │
│  export-claude-config     設定をJSONファイルにエクスポート  │
│  import-claude-config     JSONファイルから設定をインポート │
│  claude-permission [path] パーミッション許可設定           │
│  claude-permissions       現在のパーミッション設定表示     │
│  claude-allow [path]      パスの自動承認を有効化           │
│  claude-config            現在の設定を表示                 │
│                                                             │
│  📚 リファレンス・検索                                     │
│  ref / reference [type]   クイックリファレンス表示         │
│    - components: コンポーネント一覧                        │
│    - apis: APIエンドポイント一覧                           │
│    - commands: コマンド一覧                                │
│    - tasks: 頻出タスク一覧                                 │
│  search [keyword]         機能・コマンド検索               │
│                                                             │
│  🎭 ペルソナシステム                                       │
│  persona / personas       ペルソナシステム表示             │
│    - themes: テーマ一覧                                    │
│    - current: 現在の設定                                   │
│  persona-set [id]         テーマ変更                       │
│    - feudal: 戦国時代風                                    │
│    - business: ビジネス風                                  │
│    - family: 家族風                                        │
│                                                             │
│  📋 PROJEXT コマンド                                       │
│  px init [名前]           新規プロジェクスト作成           │
│  px generate "アイデア"   AI自動プロジェクト構造生成       │
│                                                             │
│  📂 基本コマンド                                           │
│  help                     このヘルプを表示                 │
│  status                   システム状態表示                 │
│  agents                   エージェント階層状態表示         │
│  ls                       ファイル一覧表示                 │
│  pwd                      現在のディレクトリ表示           │
│  date                     現在の日時表示                   │
│                                                             │
│  💰 コスト追跡                                             │
│  cost                     API使用料・セッション集計        │
│                                                             │
│  ⚠️  必要な設定                                             │
│  • Max Plan: claude /logout && claude で認証              │
│  • API Mode: ANTHROPIC_API_KEY environment variable        │
│                                                             │
╰─────────────────────────────────────────────────────────────╯

🎯 Meta Studio Terminal - Max Plan認証対応完了`;
}

async function simulateTerminalCommand(command: string): Promise<string> {
  const simulations = {
    'ls': 'projext/  templates/  agents/  artifacts/',
    'pwd': '/Users/<USER>/workspace',
    'whoami': 'meta-studio-developer',
    'date': new Date().toISOString(),
    'clear': '\x1b[2J\x1b[H',
  };
  
  return simulations[command as keyof typeof simulations] || `Command not found: ${command}`;
}

async function createProjectext(name: string): Promise<string> {
  return `📋 Creating projext: ${name}...`;
}

async function listProjectexts(): Promise<string> {
  return `📋 Available projexts:\n- meditation-app\n- investment-bot\n- vtuber-studio`;
}

async function showProjectext(name: string): Promise<string> {
  return `📋 Projext details: ${name}`;
}

async function deleteProjectext(name: string): Promise<string> {
  return `🗑️ Deleted projext: ${name}`;
}

async function manageAgents(args: string[]): Promise<string> {
  return `🤖 Agent management: ${args.join(' ')}`;
}

async function executeProjectTask(task: string): Promise<string> {
  return `🚀 Executing: ${task}`;
}

async function deployProject(target: string): Promise<string> {
  return `🚀 Deploying to: ${target}`;
}

// Claude Code ストリーミング処理
async function handleClaudeApiStreaming(prompt: string, terminal: any): Promise<string> {
  try {
    const response = await fetch('/api/claude-code', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        prompt,
        maxTurns: 3,
        streaming: true
      })
    });

    if (!response.body) {
      throw new Error('No response body');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let finalResult = '';
    let startTime = Date.now();

    // プログレス表示をクリア
    terminal.write('\r\x1b[K');
    
    // 初期状態表示
    terminal.writeln('🔄 Claude思考中...');
    terminal.write('📊 処理状況: ');
    const statusLine = terminal.buffer.active.cursorY;

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              const elapsed = Math.round((Date.now() - startTime) / 1000);
              
              // ステータス行に戻って更新
              terminal.write(`\x1b[${statusLine + 1};1H\x1b[K`);
              
              switch (data.type) {
                case 'status':
                  terminal.write(`🟡 初期化中... (${elapsed}s)`);
                  break;
                  
                case 'update':
                  switch (data.status) {
                    case 'initializing':
                      terminal.write(`🔵 セッション開始... (${elapsed}s)`);
                      break;
                    case 'generating':
                      const tokens = data.tokens || { input: 0, output: 0 };
                      terminal.write(`🟢 生成中... (${elapsed}s) [入力:${tokens.input} 出力:${tokens.output}]`);
                      break;
                    case 'completed':
                      terminal.write(`✅ 完了! (${elapsed}s) [コスト: $${(data.cost || 0).toFixed(4)}]`);
                      finalResult = data.result || '';
                      break;
                  }
                  break;
                  
                case 'complete':
                  terminal.write(`\n✨ 処理完了 (${elapsed}s)\n`);
                  break;
                  
                case 'error':
                  terminal.write(`\n❌ エラー: ${data.error}\n`);
                  return `エラーが発生しました: ${data.error}`;
              }
            } catch (e) {
              // JSON解析エラーは無視
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    // 最終結果を表示
    if (finalResult) {
      terminal.writeln('\n');
      const lines = finalResult.split('\n');
      lines.forEach(line => {
        terminal.writeln(`${line}`);
      });
      return finalResult;
    }

    return 'ストリーミング処理が完了しましたが、結果の取得に失敗しました。';
    
  } catch (error) {
    terminal.writeln(`\n❌ ストリーミングエラー: ${error}`);
    return `ストリーミングエラー: ${error}`;
  }
}

// Claude Code統合機能（API + CLI デュアルサポート）
async function handleClaudeCommand(args: string[], commandType: string = 'claude', terminal?: any): Promise<string> {
  if (args.length === 0) {
    return getClaudeHelp();
  }
  
  // claude-api が明示的に指定された場合のみAPI経由
  if (commandType === 'claude-api') {
    return await handleClaudeApiCommand(args, terminal);
  }
  
  // Max Plan認証チェック後、適切な方法を選択
  try {
    // まずCLI認証状態を確認
    const cliResponse = await fetch('/api/claude-cli-check');
    const cliData = await cliResponse.json();
    
    if (cliData.authenticated) {
      // Max Plan認証済み - CLI使用
      return await handleClaudeCliCommand(args);
    } else {
      // Max Plan未認証 - API経由にフォールバック
      return await handleClaudeApiCommand(args, terminal);
    }
  } catch (error) {
    // CLI確認失敗時はAPI経由
    return await handleClaudeApiCommand(args, terminal);
  }
}

// Claude Code API経由の処理（従量課金）
async function handleClaudeApiCommand(args: string[], terminal?: any): Promise<string> {
  const prompt = args.join(' ');
  
  // ストリーミング対応の場合
  if (terminal) {
    return handleClaudeApiStreaming(prompt, terminal);
  }
  
  try {
    const response = await fetch('/api/claude-code', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        prompt,
        maxTurns: 3,
        streaming: false
      })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      if (data.needsApiKey) {
        return `╭─ Claude Code SDK - Authentication Required ─────╮
❌ ANTHROPIC_API_KEY not configured

To enable Claude Code SDK:
1. Get API key from: console.anthropic.com
2. Set environment variable:
   export ANTHROPIC_API_KEY="your-key"
3. Restart the server

📝 Your query: "${prompt}"
╰─────────────────────────────────────────────────╯`;
      }
      
      return `╭─ Claude Code SDK Error ─────────────────────╮
❌ Error: ${data.error}

📝 Your query: "${prompt}"
╰─────────────────────────────────────────────╯`;
    }
    
    // Claude Code SDKレスポンスから実際のClaude回答を抽出
    let claudeResponse = '';
    
    // resultフィールドから最終回答を取得
    const resultMessage = data.messages.find((msg: any) => msg.type === 'result');
    if (resultMessage && resultMessage.result) {
      claudeResponse = resultMessage.result;
    }
    
    // フォールバック: assistant messageから最後の応答を取得
    if (!claudeResponse) {
      const assistantMessages = data.messages.filter((msg: any) => 
        msg.type === 'assistant' && msg.message && msg.message.content
      );
      if (assistantMessages.length > 0) {
        const lastMessage = assistantMessages[assistantMessages.length - 1];
        if (lastMessage.message.content && Array.isArray(lastMessage.message.content)) {
          const textContent = lastMessage.message.content.find((item: any) => item.type === 'text');
          if (textContent && textContent.text) {
            claudeResponse = textContent.text;
          }
        }
      }
    }
    
    // 空の場合のデフォルト応答
    if (!claudeResponse) {
      claudeResponse = '応答を受信しましたが、内容の解析に問題が発生しました。';
    }
    
    // 改善されたレスポンス表示フォーマット（縦線なし、自動幅調整）
    const lines = claudeResponse.split('\n');
    const maxLength = Math.max(...lines.map(line => line.length), 20);
    const boxWidth = Math.min(maxLength + 8, 80); // 最大80文字幅
    const separator = '─'.repeat(boxWidth - 4);
    
    const formattedLines = lines.map(line => {
      if (line.trim() === '') return '';
      return `🤖 ${line}`;
    }).filter(line => line !== '');
    
    return `╭─${separator}╮
${formattedLines.join('\n')}
╰─${separator}╯`;
    
  } catch (error) {
    return `╭─ Claude Code SDK Error ──────────────────────────────╮
❌ Network Error: ${error}

📝 Your query: "${prompt}"

💡 Check server connection and try again
╰──────────────────────────────────────────────────╯`;
  }
}

function getClaudeHelp(): string {
  return `╭─ Claude Code コマンド ─────────────────────────────────────╮
│                                                             │
│  🤖 Claude Code デュアルサポート                          │
│                                                             │
│  メソッド:                                                  │
│    claude [プロンプト]           最適な方法を自動選択       │
│    claude-api [プロンプト]       API SDK強制使用           │
│    claude-cli [プロンプト]       CLI強制使用               │
│                                                             │
│  使用例:                                                    │
│    claude "Reactボタンコンポーネントを作成"                │
│    claude-api "UserProfile.tsxのバグを修正"                │
│    claude-cli "このコードをパフォーマンス最適化"           │
│    cost                          使用量とコストを表示      │
│                                                             │
│  API メソッド機能:                                          │
│    • マルチターン会話                                      │
│    • プロジェクトコンテキスト認識                          │
│    • セッション管理                                        │
│    • 詳細なコスト追跡                                      │
│                                                             │
│  CLI メソッド機能:                                          │
│    • 直接CLI アクセス                                      │
│    • 高速レスポンス                                        │
│    • ネイティブツール統合                                  │
│    • オフライン設定                                        │
│                                                             │
│  必要な設定:                                                │
│    • API: ANTHROPIC_API_KEY 環境変数                       │
│    • CLI: claude-code グローバルインストール               │
│                                                             │
│  🔗 Powered by @anthropic-ai/claude-code v1.0.25          │
│                                                             │
╰─────────────────────────────────────────────────────────────╯`;
}

// API コスト追跡機能
async function getApiCost(): Promise<string> {
  try {
    // ログファイルから最新のClaude Code SDK使用コスト情報を取得
    const response = await fetch('/api/claude-cost');
    const data = await response.json();
    
    if (!data.success) {
      return `╭─ API Cost Tracking Error ────────────────────╮
│                                               │
│  ❌ Error: ${data.error}
│                                               │
│  💡 Tip: API cost tracking requires          │
│      ANTHROPIC_API_KEY to be configured      │
│                                               │
╰───────────────────────────────────────────────╯`;
    }
    
    const { totalCost, sessionCount, todayCost, monthCost } = data;
    
    return `╭─ Claude Code API Usage & Costs ──────────────────╮
│ 💰 Current Session Total: $${totalCost.toFixed(4)} USD │
│ 📊 Sessions Today: ${sessionCount}                     │
│ 📅 Today's Cost: $${todayCost.toFixed(4)} USD         │
│ 📈 Month to Date: $${monthCost.toFixed(4)} USD        │
│                                                       │
│ 💡 Cost Breakdown:                                   │
│   • Input Tokens: Cache + Processing                 │
│   • Output Tokens: Generated responses               │
│   • Tool Usage: Web search, file operations          │
│                                                       │
│ 🔗 Detailed billing: console.anthropic.com/usage    │
╰───────────────────────────────────────────────────────╯`;
    
  } catch (error) {
    return `╭─ API Cost Tracking Error ────────────────────╮
│                                               │
│  ❌ Network Error: Unable to fetch cost data │
│                                               │
│  💡 Check server connection and try:         │
│    • Ensure ANTHROPIC_API_KEY is set        │
│    • Verify API endpoint is accessible       │
│                                               │
╰───────────────────────────────────────────────╯`;
  }
}

// Claude Code CLI経由の処理
async function handleClaudeCliCommand(args: string[]): Promise<string> {
  const prompt = args.join(' ');
  
  try {
    const response = await fetch('/api/claude-cli', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        prompt,
        command: 'query'
      })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      if (data.needsCli) {
        return `╭─ Claude Code CLI - Installation Required ────╮
│                                               │
│  ❌ Claude Code CLI not installed             │
│                                               │
│  To enable Claude Code CLI:                   │
│  1. Install: npm install -g @anthropic-ai/claude-code │
│  2. Max Plan Auth: claude /logout && claude  │
│  3. Test: claude "hello"                      │
│                                               │
│  📝 Your query: "${prompt}"
│                                               │
│  💡 Fallback: Use 'claude-api ${prompt}' instead │
│                                               │
╰───────────────────────────────────────────────╯`;
      }
      
      if (data.needsAuth) {
        return `╭─ Claude Code CLI - Max Plan Auth Required ───╮
│                                               │
│  ❌ Max Plan authentication required          │
│                                               │
│  To authenticate with Max Plan:               │
│  1. Logout: claude /logout                    │
│  2. Login: claude                             │
│  3. Select your Max Plan account              │
│                                               │
│  📝 Your query: "${prompt}"
│                                               │
│  💡 After auth, try: claude "${prompt}"      │
│                                               │
╰───────────────────────────────────────────────╯`;
      }
      
      return `╭─ Claude Code CLI Error ──────────────────────╮
│                                               │
│  ❌ Error: ${data.error}
│                                               │
│  📝 Your query: "${prompt}"
│                                               │
│  💡 Try: claude-api ${prompt}                │
│                                               │
╰───────────────────────────────────────────────╯`;
    }
    
    return `╭─ Claude CLI Response ────────────────────────╮
│                                               │
│  🤖 ${data.response.replace(/\n/g, ' ')}
│                                               │
│  ℹ️  Method: CLI (Max Plan)                  │
╰───────────────────────────────────────────────╯`;
    
  } catch (error) {
    return `╭─ Claude Code CLI Error ──────────────────────╮
│                                               │
│  ❌ Network Error: ${error}
│                                               │
│  📝 Your query: "${prompt}"
│                                               │
│  💡 Fallback: claude-api ${prompt}           │
│                                               │
╰───────────────────────────────────────────────╯`;
  }
}

// Claude Code CLI認証処理
async function handleClaudeCodeAuth(): Promise<string> {
  try {
    const response = await fetch('/api/claude-auth', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'login' })
    });
    
    const data = await response.json();
    
    if (data.requiresManualSetup) {
      const steps = data.instructions.map(step => `│  ${step}`).join('\n');
      
      return `╭─ Claude Code Max Plan認証設定手順 ──────────────╮
│                                                 │
│  🔧 Meta Studio外での手動設定が必要です        │
│                                                 │
${steps}
│                                                 │
│  📝 理由: ${data.reason}
│                                                 │
│  ✅ 設定完了後: claude "hello" でテスト         │
│                                                 │
╰─────────────────────────────────────────────────╯`;
    }
    
    if (data.success) {
      return `╭─ Max Plan認証済み ──────────────────────────────╮
│                                                 │
│  ✅ Max Plan authentication completed          │
│                                                 │
│  💡 You can now use:                           │
│     claude "your prompt"  (Max Plan - Free)    │
│     claude-api "prompt"   (API - Paid)         │
│                                                 │
╰─────────────────────────────────────────────────╯`;
    }
    
    return `╭─ Claude Code認証エラー ─────────────────────────╮
│                                                 │
│  ❌ ${data.error || 'Unknown error'}
│                                                 │
╰─────────────────────────────────────────────────╯`;
    
  } catch (error) {
    return `╭─ Claude Code認証エラー ─────────────────────────╮
│                                                 │
│  ❌ ネットワークエラー: ${error}
│                                                 │
╰─────────────────────────────────────────────────╯`;
  }
}

// Claude Code CLI認証をログアウト
async function handleClaudeCodeLogout(): Promise<string> {
  try {
    const response = await fetch('/api/claude-auth', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'logout' })
    });
    
    const data = await response.json();
    
    if (data.success) {
      return `╭─ Max Plan Logout Complete ──────────────────────╮
│                                                  │
│  ✅ Successfully logged out from Max Plan       │
│                                                  │
│  🔄 Claude commands will now use API mode       │
│     (requires ANTHROPIC_API_KEY)                │
│                                                  │
│  💡 To login again: auth                        │
│                                                  │
╰──────────────────────────────────────────────────╯`;
    } else {
      return `╭─ Logout Error ──────────────────────────────────╮
│                                                  │
│  ❌ Logout failed: ${data.error}
│                                                  │
╰──────────────────────────────────────────────────╯`;
    }
    
  } catch (error) {
    return `╭─ Logout Error ──────────────────────────────────╮
│                                                  │
│  ❌ Network error: ${error}
│                                                  │
╰──────────────────────────────────────────────────╯`;
  }
}