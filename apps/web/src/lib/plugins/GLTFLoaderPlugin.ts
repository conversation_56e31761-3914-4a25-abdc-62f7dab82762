/**
 * GLTFローダープラグイン
 * GLTF/GLB形式のモデルを統一インターフェースで読み込む
 */

import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { 
  UniversalModel, 
  ModelFormat, 
  ModelType, 
  ModelValidationResult,
  ModelLoaderOptions,
  ModelCapabilities,
  ModelMetadata
} from '@/types/UniversalModel';
import { ModelLoaderPlugin, ModelLoaderUtils } from '@/lib/UniversalModelLoader';

export class GLTFLoaderPlugin implements ModelLoaderPlugin {
  format = ModelFormat.GLTF;
  supportedExtensions = ['.gltf', '.glb'];
  mimeTypes = ['model/gltf+json', 'model/gltf-binary', 'application/octet-stream'];
  
  /**
   * GLTFファイルの検証
   */
  validate(data: ArrayBuffer, filename: string): ModelValidationResult {
    const header = ModelLoaderUtils.analyzeFileHeader(data);
    const ext = filename.toLowerCase();
    
    let confidence = 0;
    const issues: string[] = [];
    
    // 拡張子チェック
    if (ext.endsWith('.gltf') || ext.endsWith('.glb')) {
      confidence += 0.4;
    }
    
    // ファイルヘッダーチェック
    if (header.isGLTF && header.isBinary) {
      confidence += 0.4; // GLBファイル
    } else if (ext.endsWith('.gltf')) {
      // GLTFファイル（JSON形式）
      try {
        const jsonText = new TextDecoder().decode(data);
        const jsonData = JSON.parse(jsonText);
        
        if (jsonData.asset && jsonData.asset.version) {
          confidence += 0.3;
          
          // glTFバージョンチェック
          const version = parseFloat(jsonData.asset.version);
          if (version >= 2.0) {
            confidence += 0.2;
          } else {
            issues.push('glTF 1.0は非推奨です。glTF 2.0への更新を推奨します。');
          }
        }
        
        if (jsonData.scenes && jsonData.nodes) {
          confidence += 0.1;
        }
        
      } catch (error) {
        issues.push('GLTFファイルの解析に失敗');
        confidence -= 0.2;
      }
    }
    
    // ファイルサイズチェック
    if (data.byteLength < 100) {
      issues.push('ファイルサイズが小さすぎます');
      confidence -= 0.2;
    } else if (data.byteLength > 200 * 1024 * 1024) {
      issues.push('ファイルサイズが大きすぎます (200MB超)');
    }
    
    return {
      isValid: confidence > 0.5,
      format: ModelFormat.GLTF,
      confidence: Math.min(confidence, 1.0),
      issues: issues.length > 0 ? issues : undefined
    };
  }
  
  /**
   * GLTFモデルの読み込み
   */
  async load(
    data: ArrayBuffer, 
    filename: string, 
    options?: ModelLoaderOptions
  ): Promise<UniversalModel> {
    console.log(`📦 GLTFモデル読み込み開始: ${filename}`);
    
    try {
      const gltfLoader = new GLTFLoader();
      
      // ArrayBufferからBlobを作成
      const blob = new Blob([data], { type: 'application/octet-stream' });
      const url = URL.createObjectURL(blob);
      
      let gltfData: any;
      try {
        gltfData = await new Promise((resolve, reject) => {
          gltfLoader.load(
            url,
            (gltf) => {
              console.log(`✅ GLTF読み込み成功: ${filename}`);
              resolve(gltf);
            },
            (progress) => {
              const percent = Math.round((progress.loaded / progress.total) * 100);
              console.log(`📊 GLTF読み込み進捗: ${percent}%`);
            },
            (error) => {
              console.error(`❌ GLTF読み込みエラー:`, error);
              reject(error);
            }
          );
        });
      } finally {
        URL.revokeObjectURL(url);
      }
      
      // GLTFメタデータを抽出
      const metadata = this.extractGLTFMetadata(gltfData, filename, data.byteLength);
      
      // GLTF機能を分析
      const capabilities = this.analyzeGLTFCapabilities(gltfData);
      
      // アニメーションミキサーを作成
      let mixer: THREE.AnimationMixer | undefined;
      if (gltfData.animations && gltfData.animations.length > 0) {
        mixer = new THREE.AnimationMixer(gltfData.scene);
      }
      
      // 統一モデルオブジェクトを作成
      const model: UniversalModel = {
        id: ModelLoaderUtils.generateModelId(),
        name: metadata.title || filename.replace(/\.[^/.]+$/, ''),
        type: this.determineModelType(gltfData),
        format: ModelFormat.GLTF,
        data: {
          scene: gltfData.scene,
          animations: gltfData.animations || [],
          mixer: mixer,
          gltf: gltfData
        },
        capabilities,
        metadata,
        isLoaded: true,
        isVisible: true,
        isAnimating: false,
        fileName: filename,
        fileSize: data.byteLength,
        arrayBuffer: data,
        boundingBox: new THREE.Box3().setFromObject(gltfData.scene),
        position: new THREE.Vector3(0, 0, 0),
        rotation: new THREE.Euler(0, 0, 0),
        scale: new THREE.Vector3(1, 1, 1)
      };
      
      console.log(`📦 GLTFモデル読み込み完了:`, {
        name: model.name,
        type: model.type,
        animations: gltfData.animations?.length || 0,
        capabilities: capabilities
      });
      
      return model;
      
    } catch (error) {
      console.error(`❌ GLTFモデル読み込みエラー: ${filename}`, error);
      throw error;
    }
  }
  
  /**
   * デフォルトのGLTF機能
   */
  getDefaultCapabilities(): ModelCapabilities {
    return {
      expressions: false, // 標準GLTFは表情制御なし
      animations: true,
      physics: false,    // 標準GLTFは物理演算なし
      lipSync: false,    // 標準GLTFはリップシンクなし
      eyeTracking: false, // 標準GLTFは視線追跡なし
      blinking: false,   // 標準GLTFは自動まばたきなし
      morphTargets: true,
      bones: true,
      materials: true
    };
  }
  
  /**
   * GLTFメタデータを抽出
   */
  private extractGLTFMetadata(gltfData: any, filename: string, fileSize: number): ModelMetadata {
    const asset = gltfData.asset || {};
    const extras = gltfData.extras || {};
    
    return {
      title: extras.title || asset.title || filename.replace(/\.[^/.]+$/, ''),
      author: extras.author || asset.author || 'Unknown',
      version: asset.version || '2.0',
      description: extras.description || asset.description || '',
      license: extras.license || 'Unknown',
      fileSize,
      // GLTF固有のメタデータ
      gltfVersion: asset.version || '2.0',
      generator: asset.generator || 'Unknown',
      copyright: asset.copyright || '',
      sceneCount: gltfData.scenes?.length || 0,
      nodeCount: gltfData.nodes?.length || 0,
      meshCount: gltfData.meshes?.length || 0,
      materialCount: gltfData.materials?.length || 0,
      textureCount: gltfData.textures?.length || 0,
      animationCount: gltfData.animations?.length || 0
    };
  }
  
  /**
   * GLTF機能を分析
   */
  private analyzeGLTFCapabilities(gltfData: any): ModelCapabilities {
    const capabilities = this.getDefaultCapabilities();
    
    try {
      // アニメーション機能をチェック
      capabilities.animations = (gltfData.animations?.length || 0) > 0;
      
      // モーフターゲット機能をチェック
      if (gltfData.meshes) {
        for (const mesh of gltfData.meshes) {
          if (mesh.primitives) {
            for (const primitive of mesh.primitives) {
              if (primitive.targets && primitive.targets.length > 0) {
                capabilities.morphTargets = true;
                break;
              }
            }
          }
        }
      }
      
      // ボーン機能をチェック
      if (gltfData.skins && gltfData.skins.length > 0) {
        capabilities.bones = true;
      }
      
      // マテリアル機能をチェック
      capabilities.materials = (gltfData.materials?.length || 0) > 0;
      
      console.log('🔍 GLTF機能分析完了:', capabilities);
      
    } catch (error) {
      console.warn('⚠️ GLTF機能分析中にエラー:', error);
    }
    
    return capabilities;
  }
  
  /**
   * モデルタイプを判定
   */
  private determineModelType(gltfData: any): ModelType {
    // ヒューマノイド的な構造があるかチェック
    if (gltfData.skins && gltfData.skins.length > 0) {
      // ボーンがある場合はアバターの可能性が高い
      return ModelType.AVATAR;
    }
    
    // アニメーションがある場合
    if (gltfData.animations && gltfData.animations.length > 0) {
      return ModelType.AVATAR;
    }
    
    // その他は小道具として扱う
    return ModelType.PROP;
  }
}

// GLBローダープラグイン（GLTFの派生）
export class GLBLoaderPlugin extends GLTFLoaderPlugin {
  format = ModelFormat.GLB;
  supportedExtensions = ['.glb'];
  mimeTypes = ['model/gltf-binary', 'application/octet-stream'];
  
  validate(data: ArrayBuffer, filename: string): ModelValidationResult {
    const result = super.validate(data, filename);
    result.format = ModelFormat.GLB;
    
    // GLBファイルの場合はバイナリヘッダーをより厳密にチェック
    const header = ModelLoaderUtils.analyzeFileHeader(data);
    if (header.isBinary && header.magic === 'glTF') {
      result.confidence = Math.min(result.confidence + 0.2, 1.0);
    }
    
    return result;
  }
}
