/**
 * VRMローダープラグイン
 * VRM形式のモデルを統一インターフェースで読み込む
 */

import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { 
  UniversalModel, 
  ModelFormat, 
  ModelType, 
  ModelValidationResult,
  ModelLoaderOptions,
  ModelCapabilities,
  ModelMetadata
} from '@/types/UniversalModel';
import { ModelLoaderPlugin, ModelLoaderUtils } from '@/lib/UniversalModelLoader';

// VRMローダーのキャッシュ
let VRMLoader_CACHE: any = null;

export class VRMLoaderPlugin implements ModelLoaderPlugin {
  format = ModelFormat.VRM;
  supportedExtensions = ['.vrm'];
  mimeTypes = ['application/octet-stream', 'model/gltf-binary'];
  
  /**
   * VRMファイルの検証
   */
  validate(data: ArrayBuffer, filename: string): ModelValidationResult {
    const header = ModelLoaderUtils.analyzeFileHeader(data);
    const ext = filename.toLowerCase();
    
    let confidence = 0;
    const issues: string[] = [];
    
    // 拡張子チェック
    if (ext.endsWith('.vrm')) {
      confidence += 0.4;
    }
    
    // ファイルヘッダーチェック
    if (header.isGLTF && header.isBinary) {
      confidence += 0.3;
    } else if (!header.isGLTF) {
      issues.push('GLTFヘッダーが見つかりません');
    }
    
    // ファイルサイズチェック
    if (data.byteLength < 1000) {
      issues.push('ファイルサイズが小さすぎます');
      confidence -= 0.2;
    } else if (data.byteLength > 100 * 1024 * 1024) {
      issues.push('ファイルサイズが大きすぎます (100MB超)');
    }
    
    // VRM固有のチェック（簡易）
    const dataView = new DataView(data);
    try {
      // GLTFのJSON部分をチェック
      if (header.isBinary && data.byteLength > 20) {
        const jsonLength = dataView.getUint32(12, true);
        if (jsonLength > 0 && jsonLength < data.byteLength) {
          const jsonBytes = new Uint8Array(data, 20, Math.min(jsonLength, 1000));
          const jsonText = new TextDecoder().decode(jsonBytes);
          
          if (jsonText.includes('VRM') || jsonText.includes('vrm')) {
            confidence += 0.3;
          }
        }
      }
    } catch (error) {
      issues.push('GLTFデータの解析に失敗');
    }
    
    return {
      isValid: confidence > 0.5,
      format: ModelFormat.VRM,
      confidence: Math.min(confidence, 1.0),
      issues: issues.length > 0 ? issues : undefined
    };
  }
  
  /**
   * VRMモデルの読み込み
   */
  async load(
    data: ArrayBuffer, 
    filename: string, 
    options?: ModelLoaderOptions
  ): Promise<UniversalModel> {
    console.log(`🎭 VRMモデル読み込み開始: ${filename}`);
    
    try {
      // VRMローダーを初期化
      const vrmModule = await this.initializeVRMLoader();
      const { VRMLoaderPlugin, VRMUtils } = vrmModule;
      
      // GLTFローダーを設定
      const gltfLoader = new GLTFLoader();
      gltfLoader.register((parser) => new VRMLoaderPlugin(parser));
      
      // ArrayBufferからBlobを作成
      const blob = new Blob([data], { type: 'application/octet-stream' });
      const url = URL.createObjectURL(blob);
      
      let gltfData: any;
      try {
        // VRMファイルを読み込み
        gltfData = await new Promise((resolve, reject) => {
          gltfLoader.load(
            url,
            (gltf) => {
              console.log(`✅ VRM GLTF読み込み成功: ${filename}`);
              resolve(gltf);
            },
            (progress) => {
              const percent = Math.round((progress.loaded / progress.total) * 100);
              console.log(`📊 VRM読み込み進捗: ${percent}%`);
            },
            (error) => {
              console.error(`❌ VRM GLTF読み込みエラー:`, error);
              reject(error);
            }
          );
        });
      } finally {
        URL.revokeObjectURL(url);
      }
      
      // VRMデータを抽出
      const vrm = gltfData.userData?.vrm;
      const scene = gltfData.scene;
      
      if (!vrm) {
        throw new Error('VRMデータが見つかりません。標準glTFファイルの可能性があります。');
      }
      
      // VRMメタデータを取得
      const metadata = this.extractVRMMetadata(vrm, filename, data.byteLength);
      
      // VRM機能を分析
      const capabilities = this.analyzeVRMCapabilities(vrm);
      
      // 統一モデルオブジェクトを作成
      const model: UniversalModel = {
        id: ModelLoaderUtils.generateModelId(),
        name: metadata.title || filename.replace(/\.[^/.]+$/, ''),
        type: ModelType.AVATAR,
        format: ModelFormat.VRM,
        data: {
          vrm,
          scene,
          animations: gltfData.animations || [],
          mixer: vrm.scene ? new THREE.AnimationMixer(vrm.scene) : undefined
        },
        capabilities,
        metadata,
        isLoaded: true,
        isVisible: true,
        isAnimating: false,
        fileName: filename,
        fileSize: data.byteLength,
        arrayBuffer: data,
        boundingBox: new THREE.Box3().setFromObject(scene),
        position: new THREE.Vector3(0, 0, 0),
        rotation: new THREE.Euler(0, 0, 0),
        scale: new THREE.Vector3(1, 1, 1)
      };
      
      console.log(`🎭 VRMモデル読み込み完了:`, {
        name: model.name,
        author: metadata.author,
        version: metadata.version,
        capabilities: capabilities
      });
      
      return model;
      
    } catch (error) {
      console.error(`❌ VRMモデル読み込みエラー: ${filename}`, error);
      throw error;
    }
  }
  
  /**
   * デフォルトのVRM機能
   */
  getDefaultCapabilities(): ModelCapabilities {
    return {
      expressions: true,
      animations: true,
      physics: true,
      lipSync: true,
      eyeTracking: true,
      blinking: true,
      morphTargets: true,
      bones: true,
      materials: true
    };
  }
  
  /**
   * VRMローダーを初期化
   */
  private async initializeVRMLoader(): Promise<any> {
    if (!VRMLoader_CACHE) {
      console.log('📦 VRMローダーモジュールを読み込み中...');
      VRMLoader_CACHE = await import('@pixiv/three-vrm');
      console.log('✅ VRMローダーモジュール読み込み完了');
    }
    return VRMLoader_CACHE;
  }
  
  /**
   * VRMメタデータを抽出
   */
  private extractVRMMetadata(vrm: any, filename: string, fileSize: number): ModelMetadata {
    const meta = vrm.meta || {};
    
    return {
      title: meta.title || filename.replace(/\.[^/.]+$/, ''),
      author: meta.author || 'Unknown',
      version: meta.version || '1.0',
      description: meta.description || '',
      license: meta.licenseName || meta.license || 'Unknown',
      thumbnail: meta.thumbnail || undefined,
      tags: meta.tags || [],
      fileSize,
      // VRM固有のメタデータ
      vrmVersion: meta.specVersion || '1.0',
      allowedUser: meta.allowedUserName || 'Everyone',
      violentUsage: meta.violentUssageName || 'Disallow',
      sexualUsage: meta.sexualUssageName || 'Disallow',
      commercialUsage: meta.commercialUssageName || 'Disallow',
      otherPermissionUrl: meta.otherPermissionUrl || '',
      otherLicenseUrl: meta.otherLicenseUrl || ''
    };
  }
  
  /**
   * VRM機能を分析
   */
  private analyzeVRMCapabilities(vrm: any): ModelCapabilities {
    const capabilities = this.getDefaultCapabilities();
    
    try {
      // 表情機能をチェック
      if (vrm.expressionManager) {
        const expressions = vrm.expressionManager.expressions || {};
        capabilities.expressions = Object.keys(expressions).length > 0;
      }
      
      // アニメーション機能をチェック
      if (vrm.scene) {
        capabilities.animations = true;
      }
      
      // ヒューマノイド機能をチェック
      if (vrm.humanoid) {
        capabilities.bones = true;
      }
      
      // 物理演算機能をチェック
      if (vrm.springBoneManager) {
        capabilities.physics = true;
      }
      
      console.log('🔍 VRM機能分析完了:', capabilities);
      
    } catch (error) {
      console.warn('⚠️ VRM機能分析中にエラー:', error);
    }
    
    return capabilities;
  }
}
