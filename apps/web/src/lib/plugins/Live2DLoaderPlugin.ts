/**
 * LIVE2Dローダープラグイン
 * LIVE2D Cubism形式のモデルを統一インターフェースで読み込む
 */

import * as THREE from 'three';
import { 
  UniversalModel, 
  ModelFormat, 
  ModelType, 
  ModelValidationResult,
  ModelLoaderOptions,
  ModelCapabilities,
  ModelMetadata
} from '@/types/UniversalModel';
import { ModelLoaderPlugin, ModelLoaderUtils } from '@/lib/UniversalModelLoader';

// LIVE2D Cubism SDKのキャッシュ
let Live2DCubismSDK_CACHE: any = null;

export class Live2DLoaderPlugin implements ModelLoaderPlugin {
  format = ModelFormat.LIVE2D;
  supportedExtensions = ['.model3.json', '.moc3', '.model.json'];
  mimeTypes = ['application/json', 'application/octet-stream'];
  
  /**
   * LIVE2Dファイルの検証
   */
  validate(data: ArrayBuffer, filename: string): ModelValidationResult {
    const ext = filename.toLowerCase();
    let confidence = 0;
    const issues: string[] = [];
    
    // 拡張子チェック
    if (ext.endsWith('.model3.json') || ext.endsWith('.model.json')) {
      confidence += 0.5;
      
      // JSONファイルの内容をチェック
      try {
        const jsonText = new TextDecoder().decode(data);
        const jsonData = JSON.parse(jsonText);
        
        // LIVE2D固有のフィールドをチェック
        if (jsonData.FileReferences || jsonData.Groups || jsonData.HitAreas) {
          confidence += 0.3;
        }
        
        if (jsonData.Moc || jsonData.moc) {
          confidence += 0.2;
        }
        
        if (jsonData.Version && (jsonData.Version >= 3 || jsonData.version >= 3)) {
          confidence += 0.1;
        } else if (jsonData.Version && jsonData.Version < 3) {
          issues.push('LIVE2D Cubism 2.x形式は非推奨です。Cubism 3.x以降への更新を推奨します。');
        }
        
      } catch (error) {
        issues.push('JSONファイルの解析に失敗');
        confidence -= 0.2;
      }
      
    } else if (ext.endsWith('.moc3')) {
      confidence += 0.4;
      
      // MOC3ファイルのヘッダーチェック
      if (data.byteLength >= 4) {
        const header = new Uint8Array(data, 0, 4);
        const magic = Array.from(header).map(b => String.fromCharCode(b)).join('');
        
        if (magic === 'MOC3') {
          confidence += 0.4;
        } else {
          issues.push('MOC3ファイルのヘッダーが無効です');
        }
      }
    }
    
    // ファイルサイズチェック
    if (data.byteLength < 100) {
      issues.push('ファイルサイズが小さすぎます');
      confidence -= 0.2;
    } else if (data.byteLength > 50 * 1024 * 1024) {
      issues.push('ファイルサイズが大きすぎます (50MB超)');
    }
    
    return {
      isValid: confidence > 0.5,
      format: ModelFormat.LIVE2D,
      confidence: Math.min(confidence, 1.0),
      issues: issues.length > 0 ? issues : undefined
    };
  }
  
  /**
   * LIVE2Dモデルの読み込み
   */
  async load(
    data: ArrayBuffer, 
    filename: string, 
    options?: ModelLoaderOptions
  ): Promise<UniversalModel> {
    console.log(`🎭 LIVE2Dモデル読み込み開始: ${filename}`);
    
    try {
      // LIVE2D Cubism SDKを初期化
      const cubismSDK = await this.initializeCubismSDK();
      
      let modelData: any;
      let modelJson: any;
      
      if (filename.toLowerCase().endsWith('.model3.json') || filename.toLowerCase().endsWith('.model.json')) {
        // model3.jsonファイルの場合
        const jsonText = new TextDecoder().decode(data);
        modelJson = JSON.parse(jsonText);
        
        console.log(`📋 LIVE2D model.json解析完了:`, {
          version: modelJson.Version || modelJson.version,
          moc: modelJson.Moc || modelJson.moc,
          textures: modelJson.FileReferences?.Textures?.length || 0,
          motions: Object.keys(modelJson.FileReferences?.Motions || {}).length
        });
        
        // 実際のモデル読み込み（ここではプレースホルダー）
        modelData = await this.loadLive2DFromJson(modelJson, filename);
        
      } else if (filename.toLowerCase().endsWith('.moc3')) {
        // MOC3ファイルの場合
        console.log(`📋 LIVE2D MOC3ファイル読み込み: ${data.byteLength} bytes`);
        modelData = await this.loadLive2DFromMoc3(data, filename);
        
      } else {
        throw new Error(`サポートされていないLIVE2Dファイル形式: ${filename}`);
      }
      
      // メタデータを抽出
      const metadata = this.extractLive2DMetadata(modelJson || {}, filename, data.byteLength);
      
      // LIVE2D機能を分析
      const capabilities = this.analyzeLive2DCapabilities(modelData, modelJson);
      
      // 統一モデルオブジェクトを作成
      const model: UniversalModel = {
        id: ModelLoaderUtils.generateModelId(),
        name: metadata.title || filename.replace(/\.[^/.]+$/, ''),
        type: ModelType.AVATAR,
        format: ModelFormat.LIVE2D,
        data: {
          live2dModel: modelData,
          modelJson: modelJson,
          // LIVE2D用のThree.jsシーンを作成
          scene: this.createLive2DScene(modelData)
        },
        capabilities,
        metadata,
        isLoaded: true,
        isVisible: true,
        isAnimating: false,
        fileName: filename,
        fileSize: data.byteLength,
        arrayBuffer: data,
        position: new THREE.Vector3(0, 0, 0),
        rotation: new THREE.Euler(0, 0, 0),
        scale: new THREE.Vector3(1, 1, 1)
      };
      
      console.log(`🎭 LIVE2Dモデル読み込み完了:`, {
        name: model.name,
        version: metadata.version,
        capabilities: capabilities
      });
      
      return model;
      
    } catch (error) {
      console.error(`❌ LIVE2Dモデル読み込みエラー: ${filename}`, error);
      throw error;
    }
  }
  
  /**
   * デフォルトのLIVE2D機能
   */
  getDefaultCapabilities(): ModelCapabilities {
    return {
      expressions: true,
      animations: true,
      physics: true,
      lipSync: true,
      eyeTracking: true,
      blinking: true,
      morphTargets: true,
      bones: false, // LIVE2Dはボーンではなくパラメータ制御
      materials: true
    };
  }
  
  /**
   * LIVE2D Cubism SDKを初期化
   */
  private async initializeCubismSDK(): Promise<any> {
    if (!Live2DCubismSDK_CACHE) {
      console.log('📦 LIVE2D Cubism SDKを読み込み中...');
      
      try {
        // LIVE2D Cubism SDK for Webを動的にロード
        // 注意: 実際の実装では適切なCubism SDKのパッケージを使用
        Live2DCubismSDK_CACHE = await import('@framework/live2dcubismframework');
        console.log('✅ LIVE2D Cubism SDK読み込み完了');
      } catch (error) {
        console.warn('⚠️ LIVE2D Cubism SDK読み込み失敗、プレースホルダーを使用:', error);
        // フォールバック: プレースホルダーSDK
        Live2DCubismSDK_CACHE = {
          CubismFramework: {
            initialize: () => console.log('LIVE2D Framework初期化（プレースホルダー）'),
            dispose: () => console.log('LIVE2D Framework破棄（プレースホルダー）')
          }
        };
      }
    }
    return Live2DCubismSDK_CACHE;
  }
  
  /**
   * model.jsonからLIVE2Dモデルを読み込み
   */
  private async loadLive2DFromJson(modelJson: any, filename: string): Promise<any> {
    console.log(`📋 LIVE2D model.json読み込み: ${filename}`);
    
    // プレースホルダー実装
    // 実際の実装では、Cubism SDKを使用してモデルを読み込む
    return {
      type: 'live2d_model',
      version: modelJson.Version || modelJson.version || 3,
      modelJson: modelJson,
      isLoaded: true
    };
  }
  
  /**
   * MOC3ファイルからLIVE2Dモデルを読み込み
   */
  private async loadLive2DFromMoc3(data: ArrayBuffer, filename: string): Promise<any> {
    console.log(`📋 LIVE2D MOC3読み込み: ${filename}`);
    
    // プレースホルダー実装
    // 実際の実装では、Cubism SDKを使用してMOC3ファイルを読み込む
    return {
      type: 'live2d_model',
      version: 3,
      mocData: data,
      isLoaded: true
    };
  }
  
  /**
   * LIVE2Dメタデータを抽出
   */
  private extractLive2DMetadata(modelJson: any, filename: string, fileSize: number): ModelMetadata {
    return {
      title: modelJson.Name || filename.replace(/\.[^/.]+$/, ''),
      author: modelJson.Author || 'Unknown',
      version: (modelJson.Version || modelJson.version || 3).toString(),
      description: modelJson.Description || '',
      license: modelJson.License || 'Unknown',
      fileSize,
      // LIVE2D固有のメタデータ
      live2dVersion: modelJson.Version || modelJson.version || 3,
      textureCount: modelJson.FileReferences?.Textures?.length || 0,
      motionCount: Object.keys(modelJson.FileReferences?.Motions || {}).length,
      expressionCount: modelJson.FileReferences?.Expressions?.length || 0
    };
  }
  
  /**
   * LIVE2D機能を分析
   */
  private analyzeLive2DCapabilities(modelData: any, modelJson: any): ModelCapabilities {
    const capabilities = this.getDefaultCapabilities();
    
    try {
      // 表情機能をチェック
      if (modelJson?.FileReferences?.Expressions) {
        capabilities.expressions = modelJson.FileReferences.Expressions.length > 0;
      }
      
      // アニメーション機能をチェック
      if (modelJson?.FileReferences?.Motions) {
        capabilities.animations = Object.keys(modelJson.FileReferences.Motions).length > 0;
      }
      
      // 物理演算機能をチェック
      if (modelJson?.FileReferences?.Physics) {
        capabilities.physics = true;
      }
      
      console.log('🔍 LIVE2D機能分析完了:', capabilities);
      
    } catch (error) {
      console.warn('⚠️ LIVE2D機能分析中にエラー:', error);
    }
    
    return capabilities;
  }
  
  /**
   * LIVE2D用のThree.jsシーンを作成
   */
  private createLive2DScene(modelData: any): THREE.Group {
    const group = new THREE.Group();
    group.name = 'Live2D Model';
    
    // プレースホルダー実装
    // 実際の実装では、LIVE2DモデルをThree.jsシーンに統合
    const geometry = new THREE.PlaneGeometry(2, 2);
    const material = new THREE.MeshBasicMaterial({ 
      color: 0x00ff00, 
      transparent: true, 
      opacity: 0.5 
    });
    const plane = new THREE.Mesh(geometry, material);
    plane.name = 'Live2D Placeholder';
    
    group.add(plane);
    
    return group;
  }
}
