import { NextRequest, NextResponse } from 'next/server'
import { 
  getQuickReference, 
  MAIN_COMPONENTS, 
  API_ENDPOINTS, 
  TERMINAL_COMMANDS,
  COMMON_TASKS,
  getComponentDetails,
  getApiDetails,
  getCommandDetails
} from '@/utils/token-saver'

// GET: クイックリファレンス取得
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const type = searchParams.get('type')
    const query = searchParams.get('query')
    
    switch (type) {
      case 'quick':
        return NextResponse.json({
          success: true,
          reference: getQuickReference()
        })
        
      case 'components':
        if (query) {
          const component = getComponentDetails(query)
          return NextResponse.json({
            success: true,
            component: component || null
          })
        }
        return NextResponse.json({
          success: true,
          components: MAIN_COMPONENTS
        })
        
      case 'apis':
        if (query) {
          const api = getApiDetails(query)
          return NextResponse.json({
            success: true,
            api: api || null
          })
        }
        return NextResponse.json({
          success: true,
          apis: API_ENDPOINTS
        })
        
      case 'commands':
        if (query) {
          const command = getCommandDetails(query)
          return NextResponse.json({
            success: true,
            command: command || null
          })
        }
        return NextResponse.json({
          success: true,
          commands: TERMINAL_COMMANDS
        })
        
      case 'tasks':
        return NextResponse.json({
          success: true,
          tasks: COMMON_TASKS
        })
        
      case 'search':
        if (!query) {
          return NextResponse.json(
            { success: false, error: 'Search query is required' },
            { status: 400 }
          )
        }
        
        const searchResults = {
          components: MAIN_COMPONENTS.filter(comp => 
            comp.name.toLowerCase().includes(query.toLowerCase()) ||
            comp.purpose.toLowerCase().includes(query.toLowerCase()) ||
            comp.keyFeatures.some(feature => feature.toLowerCase().includes(query.toLowerCase()))
          ),
          apis: API_ENDPOINTS.filter(api =>
            api.path.toLowerCase().includes(query.toLowerCase()) ||
            api.purpose.toLowerCase().includes(query.toLowerCase())
          ),
          commands: TERMINAL_COMMANDS.filter(cmd =>
            cmd.command.toLowerCase().includes(query.toLowerCase()) ||
            cmd.description.toLowerCase().includes(query.toLowerCase())
          ),
          tasks: Object.entries(COMMON_TASKS).filter(([key, task]) =>
            key.toLowerCase().includes(query.toLowerCase()) ||
            task.description.toLowerCase().includes(query.toLowerCase())
          ).reduce((acc, [key, task]) => ({ ...acc, [key]: task }), {})
        }
        
        return NextResponse.json({
          success: true,
          query,
          results: searchResults
        })
        
      default:
        return NextResponse.json({
          success: true,
          overview: {
            totalComponents: MAIN_COMPONENTS.length,
            totalApis: API_ENDPOINTS.length,
            totalCommands: TERMINAL_COMMANDS.length,
            totalTasks: Object.keys(COMMON_TASKS).length
          },
          quickReference: getQuickReference()
        })
    }
  } catch (error) {
    console.error('Quick reference API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get reference data' },
      { status: 500 }
    )
  }
}