import { NextRequest, NextResponse } from 'next/server'
import { personaSystem, PERSONA_THEMES } from '@/utils/agent-persona-system'

// GET: ペルソナシステム情報取得
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const type = searchParams.get('type')
    
    const currentTheme = personaSystem.getCurrentTheme()
    const availableThemes = personaSystem.getAvailableThemes()
    const customSets = personaSystem.getCustomSets()
    
    switch (type) {
      case 'themes':
        return NextResponse.json({
          success: true,
          themes: availableThemes
        })
        
      case 'current':
        return NextResponse.json({
          success: true,
          currentTheme
        })
        
      case 'custom':
        return NextResponse.json({
          success: true,
          customSets
        })
        
      case 'persona':
        const personaId = searchParams.get('id')
        const level = searchParams.get('level')
        
        if (personaId) {
          const persona = personaSystem.getPersonaById(personaId)
          return NextResponse.json({
            success: true,
            persona: persona || null
          })
        } else if (level) {
          const persona = personaSystem.getPersonaByLevel(parseInt(level))
          return NextResponse.json({
            success: true,
            persona: persona || null
          })
        }
        
        return NextResponse.json(
          { success: false, error: 'Persona ID or level required' },
          { status: 400 }
        )
        
      default:
        return NextResponse.json({
          success: true,
          currentTheme,
          themes: availableThemes,
          customSets,
          totalThemes: availableThemes.length,
          totalCustomSets: customSets.length
        })
    }
  } catch (error) {
    console.error('Persona system API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get persona system data' },
      { status: 500 }
    )
  }
}

// POST: ペルソナシステム操作
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, themeId, customSet, persona } = body
    
    switch (action) {
      case 'setTheme':
        if (!themeId) {
          return NextResponse.json(
            { success: false, error: 'Theme ID is required' },
            { status: 400 }
          )
        }
        
        const success = personaSystem.setTheme(themeId)
        if (success) {
          const newTheme = personaSystem.getCurrentTheme()
          return NextResponse.json({
            success: true,
            theme: newTheme,
            message: `Theme changed to ${newTheme.name}`
          })
        } else {
          return NextResponse.json(
            { success: false, error: 'Invalid theme ID' },
            { status: 400 }
          )
        }
        
      case 'createCustomSet':
        if (!customSet || !customSet.name || !customSet.roles) {
          return NextResponse.json(
            { success: false, error: 'Custom set name and roles are required' },
            { status: 400 }
          )
        }
        
        const customSetId = personaSystem.createCustomSet(
          customSet.name,
          customSet.description || '',
          customSet.roles
        )
        
        return NextResponse.json({
          success: true,
          customSetId,
          message: `Custom persona set "${customSet.name}" created`
        })
        
      case 'applyCustomSet':
        if (!customSet || !customSet.id) {
          return NextResponse.json(
            { success: false, error: 'Custom set ID is required' },
            { status: 400 }
          )
        }
        
        const applySuccess = personaSystem.applyCustomSet(customSet.id)
        if (applySuccess) {
          const currentTheme = personaSystem.getCurrentTheme()
          return NextResponse.json({
            success: true,
            theme: currentTheme,
            message: 'Custom persona set applied'
          })
        } else {
          return NextResponse.json(
            { success: false, error: 'Custom set not found' },
            { status: 404 }
          )
        }
        
      case 'getPersonaAddress':
        if (!persona || typeof persona.level !== 'number') {
          return NextResponse.json(
            { success: false, error: 'Persona level is required' },
            { status: 400 }
          )
        }
        
        const address = personaSystem.getPersonaAddress(persona.level, persona.name)
        return NextResponse.json({
          success: true,
          address,
          persona: personaSystem.getPersonaByLevel(persona.level)
        })
        
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Persona system POST error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to process persona system operation' },
      { status: 500 }
    )
  }
}

// PUT: ペルソナシステム設定更新
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { loadSavedTheme } = body
    
    if (loadSavedTheme) {
      personaSystem.loadSavedTheme()
      const currentTheme = personaSystem.getCurrentTheme()
      
      return NextResponse.json({
        success: true,
        theme: currentTheme,
        message: 'Saved theme loaded'
      })
    }
    
    return NextResponse.json(
      { success: false, error: 'Invalid operation' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Persona system PUT error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update persona system' },
      { status: 500 }
    )
  }
}