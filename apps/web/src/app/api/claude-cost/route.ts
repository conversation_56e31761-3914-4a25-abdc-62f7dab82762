import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

export async function GET() {
  try {
    // dev.logファイルからClaude Code SDK の使用コスト情報を抽出
    const logPath = join(process.cwd(), 'dev.log');
    const logData = await readFile(logPath, 'utf-8');
    
    // ログからコスト情報を抽出
    const costMatches = logData.match(/total_cost_usd: ([\d.]+)/g);
    const sessionMatches = logData.match(/session_id: '([^']+)'/g);
    
    if (!costMatches || costMatches.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No cost data found in logs'
      });
    }
    
    // コスト情報を計算
    const costs = costMatches.map(match => {
      const cost = parseFloat(match.split(': ')[1]);
      return cost;
    });
    
    const sessions = sessionMatches ? [...new Set(sessionMatches)] : [];
    const totalCost = costs.reduce((sum, cost) => sum + cost, 0);
    
    // 今日の日付を取得
    const today = new Date().toISOString().split('T')[0];
    const todayLogLines = logData.split('\n').filter(line => line.includes(today));
    
    // 今日のコスト計算
    const todayCosts = todayLogLines
      .map(line => {
        const match = line.match(/total_cost_usd: ([\d.]+)/);
        return match ? parseFloat(match[1]) : 0;
      })
      .filter(cost => cost > 0);
    
    const todayCost = todayCosts.reduce((sum, cost) => sum + cost, 0);
    
    // 今月のコスト（簡易計算 - 実際にはより詳細な日付解析が必要）
    const monthCost = totalCost; // 簡易版として全体コストを使用
    
    return NextResponse.json({
      success: true,
      totalCost,
      sessionCount: sessions.length,
      todayCost,
      monthCost,
      lastUpdated: new Date().toISOString(),
      breakdown: {
        totalSessions: sessions.length,
        totalRequests: costs.length,
        averageCostPerRequest: totalCost / costs.length,
        highestCost: Math.max(...costs),
        lowestCost: Math.min(...costs)
      }
    });
    
  } catch (error: any) {
    console.error('Cost tracking error:', error);
    
    if (error.code === 'ENOENT') {
      return NextResponse.json({
        success: false,
        error: 'Log file not found. No Claude Code usage recorded yet.'
      });
    }
    
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to calculate API costs'
    });
  }
}