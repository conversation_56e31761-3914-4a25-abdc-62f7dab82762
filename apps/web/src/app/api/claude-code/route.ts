import { NextRequest, NextResponse } from 'next/server';
import { query } from '@anthropic-ai/claude-code';

export async function POST(request: NextRequest) {
  try {
    const { prompt, maxTurns = 3, streaming = false } = await request.json();
    
    if (!prompt) {
      return NextResponse.json({ 
        error: 'Prompt is required',
        success: false 
      }, { status: 400 });
    }

    // ANTHROPIC_API_KEY環境変数確認
    if (!process.env.ANTHROPIC_API_KEY) {
      return NextResponse.json({ 
        error: 'ANTHROPIC_API_KEY environment variable is not set',
        success: false,
        needsApiKey: true
      }, { status: 401 });
    }

    console.log('Claude Code SDK Query:', prompt);
    
    // ストリーミングレスポンス
    if (streaming) {
      const encoder = new TextEncoder();
      const stream = new ReadableStream({
        async start(controller) {
          try {
            const abortController = new AbortController();
            let messages = [];
            let startTime = Date.now();
            
            // 初期状態を送信
            controller.enqueue(encoder.encode(`data: ${JSON.stringify({
              type: 'status',
              status: 'thinking',
              elapsed: 0,
              tokens: { input: 0, output: 0 }
            })}\n\n`));
            
            for await (const message of query({
              prompt,
              abortController,
              options: {
                maxTurns,
                cwd: '/Users/<USER>/Dev/meta-studio/apps/web'
              }
            })) {
              messages.push(message);
              const elapsed = Date.now() - startTime;
              
              // リアルタイム更新を送信
              let updateData = {
                type: 'update',
                elapsed,
                message: message
              };
              
              // メッセージタイプに応じた状態判定
              if (message.type === 'system') {
                updateData.status = 'initializing';
              } else if (message.type === 'assistant') {
                updateData.status = 'generating';
                if (message.message?.usage) {
                  updateData.tokens = {
                    input: message.message.usage.input_tokens || 0,
                    output: message.message.usage.output_tokens || 0
                  };
                }
              } else if (message.type === 'result') {
                updateData.status = 'completed';
                updateData.result = message.result;
                updateData.cost = message.total_cost_usd;
              }
              
              controller.enqueue(encoder.encode(`data: ${JSON.stringify(updateData)}\n\n`));
            }
            
            // 完了通知
            controller.enqueue(encoder.encode(`data: ${JSON.stringify({
              type: 'complete',
              elapsed: Date.now() - startTime,
              messages,
              messageCount: messages.length
            })}\n\n`));
            
            controller.close();
          } catch (error) {
            controller.enqueue(encoder.encode(`data: ${JSON.stringify({
              type: 'error',
              error: error.message
            })}\n\n`));
            controller.close();
          }
        }
      });
      
      return new Response(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    }
    
    // 従来の非ストリーミングレスポンス
    const messages = [];
    const abortController = new AbortController();
    
    // Claude Code SDKクエリ実行
    for await (const message of query({
      prompt,
      abortController,
      options: {
        maxTurns,
        cwd: '/Users/<USER>/Dev/meta-studio/apps/web'
      }
    })) {
      messages.push(message);
      console.log('Claude Message:', message);
    }
    
    return NextResponse.json({
      messages,
      success: true,
      messageCount: messages.length
    });
    
  } catch (error: any) {
    console.error('Claude Code SDK Error:', error);
    
    // API Key関連エラー
    if (error.message?.includes('API key') || error.message?.includes('authentication')) {
      return NextResponse.json({ 
        error: 'Invalid or missing ANTHROPIC_API_KEY',
        success: false,
        needsApiKey: true
      }, { status: 401 });
    }
    
    // Rate Limit
    if (error.message?.includes('rate limit')) {
      return NextResponse.json({ 
        error: 'Rate limit exceeded. Please try again later.',
        success: false 
      }, { status: 429 });
    }
    
    return NextResponse.json({ 
      error: error.message || 'Claude Code SDK execution failed',
      success: false 
    }, { status: 500 });
  }
}