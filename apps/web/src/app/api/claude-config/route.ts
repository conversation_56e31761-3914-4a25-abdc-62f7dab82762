import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'
import os from 'os'

// Claude設定ファイルのパス
const CLAUDE_CONFIG_PATH = path.join(os.homedir(), '.config', 'claude-desktop', 'config.json')
const META_STUDIO_CONFIG_PATH = path.join(process.cwd(), '.claude', 'config.json')

interface ClaudeConfig {
  apiKey?: string
  model?: string
  maxTokens?: number
  temperature?: number
  permissions?: {
    allowedPaths?: string[]
    autoApprove?: boolean
  }
  customSettings?: Record<string, any>
}

// GET: 設定をエクスポート
export async function GET() {
  try {
    const configs: Record<string, any> = {}
    
    // Claude Desktop設定を読み込み
    try {
      const claudeConfig = await fs.readFile(CLAUDE_CONFIG_PATH, 'utf-8')
      configs.claudeDesktop = JSON.parse(claudeConfig)
    } catch (error) {
      configs.claudeDesktop = null
    }
    
    // Meta Studio独自設定を読み込み
    try {
      const metaConfig = await fs.readFile(META_STUDIO_CONFIG_PATH, 'utf-8')
      configs.metaStudio = JSON.parse(metaConfig)
    } catch (error) {
      configs.metaStudio = {
        permissions: {
          allowedPaths: [],
          autoApprove: false
        },
        customSettings: {}
      }
    }
    
    // 環境変数から設定を取得
    configs.environment = {
      hasApiKey: !!process.env.ANTHROPIC_API_KEY,
      model: process.env.ANTHROPIC_MODEL || 'claude-3-sonnet-20240229',
      maxTokens: parseInt(process.env.ANTHROPIC_MAX_TOKENS || '4096'),
      temperature: parseFloat(process.env.ANTHROPIC_TEMPERATURE || '0.7')
    }
    
    return NextResponse.json({
      success: true,
      configs,
      exportedAt: new Date().toISOString()
    })
  } catch (error) {
    console.error('Config export error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to export configuration' },
      { status: 500 }
    )
  }
}

// POST: 設定をインポート
export async function POST(request: NextRequest) {
  try {
    const { config, target = 'metaStudio' }: { config: ClaudeConfig; target?: string } = await request.json()
    
    if (target === 'metaStudio') {
      // Meta Studio設定ディレクトリを作成
      const configDir = path.dirname(META_STUDIO_CONFIG_PATH)
      await fs.mkdir(configDir, { recursive: true })
      
      // 既存の設定を読み込み
      let existingConfig: ClaudeConfig = {}
      try {
        const existing = await fs.readFile(META_STUDIO_CONFIG_PATH, 'utf-8')
        existingConfig = JSON.parse(existing)
      } catch (error) {
        // ファイルが存在しない場合は無視
      }
      
      // 設定をマージ
      const mergedConfig = {
        ...existingConfig,
        ...config,
        permissions: {
          ...existingConfig.permissions,
          ...config.permissions
        },
        customSettings: {
          ...existingConfig.customSettings,
          ...config.customSettings
        },
        lastUpdated: new Date().toISOString()
      }
      
      // 設定を保存
      await fs.writeFile(
        META_STUDIO_CONFIG_PATH,
        JSON.stringify(mergedConfig, null, 2)
      )
      
      return NextResponse.json({
        success: true,
        message: 'Configuration imported successfully',
        config: mergedConfig
      })
    } else {
      return NextResponse.json(
        { success: false, error: 'Direct Claude Desktop config modification not supported' },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Config import error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to import configuration' },
      { status: 500 }
    )
  }
}

// PUT: 特定の設定を更新
export async function PUT(request: NextRequest) {
  try {
    const { key, value }: { key: string; value: any } = await request.json()
    
    // 設定ディレクトリを作成
    const configDir = path.dirname(META_STUDIO_CONFIG_PATH)
    await fs.mkdir(configDir, { recursive: true })
    
    // 既存の設定を読み込み
    let config: ClaudeConfig = {}
    try {
      const existing = await fs.readFile(META_STUDIO_CONFIG_PATH, 'utf-8')
      config = JSON.parse(existing)
    } catch (error) {
      // ファイルが存在しない場合は無視
    }
    
    // ネストしたキーをサポート (例: "permissions.autoApprove")
    const keys = key.split('.')
    let target: any = config
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!target[keys[i]]) {
        target[keys[i]] = {}
      }
      target = target[keys[i]]
    }
    
    target[keys[keys.length - 1]] = value
    
    // 設定を保存
    await fs.writeFile(
      META_STUDIO_CONFIG_PATH,
      JSON.stringify(config, null, 2)
    )
    
    return NextResponse.json({
      success: true,
      message: `Setting "${key}" updated successfully`,
      config
    })
  } catch (error) {
    console.error('Config update error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update configuration' },
      { status: 500 }
    )
  }
}