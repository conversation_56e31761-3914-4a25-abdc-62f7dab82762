import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs/promises'
import path from 'path'

const TASK_MD_PATH = path.join(process.cwd(), '../../task.md')

interface Task {
  id: string
  content: string
  status: 'pending' | 'in_progress' | 'completed' | 'archived'
  priority: 'high' | 'medium' | 'low'
  difficulty: 'high' | 'medium' | 'low'
  tags: string[]
  assignee?: string
  dueDate?: string
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  estimatedTime?: number
  actualTime?: number
  parentId?: string
  subtasks?: Task[]
}

interface ProgressStats {
  total: number
  completed: number
  inProgress: number
  pending: number
  completionRate: number
}

// 優先度・難易度アイコンの変換
const getPriorityIcon = (priority: string): string => {
  switch (priority) {
    case 'high': return '🔥'
    case 'medium': return '⚡'
    case 'low': return '💡'
    default: return '📋'
  }
}

const getDifficultyIcon = (difficulty: string): string => {
  switch (difficulty) {
    case 'high': return '🔴'
    case 'medium': return '🟡'
    case 'low': return '🟢'
    default: return '⚪'
  }
}

const getStatusIcon = (status: string): string => {
  switch (status) {
    case 'completed': return '☑️'
    case 'in_progress': return '🔄'
    case 'pending': return '⏸️'
    case 'archived': return '📦'
    default: return '📋'
  }
}

// task.mdの解析
const parseTaskMd = (content: string): { 
  realtimeTasks: Task[], 
  completedTasks: Task[], 
  pendingTasks: Task[] 
} => {
  const realtimeTasks: Task[] = []
  const completedTasks: Task[] = []
  const pendingTasks: Task[] = []

  const lines = content.split('\n')
  let currentSection = ''
  let currentTasks = realtimeTasks
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    
    // セクション判定
    if (line.includes('ユーザーのリアルタイム記入欄')) {
      currentSection = 'realtime'
      currentTasks = realtimeTasks
      continue
    } else if (line.includes('今回のセッション新規完了タスク')) {
      currentSection = 'completed'
      currentTasks = completedTasks
      continue
    } else if (line.includes('進行中・高優先度')) {
      currentSection = 'pending'
      currentTasks = pendingTasks
      continue
    }
    
    // タスク行の解析
    if (line.match(/^[-\*]\s*(🔥|⚡|💡|🔴|🟡|🟢|☑️|🔄|⏸️|❌)/)) {
      const task = parseTaskLine(line, i)
      if (task) {
        currentTasks.push(task)
      }
    }
  }
  
  return { realtimeTasks, completedTasks, pendingTasks }
}

// タスク行の解析
const parseTaskLine = (line: string, lineNumber: number): Task | null => {
  // 正規表現でタスク行を解析
  const match = line.match(/^[-\*]\s*(🔥|⚡|💡)?\s*(🔴|🟡|🟢)?\s*(☑️|🔄|⏸️|❌)?\s*\*\*(.+?)\*\*\s*-?\s*(.*)$/)
  
  if (!match) {
    // 基本的なタスク行のフォールバック解析
    const simpleMatch = line.match(/^[-\*]\s*(.+)$/)
    if (simpleMatch) {
      return {
        id: `task-md-${lineNumber}-${Date.now()}`,
        content: simpleMatch[1].trim(),
        status: 'pending',
        priority: 'medium',
        difficulty: 'medium',
        tags: [],
        createdAt: new Date(),
        updatedAt: new Date()
      }
    }
    return null
  }
  
  const [, priorityIcon, difficultyIcon, statusIcon, title, description] = match
  
  // アイコンから属性を逆引き
  const priority = priorityIcon === '🔥' ? 'high' : priorityIcon === '⚡' ? 'medium' : 'low'
  const difficulty = difficultyIcon === '🔴' ? 'high' : difficultyIcon === '🟡' ? 'medium' : 'low'
  const status = statusIcon === '☑️' ? 'completed' : statusIcon === '🔄' ? 'in_progress' : 'pending'
  
  return {
    id: `task-md-${lineNumber}-${Date.now()}`,
    content: `${title} - ${description}`.trim(),
    status,
    priority,
    difficulty,
    tags: extractTags(description),
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

// タグの抽出
const extractTags = (text: string): string[] => {
  const tags: string[] = []
  
  // 実装、修正、追加などのキーワードをタグとして抽出
  const keywords = ['実装', '修正', '追加', '改善', '調査', '設計', '統合', '機能', 'UI', 'UX', 'API', 'バグ']
  keywords.forEach(keyword => {
    if (text.includes(keyword)) {
      tags.push(keyword)
    }
  })
  
  return [...new Set(tags)] // 重複除去
}

// task.mdの生成
const generateTaskMd = (
  completedTasks: Task[], 
  pendingTasks: Task[], 
  progressStats: ProgressStats
): string => {
  const now = new Date().toISOString().split('T')[0]
  
  return `# メタスタジオタスク管理 - 統合版（最終更新: ${now}）

## 📋 タスク管理ルール
- **重要度**: 🔥 高 | ⚡ 中 | 💡 低
- **難易度**: 🔴 高 | 🟡 中 | 🟢 低
- **状態**: ☑️ 完了 | 🔄 進行中 | ⏸️ 保留 | ❌ 不要

## 📊 進捗統計
- **全体進捗**: ${progressStats.completionRate}% (${progressStats.completed}/${progressStats.total})
- **完了**: ${progressStats.completed}件
- **進行中**: ${progressStats.inProgress}件
- **未着手**: ${progressStats.pending}件

---
## ユーザーのリアルタイム記入欄 （このカテゴリ自体は消さず中身は実装したら移動して空白にして。メモリーしてね。ここにあるものはタスクリストにMECEにすぐ振り分け。）

（空白 - 実装完了項目は今回のセッション完了タスクに移動済み）

---

## 🔥 今回のセッション新規完了タスク(ユーザーの要望を聞いて実装したものの倉庫。ユーザーがOKだしたら本完了に移動。)確認待ちタスクはここ

${completedTasks.map(task => 
  `${getStatusIcon(task.status)} ${getPriorityIcon(task.priority)}${getDifficultyIcon(task.difficulty)} **${task.content}** - 完了日: ${task.completedAt ? task.completedAt.toLocaleDateString() : '不明'}`
).join('\n')}

---

## 【進行中・高優先度】即座に実装すべきタスク（ユーザーから聞いた要望の置き場）

${pendingTasks
  .filter(task => task.status !== 'completed')
  .sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 }
    return priorityOrder[b.priority] - priorityOrder[a.priority]
  })
  .map(task => 
    `- ${getStatusIcon(task.status)} ${getPriorityIcon(task.priority)}${getDifficultyIcon(task.difficulty)} **${task.content}**${task.dueDate ? ` - 期限: ${new Date(task.dueDate).toLocaleDateString()}` : ''}${task.tags.length > 0 ? ` [${task.tags.join(', ')}]` : ''}`
  ).join('\n')}

---

## ✅ 【完了・実装済み】
${completedTasks.filter(task => task.status === 'completed').map(task => 
  `☑️ **${task.content}** - 完了日: ${task.completedAt ? task.completedAt.toLocaleDateString() : '不明'}`
).join('\n')}

---

## 📦 【アーカイブ】
${pendingTasks.filter(task => task.status === 'archived').map(task => 
  `📦 **${task.content}** - アーカイブ日: ${task.updatedAt.toLocaleDateString()}`
).join('\n')}

---

*このファイルは TaskManager システムにより自動更新されています*
*最終更新: ${new Date().toISOString()}*`
}

// GET: task.mdの読み込み
export async function GET() {
  try {
    const content = await fs.readFile(TASK_MD_PATH, 'utf-8')
    const parsed = parseTaskMd(content)
    
    return NextResponse.json({
      success: true,
      content,
      parsed
    })
  } catch (error) {
    console.error('task.md読み込みエラー:', error)
    return NextResponse.json(
      { success: false, error: 'ファイル読み込みに失敗' },
      { status: 500 }
    )
  }
}

// POST: task.mdの更新
export async function POST(request: NextRequest) {
  try {
    const { completedTasks, pendingTasks, progressStats } = await request.json()
    
    // task.mdの内容を生成
    const newContent = generateTaskMd(completedTasks, pendingTasks, progressStats)
    
    // ファイルの書き込み
    await fs.writeFile(TASK_MD_PATH, newContent, 'utf-8')
    
    return NextResponse.json({
      success: true,
      message: 'task.mdが正常に更新されました',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('task.md更新エラー:', error)
    return NextResponse.json(
      { success: false, error: 'ファイル更新に失敗' },
      { status: 500 }
    )
  }
}

// PUT: 特定タスクの更新
export async function PUT(request: NextRequest) {
  try {
    const { taskId, updates } = await request.json()
    
    // task.mdから現在のタスクを読み込み
    const content = await fs.readFile(TASK_MD_PATH, 'utf-8')
    const parsed = parseTaskMd(content)
    
    // タスクを更新
    const allTasks = [...parsed.realtimeTasks, ...parsed.completedTasks, ...parsed.pendingTasks]
    const updatedTasks = allTasks.map(task => 
      task.id === taskId ? { ...task, ...updates, updatedAt: new Date() } : task
    )
    
    // 分類し直し
    const completedTasks = updatedTasks.filter(t => t.status === 'completed')
    const pendingTasks = updatedTasks.filter(t => t.status !== 'completed')
    
    // 進捗統計を計算
    const progressStats = {
      total: updatedTasks.length,
      completed: completedTasks.length,
      inProgress: updatedTasks.filter(t => t.status === 'in_progress').length,
      pending: updatedTasks.filter(t => t.status === 'pending').length,
      completionRate: Math.round((completedTasks.length / updatedTasks.length) * 100) || 0
    }
    
    // task.mdを再生成・保存
    const newContent = generateTaskMd(completedTasks, pendingTasks, progressStats)
    await fs.writeFile(TASK_MD_PATH, newContent, 'utf-8')
    
    return NextResponse.json({
      success: true,
      message: 'タスクが正常に更新されました',
      task: updatedTasks.find(t => t.id === taskId)
    })
  } catch (error) {
    console.error('タスク更新エラー:', error)
    return NextResponse.json(
      { success: false, error: 'タスク更新に失敗' },
      { status: 500 }
    )
  }
}

// DELETE: タスクの削除
export async function DELETE(request: NextRequest) {
  try {
    const { taskId } = await request.json()
    
    // task.mdから現在のタスクを読み込み
    const content = await fs.readFile(TASK_MD_PATH, 'utf-8')
    const parsed = parseTaskMd(content)
    
    // タスクを削除
    const allTasks = [...parsed.realtimeTasks, ...parsed.completedTasks, ...parsed.pendingTasks]
    const filteredTasks = allTasks.filter(task => task.id !== taskId)
    
    // 分類
    const completedTasks = filteredTasks.filter(t => t.status === 'completed')
    const pendingTasks = filteredTasks.filter(t => t.status !== 'completed')
    
    // 進捗統計を計算
    const progressStats = {
      total: filteredTasks.length,
      completed: completedTasks.length,
      inProgress: filteredTasks.filter(t => t.status === 'in_progress').length,
      pending: filteredTasks.filter(t => t.status === 'pending').length,
      completionRate: Math.round((completedTasks.length / filteredTasks.length) * 100) || 0
    }
    
    // task.mdを再生成・保存
    const newContent = generateTaskMd(completedTasks, pendingTasks, progressStats)
    await fs.writeFile(TASK_MD_PATH, newContent, 'utf-8')
    
    return NextResponse.json({
      success: true,
      message: 'タスクが正常に削除されました'
    })
  } catch (error) {
    console.error('タスク削除エラー:', error)
    return NextResponse.json(
      { success: false, error: 'タスク削除に失敗' },
      { status: 500 }
    )
  }
}