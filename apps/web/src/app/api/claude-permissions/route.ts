import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'

const PERMISSIONS_FILE = path.join(process.cwd(), '.claude', 'permissions.json')

interface PermissionEntry {
  path: string
  type: 'file' | 'directory'
  permissions: string[]
  autoApprove: boolean
  createdAt: string
  lastUsed?: string
}

interface PermissionsConfig {
  version: string
  globalAutoApprove: boolean
  entries: PermissionEntry[]
}

// デフォルトのパーミッション設定
const DEFAULT_PERMISSIONS: PermissionsConfig = {
  version: '1.0.0',
  globalAutoApprove: false,
  entries: [
    {
      path: process.cwd(),
      type: 'directory',
      permissions: ['read', 'write', 'execute'],
      autoApprove: true,
      createdAt: new Date().toISOString()
    }
  ]
}

// パーミッション設定を読み込み
async function loadPermissions(): Promise<PermissionsConfig> {
  try {
    const data = await fs.readFile(PERMISSIONS_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    // ファイルが存在しない場合はデフォルトを作成
    await savePermissions(DEFAULT_PERMISSIONS)
    return DEFAULT_PERMISSIONS
  }
}

// パーミッション設定を保存
async function savePermissions(config: PermissionsConfig): Promise<void> {
  const dir = path.dirname(PERMISSIONS_FILE)
  await fs.mkdir(dir, { recursive: true })
  await fs.writeFile(PERMISSIONS_FILE, JSON.stringify(config, null, 2))
}

// GET: パーミッション確認
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const targetPath = searchParams.get('path')
    
    const config = await loadPermissions()
    
    if (!targetPath) {
      // 全体の設定を返す
      return NextResponse.json({
        success: true,
        globalAutoApprove: config.globalAutoApprove,
        entries: config.entries,
        totalEntries: config.entries.length
      })
    }
    
    // 特定のパスのパーミッションを確認
    const normalizedPath = path.resolve(targetPath)
    const entry = config.entries.find(e => {
      const entryPath = path.resolve(e.path)
      if (e.type === 'directory') {
        // ディレクトリの場合は、そのパス以下のすべてのファイルに適用
        return normalizedPath.startsWith(entryPath)
      } else {
        // ファイルの場合は完全一致
        return normalizedPath === entryPath
      }
    })
    
    if (entry || config.globalAutoApprove) {
      return NextResponse.json({
        success: true,
        allowed: true,
        autoApprove: entry?.autoApprove || config.globalAutoApprove,
        permissions: entry?.permissions || ['read', 'write', 'execute'],
        reason: entry ? 'Specific path permission' : 'Global auto-approve enabled'
      })
    }
    
    return NextResponse.json({
      success: true,
      allowed: false,
      autoApprove: false,
      reason: 'No permission found for this path'
    })
  } catch (error) {
    console.error('Permission check error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to check permissions' },
      { status: 500 }
    )
  }
}

// POST: パーミッション追加/更新
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { path: targetPath, type = 'directory', permissions = ['read', 'write'], autoApprove = true } = body
    
    if (!targetPath) {
      return NextResponse.json(
        { success: false, error: 'Path is required' },
        { status: 400 }
      )
    }
    
    const config = await loadPermissions()
    const normalizedPath = path.resolve(targetPath)
    
    // 既存のエントリを探す
    const existingIndex = config.entries.findIndex(e => path.resolve(e.path) === normalizedPath)
    
    const newEntry: PermissionEntry = {
      path: normalizedPath,
      type,
      permissions,
      autoApprove,
      createdAt: new Date().toISOString(),
      lastUsed: new Date().toISOString()
    }
    
    if (existingIndex >= 0) {
      // 既存のエントリを更新
      config.entries[existingIndex] = {
        ...config.entries[existingIndex],
        ...newEntry,
        createdAt: config.entries[existingIndex].createdAt // 作成日時は保持
      }
    } else {
      // 新規エントリを追加
      config.entries.push(newEntry)
    }
    
    await savePermissions(config)
    
    return NextResponse.json({
      success: true,
      message: 'Permission added/updated successfully',
      entry: newEntry
    })
  } catch (error) {
    console.error('Permission update error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update permissions' },
      { status: 500 }
    )
  }
}

// PUT: グローバル設定の更新
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { globalAutoApprove } = body
    
    if (typeof globalAutoApprove !== 'boolean') {
      return NextResponse.json(
        { success: false, error: 'globalAutoApprove must be a boolean' },
        { status: 400 }
      )
    }
    
    const config = await loadPermissions()
    config.globalAutoApprove = globalAutoApprove
    
    await savePermissions(config)
    
    return NextResponse.json({
      success: true,
      message: `Global auto-approve ${globalAutoApprove ? 'enabled' : 'disabled'}`,
      globalAutoApprove
    })
  } catch (error) {
    console.error('Global permission update error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update global permissions' },
      { status: 500 }
    )
  }
}

// DELETE: パーミッション削除
export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const targetPath = searchParams.get('path')
    
    if (!targetPath) {
      return NextResponse.json(
        { success: false, error: 'Path is required' },
        { status: 400 }
      )
    }
    
    const config = await loadPermissions()
    const normalizedPath = path.resolve(targetPath)
    
    const originalLength = config.entries.length
    config.entries = config.entries.filter(e => path.resolve(e.path) !== normalizedPath)
    
    if (config.entries.length === originalLength) {
      return NextResponse.json(
        { success: false, error: 'Permission entry not found' },
        { status: 404 }
      )
    }
    
    await savePermissions(config)
    
    return NextResponse.json({
      success: true,
      message: 'Permission removed successfully',
      removedPath: normalizedPath
    })
  } catch (error) {
    console.error('Permission delete error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete permission' },
      { status: 500 }
    )
  }
}