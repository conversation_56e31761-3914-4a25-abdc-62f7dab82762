import { NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function GET() {
  try {
    // Claude CLI の存在と認証状態を確認
    const { stdout } = await execAsync('claude --version', { timeout: 5000 });
    
    if (stdout.includes('1.')) {
      // Claude CLI が存在する場合、簡単な認証テストを実行
      try {
        await execAsync('claude "test" --print', { 
          timeout: 10000,
          env: {
            ...process.env,
            CLAUDE_NONINTERACTIVE: '1'
          }
        });
        
        return NextResponse.json({
          authenticated: true,
          method: 'Max Plan CLI',
          version: stdout.trim()
        });
        
      } catch (authError) {
        return NextResponse.json({
          authenticated: false,
          method: 'CLI available but not authenticated',
          version: stdout.trim(),
          needsSetup: true
        });
      }
    }
    
    return NextResponse.json({
      authenticated: false,
      method: 'CLI not found'
    });
    
  } catch (error) {
    return NextResponse.json({
      authenticated: false,
      method: 'CLI check failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}