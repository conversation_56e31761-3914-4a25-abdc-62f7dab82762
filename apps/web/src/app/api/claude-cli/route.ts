import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    const { prompt, command = 'query' } = await request.json();
    
    if (!prompt) {
      return NextResponse.json({ 
        error: 'Prompt is required',
        success: false 
      }, { status: 400 });
    }

    // Claude Code CLIの存在確認
    try {
      await execAsync('which claude');
    } catch (error) {
      return NextResponse.json({
        success: false,
        needsCli: true,
        error: 'Claude Code CLI not found in PATH'
      });
    }

    // Max Plan認証チェック
    try {
      const { stdout: authCheck } = await execAsync('claude /auth');
      if (!authCheck.includes('authenticated') && !authCheck.includes('logged in')) {
        return NextResponse.json({
          success: false,
          needsAuth: true,
          error: 'Max Plan authentication required. Run: claude /logout && claude'
        });
      }
    } catch (error) {
      // 認証チェックが失敗した場合も続行（バージョンによって異なる可能性）
      console.warn('Auth check failed, proceeding:', error);
    }

    // Claude Code CLIバージョン確認
    let version = 'Unknown';
    try {
      const { stdout } = await execAsync('claude --version');
      version = stdout.trim();
    } catch (error) {
      console.warn('Could not get Claude CLI version:', error);
    }

    // Claude Code CLI実行（非対話モード）
    const escapedPrompt = prompt.replace(/"/g, '\\"');
    const cliCommand = `claude "${escapedPrompt}"`;
    
    console.log('Executing Claude Code CLI:', cliCommand);
    
    const { stdout, stderr } = await execAsync(cliCommand, {
      timeout: 60000, // 60秒に延長
      cwd: '/Users/<USER>/Dev/meta-studio/apps/web',
      env: {
        ...process.env,
        CLAUDE_NONINTERACTIVE: '1',  // 非対話モード
        CI: '1'  // CI環境をシミュレート
      }
    });
    
    if (stderr && !stderr.includes('Warning')) {
      return NextResponse.json({
        success: false,
        error: stderr,
        version
      });
    }
    
    return NextResponse.json({
      success: true,
      response: stdout.trim(),
      version,
      method: 'CLI',
      timestamp: new Date().toISOString()
    });
    
  } catch (error: any) {
    console.error('Claude CLI Error:', error);
    
    // タイムアウトエラー
    if (error.signal === 'SIGTERM' || error.message?.includes('timeout')) {
      return NextResponse.json({
        success: false,
        error: 'Claude CLI command timed out (30s limit)'
      });
    }
    
    // 認証エラー
    if (error.message?.includes('authentication') || error.message?.includes('API key')) {
      return NextResponse.json({
        success: false,
        error: 'Claude CLI authentication failed. Check API key configuration.'
      });
    }
    
    return NextResponse.json({
      success: false,
      error: error.message || 'Claude CLI execution failed'
    });
  }
}