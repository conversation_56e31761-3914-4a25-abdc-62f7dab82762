import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();
    
    if (action === 'login') {
      return await handleLogin();
    } else if (action === 'logout') {
      return await handleLogout();
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action. Use "login" or "logout"'
      });
    }
    
  } catch (error: any) {
    console.error('Claude Auth Error:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Authentication failed'
    });
  }
}

async function handleLogin() {
  try {
    // Claude CLIの存在確認
    await execAsync('which claude');
    
    return NextResponse.json({
      success: false,
      requiresManualSetup: true,
      instructions: [
        "1. ターミナル（Meta Studio外）を開く",
        "2. 'claude /logout' を実行",
        "3. 'claude update' を実行", 
        "4. ターミナルを再起動",
        "5. 'claude' を実行してMax Planアカウントを選択",
        "6. Meta Studioに戻って 'claude \"test\"' で確認"
      ],
      reason: "Claude Code認証は対話的プロセスのため、Meta Studio外での設定が必要です",
      nextStep: "手動設定完了後、Meta Studioで 'claude \"hello\"' をテストしてください"
    });
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Claude CLI not found. Install: npm install -g @anthropic-ai/claude-code'
    });
  }
}

async function handleLogout() {
  try {
    // 非対話的ログアウト
    await execAsync('claude /logout', {
      timeout: 10000,
      env: {
        ...process.env,
        CLAUDE_NONINTERACTIVE: '1'
      }
    });
    
    return NextResponse.json({
      success: true,
      message: 'Successfully logged out from Max Plan'
    });
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Logout failed. Claude CLI may not be installed.'
    });
  }
}