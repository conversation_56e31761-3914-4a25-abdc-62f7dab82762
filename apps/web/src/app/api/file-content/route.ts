import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join, extname } from 'path';

// ファイル拡張子からMIMEタイプを取得
const getMimeType = (filePath: string): string => {
  const ext = extname(filePath).toLowerCase();
  const mimeTypes: { [key: string]: string } = {
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.pdf': 'application/pdf',
    '.txt': 'text/plain',
    '.md': 'text/markdown',
    '.json': 'application/json',
    '.yaml': 'text/yaml',
    '.yml': 'text/yaml',
    '.js': 'text/javascript',
    '.ts': 'text/typescript',
    '.jsx': 'text/javascript',
    '.tsx': 'text/typescript',
    '.css': 'text/css',
    '.html': 'text/html',
  };
  return mimeTypes[ext] || 'application/octet-stream';
};

// ファイルが画像かどうかを判定
const isImageFile = (filePath: string): boolean => {
  const ext = extname(filePath).toLowerCase();
  return ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.ico'].includes(ext);
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const filePath = searchParams.get('path');
    const type = searchParams.get('type'); // 'image' or 'text'
    
    if (!filePath) {
      return NextResponse.json({ error: 'File path is required' }, { status: 400 });
    }

    // セキュリティ: プロジェクトディレクトリ内のみアクセス許可
    const projectRoot = '/Users/<USER>/Dev/meta-studio';
    
    // パス解決の改善: 相対パスと絶対パスの両方に対応
    let resolvedPath: string;
    
    if (filePath.startsWith('/')) {
      // 絶対パス形式の場合 (/apps/mobile/App.tsx)
      resolvedPath = join(projectRoot, filePath);
    } else {
      // 相対パス形式の場合 (App.tsx)
      resolvedPath = join(projectRoot, filePath);
    }
    
    console.log(`File path resolution: "${filePath}" -> "${resolvedPath}"`);
    
    // プロジェクトルート外へのアクセスを防ぐ
    if (!resolvedPath.startsWith(projectRoot)) {
      console.error('Access denied for path:', resolvedPath);
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const mimeType = getMimeType(resolvedPath);
    const isImage = isImageFile(resolvedPath);

    // 画像ファイルの場合はバイナリ形式で返す
    if (type === 'image' || isImage) {
      const buffer = await readFile(resolvedPath);
      
      return new NextResponse(buffer, {
        headers: {
          'Content-Type': mimeType,
          'Cache-Control': 'public, max-age=31536000, immutable',
        },
      });
    }

    // テキストファイルの場合はJSONで返す
    const content = await readFile(resolvedPath, 'utf8');
    
    return NextResponse.json({
      path: filePath,
      resolvedPath,
      content,
      success: true
    });

  } catch (error) {
    console.error('File reading error:', error);
    console.error('Failed path:', filePath);
    return NextResponse.json(
      { error: 'Failed to read file', details: error instanceof Error ? error.message : 'Unknown error', path: filePath },
      { status: 500 }
    );
  }
}