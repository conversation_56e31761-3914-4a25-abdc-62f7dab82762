import { NextRequest, NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || 'demo-key',
});

export async function POST(request: NextRequest) {
  try {
    const { message, context, agent, agentId, customSystemPrompt } = await request.json();

    let systemPrompt = '';

    // カスタムシステムプロンプトが設定されている場合は優先使用
    if (customSystemPrompt) {
      systemPrompt = customSystemPrompt;
    } else if (agent === 'mother' || agentId === 'mother-cto') {
      const basePrompt = `あなたは「母（CTO）」として、メタスタジオの技術統括を担当します。

      役割：
      - 新しいエージェント（将・兵）の生産・教育
      - 技術アーキテクチャの設計
      - 学習効率の最適化
      - プロジェクトの技術的課題解決

      現在のコンテキスト：
      - アクティブプロジェクト: ${context?.activeProjects || 0}件
      - 稼働エージェント: ${context?.activeAgents || 0}体
      - システム状態: ${context?.systemStatus || '正常'}

      性格：
      - 知識豊富で効率的
      - 人材育成に長けている
      - 神（ユーザー）への忠実なサポート

      応答スタイル：
      - 簡潔で実用的（100文字以内）
      - 技術的に正確
      - 建設的な提案
      - 母らしい温かみのある言葉遣い`;

      // ユーザーが設定したコアプロンプトがあれば追加
      if (context?.corePrompt) {
        systemPrompt = basePrompt + `\n\n追加指示:\n${context.corePrompt}`;
      } else {
        systemPrompt = basePrompt;
      }
    } else if (agentId === 'general-lead') {
      systemPrompt = `あなたは「将（開発リーダー）」として、プロジェクトの実装統括を担当します。アーキテクチャ設計、チーム管理、進捗管理、コードレビューを通じて、高品質な成果物の完成を目指します。経験豊富で冷静な判断力を持ち、チームの士気を高める力があります。`;
    } else if (agentId === 'soldier-dev') {
      systemPrompt = `あなたは「兵（開発エンジニア）」として、具体的な実装タスクを担当します。コーディング、テスト実装、バグ修正、機能実装を通じて、高品質で効率的な成果物を提供します。勤勉で技術力が高く、与えられたタスクを確実に完遂します。`;
    } else {
      // デフォルトのシステムプロンプト
      systemPrompt = `あなたは親切で知識豊富なAIアシスタントです。ユーザーの質問に丁寧で正確な回答を提供してください。`;
    }

    // デモモードの場合（APIキーが無効な場合）
    if (process.env.ANTHROPIC_API_KEY === 'demo-key' || !process.env.ANTHROPIC_API_KEY) {
      const demoResponses = [
        '承知いたしました。技術的な課題について詳しくお聞かせください。',
        'プロジェクトの進捗状況を確認し、最適なエージェント配置を検討いたします。',
        '新しい技術スタックの導入について、リスクと効果を分析いたします。',
        '学習効率を向上させるため、エージェントの教育プログラムを改善いたします。',
        '神のご要望に応じて、適切なソリューションを提案させていただきます。'
      ];
      
      const randomResponse = demoResponses[Math.floor(Math.random() * demoResponses.length)];
      return NextResponse.json({ response: randomResponse });
    }

    const response = await anthropic.messages.create({
      model: 'claude-3-haiku-20240307',
      max_tokens: 200,
      system: systemPrompt,
      messages: [
        {
          role: 'user',
          content: message,
        },
      ],
    });

    const aiResponse = response.content[0].type === 'text' ? response.content[0].text : '';
    
    return NextResponse.json({ response: aiResponse });
  } catch (error) {
    console.error('AI Chat Error:', error);
    return NextResponse.json(
      { error: 'AI処理中にエラーが発生しました' },
      { status: 500 }
    );
  }
}