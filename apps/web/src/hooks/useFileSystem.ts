import { useState, useEffect, useCallback } from 'react';

export interface FileItem {
  name: string;
  type: 'file' | 'directory';
  path: string;
  fullPath?: string;
  relativePath?: string;
  size?: number;
  modified?: string;
  extension?: string;
}

export interface FileSystemState {
  currentPath: string;
  items: FileItem[];
  loading: boolean;
  error: string | null;
}

export const useFileSystem = (initialPath: string = '/', rootPath: string = '/Users/<USER>/Dev/meta-studio') => {
  const [state, setState] = useState<FileSystemState>({
    currentPath: initialPath,
    items: [],
    loading: false,
    error: null,
  });

  const loadDirectory = useCallback(async (path: string) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const response = await fetch(`/api/files?path=${encodeURIComponent(path)}&root=${encodeURIComponent(rootPath)}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.error) {
        throw new Error(data.error);
      }
      
      setState({
        currentPath: data.path,
        items: data.items,
        loading: false,
        error: null,
      });
    } catch (error) {
      console.error('Directory loading error:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }));
    }
  }, []);

  const navigateToPath = useCallback((path: string) => {
    loadDirectory(path);
  }, [loadDirectory]);

  const refreshCurrentDirectory = useCallback(() => {
    loadDirectory(state.currentPath);
  }, [loadDirectory, state.currentPath]);

  const createFile = useCallback(async (name: string, content: string = '') => {
    try {
      const response = await fetch('/api/files', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create_file',
          path: state.currentPath,
          name,
          content
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create file');
      }
      
      // ディレクトリを再読み込み
      await refreshCurrentDirectory();
      return true;
    } catch (error) {
      console.error('Create file error:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to create file'
      }));
      return false;
    }
  }, [state.currentPath, refreshCurrentDirectory]);

  const createFolder = useCallback(async (name: string) => {
    try {
      const response = await fetch('/api/files', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create_folder',
          path: state.currentPath,
          name
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create folder');
      }
      
      // ディレクトリを再読み込み
      await refreshCurrentDirectory();
      return true;
    } catch (error) {
      console.error('Create folder error:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to create folder'
      }));
      return false;
    }
  }, [state.currentPath, refreshCurrentDirectory]);

  const deleteItem = useCallback(async (path: string) => {
    try {
      const response = await fetch(`/api/files?path=${encodeURIComponent(path)}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete item');
      }
      
      // ディレクトリを再読み込み
      await refreshCurrentDirectory();
      return true;
    } catch (error) {
      console.error('Delete item error:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to delete item'
      }));
      return false;
    }
  }, [refreshCurrentDirectory]);

  const renameItem = useCallback(async (oldPath: string, newName: string) => {
    try {
      const response = await fetch('/api/files', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ oldPath, newName })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to rename item');
      }
      
      // ディレクトリを再読み込み
      await refreshCurrentDirectory();
      return true;
    } catch (error) {
      console.error('Rename item error:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to rename item'
      }));
      return false;
    }
  }, [refreshCurrentDirectory]);

  const searchFiles = useCallback((query: string): FileItem[] => {
    if (!query.trim()) return state.items;
    
    const searchTerm = query.toLowerCase();
    return state.items.filter(item => 
      item.name.toLowerCase().includes(searchTerm) ||
      (item.extension && item.extension.toLowerCase().includes(searchTerm))
    );
  }, [state.items]);

  useEffect(() => {
    loadDirectory(initialPath);
  }, [loadDirectory, initialPath]);

  return {
    ...state,
    loadDirectory,
    navigateToPath,
    refreshCurrentDirectory,
    createFile,
    createFolder,
    deleteItem,
    renameItem,
    searchFiles,
  };
};