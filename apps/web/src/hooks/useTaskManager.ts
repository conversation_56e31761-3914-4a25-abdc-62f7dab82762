import { useState, useEffect, useCallback } from 'react'
import { useNotifications } from './useNotifications'

export interface Task {
  id: string
  content: string
  status: 'pending' | 'in_progress' | 'completed' | 'archived'
  priority: 'high' | 'medium' | 'low'
  difficulty: 'high' | 'medium' | 'low'
  tags: string[]
  assignee?: string
  dueDate?: string
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  estimatedTime?: number
  actualTime?: number
  parentId?: string
  subtasks?: Task[]
}

export interface ProgressStats {
  total: number
  completed: number
  inProgress: number
  pending: number
  completionRate: number
}

export interface TaskFilter {
  priority?: string
  status?: string
  search?: string
  assignee?: string
  dueDate?: string
  tags?: string[]
}

export interface UseTaskManagerOptions {
  autoSync?: boolean
  syncInterval?: number
  persistToLocalStorage?: boolean
  syncWithTodoWrite?: boolean
}

export const useTaskManager = (options: UseTaskManagerOptions = {}) => {
  const {
    autoSync = true,
    syncInterval = 30000, // 30秒
    persistToLocalStorage = true,
    syncWithTodoWrite = true
  } = options

  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [progressStats, setProgressStats] = useState<ProgressStats>({
    total: 0,
    completed: 0,
    inProgress: 0,
    pending: 0,
    completionRate: 0
  })
  const [duplicateWarnings, setDuplicateWarnings] = useState<string[]>([])
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null)

  const { showNotification, playSound } = useNotifications()

  // 進捗統計の計算
  const calculateProgressStats = useCallback((taskList: Task[]): ProgressStats => {
    const total = taskList.length
    const completed = taskList.filter(t => t.status === 'completed').length
    const inProgress = taskList.filter(t => t.status === 'in_progress').length
    const pending = taskList.filter(t => t.status === 'pending').length
    const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0

    return { total, completed, inProgress, pending, completionRate }
  }, [])

  // 重複検出
  const detectDuplicates = useCallback((taskList: Task[]): string[] => {
    const similarities: string[] = []
    const contentMap = new Map<string, Task[]>()

    taskList.forEach(task => {
      const normalizedContent = task.content.toLowerCase().trim()
      if (!contentMap.has(normalizedContent)) {
        contentMap.set(normalizedContent, [])
      }
      contentMap.get(normalizedContent)!.push(task)
    })

    contentMap.forEach((tasks, content) => {
      if (tasks.length > 1) {
        similarities.push(`重複: "${content}" (${tasks.length}個)`)
      }
    })

    return similarities
  }, [])

  // ローカルストレージの処理
  const saveToLocalStorage = useCallback((taskList: Task[]) => {
    if (persistToLocalStorage) {
      try {
        localStorage.setItem('metastudio-tasks', JSON.stringify(taskList))
        localStorage.setItem('metastudio-tasks-meta', JSON.stringify({
          lastUpdated: new Date().toISOString(),
          version: '2.0.0'
        }))
      } catch (error) {
        console.error('ローカルストレージ保存エラー:', error)
      }
    }
  }, [persistToLocalStorage])

  const loadFromLocalStorage = useCallback((): Task[] => {
    if (!persistToLocalStorage) return []
    
    try {
      const saved = localStorage.getItem('metastudio-tasks')
      if (saved) {
        const parsed = JSON.parse(saved).map((task: any) => ({
          ...task,
          createdAt: new Date(task.createdAt),
          updatedAt: new Date(task.updatedAt),
          completedAt: task.completedAt ? new Date(task.completedAt) : undefined
        }))
        return parsed
      }
    } catch (error) {
      console.error('ローカルストレージ読み込みエラー:', error)
    }
    return []
  }, [persistToLocalStorage])

  // task.mdとの同期
  const syncWithTaskMd = useCallback(async (): Promise<{ success: boolean; message?: string }> => {
    setLoading(true)
    setError(null)

    try {
      // task.mdからデータを読み込み
      const response = await fetch('/api/task-md', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      if (data.success && data.parsed) {
        const { realtimeTasks, completedTasks, pendingTasks } = data.parsed
        const allTasks = [...realtimeTasks, ...completedTasks, ...pendingTasks]

        setTasks(allTasks)
        saveToLocalStorage(allTasks)
        setLastSyncTime(new Date())
        
        showNotification('task.md同期完了', 'info')
        playSound('success')

        return { success: true, message: 'task.mdとの同期が完了しました' }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '不明なエラー'
      setError(`task.md同期エラー: ${errorMessage}`)
      showNotification(`同期エラー: ${errorMessage}`, 'error')
      playSound('error')
      return { success: false, message: errorMessage }
    } finally {
      setLoading(false)
    }

    return { success: false, message: '同期に失敗しました' }
  }, [showNotification, playSound, saveToLocalStorage])

  // task.mdへの書き込み
  const writeToTaskMd = useCallback(async (): Promise<{ success: boolean; message?: string }> => {
    setLoading(true)
    setError(null)

    try {
      const completedTasks = tasks.filter(t => t.status === 'completed')
      const pendingTasks = tasks.filter(t => t.status !== 'completed')

      const response = await fetch('/api/task-md', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          completedTasks,
          pendingTasks,
          progressStats
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      if (data.success) {
        setLastSyncTime(new Date())
        showNotification('task.md更新完了', 'success')
        playSound('success')
        return { success: true, message: 'task.mdの更新が完了しました' }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '不明なエラー'
      setError(`task.md更新エラー: ${errorMessage}`)
      showNotification(`更新エラー: ${errorMessage}`, 'error')
      playSound('error')
      return { success: false, message: errorMessage }
    } finally {
      setLoading(false)
    }

    return { success: false, message: '更新に失敗しました' }
  }, [tasks, progressStats, showNotification, playSound])

  // TodoWriteツールとの連携（claude code向け）
  const syncWithTodoWrite = useCallback(async () => {
    if (!syncWithTodoWrite) return

    try {
      // TodoWriteツール形式に変換
      const todoWriteFormat = tasks.map(task => ({
        id: task.id,
        content: task.content,
        status: task.status,
        priority: task.priority
      }))

      // Claude Code環境では直接TodoWriteツールを呼び出せないため、
      // 代わりにローカルストレージに保存して連携
      localStorage.setItem('metastudio-todowrite-sync', JSON.stringify({
        todos: todoWriteFormat,
        lastSync: new Date().toISOString(),
        stats: progressStats
      }))

      console.log('TodoWrite連携データを更新:', todoWriteFormat)
    } catch (error) {
      console.error('TodoWrite連携エラー:', error)
    }
  }, [tasks, progressStats, syncWithTodoWrite])

  // タスクの作成
  const createTask = useCallback((taskData: Partial<Task>): Task => {
    const newTask: Task = {
      id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content: taskData.content || '',
      status: taskData.status || 'pending',
      priority: taskData.priority || 'medium',
      difficulty: taskData.difficulty || 'medium',
      tags: taskData.tags || [],
      assignee: taskData.assignee,
      dueDate: taskData.dueDate,
      createdAt: new Date(),
      updatedAt: new Date(),
      estimatedTime: taskData.estimatedTime,
      parentId: taskData.parentId,
      subtasks: []
    }

    setTasks(prev => {
      const updated = [...prev, newTask]
      saveToLocalStorage(updated)
      return updated
    })

    showNotification(`新規タスク作成: ${newTask.content}`, 'info')
    playSound('create')

    return newTask
  }, [saveToLocalStorage, showNotification, playSound])

  // タスクの更新
  const updateTask = useCallback((taskId: string, updates: Partial<Task>) => {
    setTasks(prev => {
      const updated = prev.map(task => 
        task.id === taskId 
          ? { 
              ...task, 
              ...updates, 
              updatedAt: new Date(),
              completedAt: updates.status === 'completed' && task.status !== 'completed' 
                ? new Date() 
                : updates.status !== 'completed' 
                  ? undefined 
                  : task.completedAt
            }
          : task
      )
      saveToLocalStorage(updated)
      return updated
    })

    // 完了時の通知
    if (updates.status === 'completed') {
      const task = tasks.find(t => t.id === taskId)
      if (task && task.status !== 'completed') {
        showNotification(`タスク完了: ${task.content}`, 'success')
        playSound('complete')
      }
    }
  }, [tasks, saveToLocalStorage, showNotification, playSound])

  // タスクの削除
  const deleteTask = useCallback((taskId: string) => {
    const task = tasks.find(t => t.id === taskId)
    if (task) {
      setTasks(prev => {
        const updated = prev.filter(t => t.id !== taskId)
        saveToLocalStorage(updated)
        return updated
      })
      
      showNotification(`タスク削除: ${task.content}`, 'warning')
      playSound('delete')
    }
  }, [tasks, saveToLocalStorage, showNotification, playSound])

  // バルク操作
  const bulkUpdateTasks = useCallback((taskIds: string[], updates: Partial<Task>) => {
    setTasks(prev => {
      const updated = prev.map(task => 
        taskIds.includes(task.id) 
          ? { ...task, ...updates, updatedAt: new Date() }
          : task
      )
      saveToLocalStorage(updated)
      return updated
    })

    showNotification(`${taskIds.length}件のタスクを一括更新`, 'info')
  }, [saveToLocalStorage, showNotification])

  // 完了タスクの自動アーカイブ
  const autoArchiveCompleted = useCallback((olderThanDays: number = 30) => {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays)

    const archivedIds: string[] = []
    setTasks(prev => {
      const updated = prev.map(task => {
        if (task.status === 'completed' && 
            task.completedAt && 
            task.completedAt < cutoffDate) {
          archivedIds.push(task.id)
          return { ...task, status: 'archived' as const, updatedAt: new Date() }
        }
        return task
      })
      
      if (archivedIds.length > 0) {
        saveToLocalStorage(updated)
        showNotification(`${archivedIds.length}件の完了タスクをアーカイブ`, 'info')
      }
      
      return updated
    })
  }, [saveToLocalStorage, showNotification])

  // フィルタリング
  const filterTasks = useCallback((filter: TaskFilter): Task[] => {
    return tasks.filter(task => {
      if (filter.priority && filter.priority !== 'all' && task.priority !== filter.priority) return false
      if (filter.status && filter.status !== 'all' && task.status !== filter.status) return false
      if (filter.assignee && task.assignee !== filter.assignee) return false
      if (filter.search && !task.content.toLowerCase().includes(filter.search.toLowerCase())) return false
      if (filter.dueDate && task.dueDate !== filter.dueDate) return false
      if (filter.tags && filter.tags.length > 0 && !filter.tags.some(tag => task.tags.includes(tag))) return false
      return true
    })
  }, [tasks])

  // 初期化
  useEffect(() => {
    const initialTasks = loadFromLocalStorage()
    if (initialTasks.length > 0) {
      setTasks(initialTasks)
    }
  }, [loadFromLocalStorage])

  // 統計・重複検出の更新
  useEffect(() => {
    if (tasks.length > 0) {
      const stats = calculateProgressStats(tasks)
      setProgressStats(stats)
      
      const duplicates = detectDuplicates(tasks)
      setDuplicateWarnings(duplicates)

      // TodoWrite連携
      syncWithTodoWrite()
    }
  }, [tasks, calculateProgressStats, detectDuplicates, syncWithTodoWrite])

  // 自動同期
  useEffect(() => {
    if (!autoSync) return

    const interval = setInterval(() => {
      if (tasks.length > 0) {
        writeToTaskMd()
      }
    }, syncInterval)

    return () => clearInterval(interval)
  }, [autoSync, syncInterval, tasks, writeToTaskMd])

  return {
    // データ
    tasks,
    progressStats,
    duplicateWarnings,
    lastSyncTime,
    loading,
    error,

    // 基本操作
    createTask,
    updateTask,
    deleteTask,
    bulkUpdateTasks,

    // 同期・連携
    syncWithTaskMd,
    writeToTaskMd,

    // ユーティリティ
    filterTasks,
    autoArchiveCompleted,

    // 状態管理
    setTasks,
    setError
  }
}