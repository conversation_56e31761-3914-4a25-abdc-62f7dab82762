'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

export interface VoiceInputOptions {
  language?: string;
  continuous?: boolean;
  interimResults?: boolean;
  maxAlternatives?: number;
}

export const useContinuousVoiceInput = (options: VoiceInputOptions = {}) => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [isSupported, setIsSupported] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const recognitionRef = useRef<any>(null);
  const restartTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // デフォルトオプション
  const {
    language = 'ja-JP',
    continuous = true,
    interimResults = true,
    maxAlternatives = 1
  } = options;

  // ブラウザサポート確認
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const SpeechRecognition = 
        window.SpeechRecognition || 
        (window as any).webkitSpeechRecognition;
      
      setIsSupported(!!SpeechRecognition);
      
      if (SpeechRecognition) {
        recognitionRef.current = new SpeechRecognition();
        setupRecognition();
      }
    }
  }, []);

  const setupRecognition = useCallback(() => {
    if (!recognitionRef.current) return;

    const recognition = recognitionRef.current;
    
    recognition.continuous = continuous;
    recognition.interimResults = interimResults;
    recognition.lang = language;
    recognition.maxAlternatives = maxAlternatives;

    recognition.onstart = () => {
      setError(null);
      console.log('音声認識開始');
    };

    recognition.onresult = (event: any) => {
      let finalTranscript = '';
      let interim = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        const transcriptText = result[0].transcript;

        if (result.isFinal) {
          finalTranscript += transcriptText;
          
          // 「どうぞ」キーワードで停止
          if (transcriptText.toLowerCase().includes('どうぞ')) {
            finalTranscript = finalTranscript.replace(/どうぞ/gi, '').trim();
            stopListening();
          }
        } else {
          interim += transcriptText;
        }
      }

      if (finalTranscript) {
        setTranscript(prev => prev + finalTranscript);
      }
      setInterimTranscript(interim);
    };

    recognition.onerror = (event: any) => {
      console.error('音声認識エラー:', event.error);
      setError(`音声認識エラー: ${event.error}`);
      
      // ネットワークエラーなどで自動再開
      if (event.error === 'network' || event.error === 'aborted') {
        restartListening();
      }
    };

    recognition.onend = () => {
      console.log('音声認識終了');
      
      // 意図的な停止でない場合は自動再開
      if (isListening && !error) {
        restartListening();
      }
    };
  }, [continuous, interimResults, language, maxAlternatives, isListening, error]);

  const startListening = useCallback(() => {
    if (!isSupported || !recognitionRef.current) {
      setError('音声認識がサポートされていません。Chrome、Edge、Safariをお使いください。');
      return;
    }

    try {
      setIsListening(true);
      setError(null);
      console.log('音声認識を開始します...');
      recognitionRef.current.start();
    } catch (err: any) {
      console.error('音声認識の開始に失敗:', err);
      setError(`音声認識の開始に失敗: ${err.message || '不明なエラー'}`);
      setIsListening(false);
    }
  }, [isSupported]);

  const stopListening = useCallback(() => {
    if (recognitionRef.current) {
      setIsListening(false);
      recognitionRef.current.stop();
      
      // 再開タイマーをクリア
      if (restartTimeoutRef.current) {
        clearTimeout(restartTimeoutRef.current);
        restartTimeoutRef.current = null;
      }
    }
  }, []);

  const restartListening = useCallback(() => {
    if (!isListening) return;
    
    // 少し待ってから再開
    restartTimeoutRef.current = setTimeout(() => {
      if (isListening && recognitionRef.current) {
        try {
          recognitionRef.current.start();
        } catch (err) {
          console.error('音声認識の再開に失敗:', err);
        }
      }
    }, 100);
  }, [isListening]);

  const resetTranscript = useCallback(() => {
    setTranscript('');
    setInterimTranscript('');
  }, []);

  // クリーンアップ
  useEffect(() => {
    return () => {
      if (restartTimeoutRef.current) {
        clearTimeout(restartTimeoutRef.current);
      }
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, []);

  return {
    isListening,
    transcript,
    interimTranscript,
    isSupported,
    error,
    startListening,
    stopListening,
    resetTranscript,
    // 全テキスト（確定 + 暫定）
    fullTranscript: transcript + interimTranscript
  };
};