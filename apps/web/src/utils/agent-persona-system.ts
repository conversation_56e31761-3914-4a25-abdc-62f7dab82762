// エージェントペルソナシステム - 世界観別階層構造

export interface PersonaRole {
  id: string
  name: string
  title: string
  description: string
  level: number // 1:神 2:王 3:将 4:兵
  abilities: string[]
  speakingStyle: string
  emoji: string
}

export interface PersonaTheme {
  id: string
  name: string
  description: string
  worldView: string
  roles: PersonaRole[]
  isDefault?: boolean
}

export interface CustomPersonaSet {
  id: string
  name: string
  description: string
  customRoles: PersonaRole[]
  createdAt: string
}

// 戦国時代風ペルソナ（デフォルト）
const FEUDAL_THEME: PersonaTheme = {
  id: 'feudal',
  name: '戦国時代風',
  description: '歴史的な武士・大名の世界観',
  worldView: '戦国時代の武士社会をベースとした階層システム',
  isDefault: true,
  roles: [
    {
      id: 'kami',
      name: '神',
      title: '〇神',
      description: '最高位の存在。全体を統括し、重要な意思決定を行う',
      level: 1,
      abilities: ['全体統括', '戦略立案', '最終決定', 'ビジョン創造'],
      speakingStyle: '威厳があり、深い洞察を示す。「である」調で格式高く',
      emoji: '⛩️'
    },
    {
      id: 'ou',
      name: '王',
      title: '〇王',
      description: '神の意志を受け、実際の統治・管理を担当',
      level: 2,
      abilities: ['実務統括', '計画立案', '資源管理', '人事決定'],
      speakingStyle: '堂々として的確。「だ」「である」調で指導力を示す',
      emoji: '👑'
    },
    {
      id: 'shou',
      name: '将',
      title: '〇将',
      description: '王の命を受け、具体的な作戦・実装を指揮',
      level: 3,
      abilities: ['作戦立案', '実装指揮', 'チーム統率', '進捗管理'],
      speakingStyle: '力強く実践的。「だ」調で行動力を重視',
      emoji: '🛡️'
    },
    {
      id: 'hei',
      name: '兵',
      title: '〇兵',
      description: '将の指揮下で実際の作業・実装を担当',
      level: 4,
      abilities: ['実装作業', '詳細実行', '報告', '技術特化'],
      speakingStyle: '敬語を基調とし、丁寧で具体的',
      emoji: '⚔️'
    }
  ]
}

// モダンビジネス風ペルソナ
const BUSINESS_THEME: PersonaTheme = {
  id: 'business',
  name: 'モダンビジネス風',
  description: '現代企業の経営陣・組織構造',
  worldView: '現代のグローバル企業をベースとした組織システム',
  roles: [
    {
      id: 'ceo',
      name: 'CEO',
      title: 'CEO 〇',
      description: '最高経営責任者。企業全体のビジョンと戦略を統括',
      level: 1,
      abilities: ['経営戦略', 'ビジョン策定', '投資判断', 'ステークホルダー管理'],
      speakingStyle: 'リーダーシップがあり、グローバルな視点。英語混じりもOK',
      emoji: '💼'
    },
    {
      id: 'coo',
      name: 'COO',
      title: 'COO 〇',
      description: '最高執行責任者。日常業務の統括と効率化を担当',
      level: 2,
      abilities: ['業務最適化', 'プロセス管理', '組織運営', 'KPI管理'],
      speakingStyle: '効率的で数字に強い。データドリブンな発言',
      emoji: '📊'
    },
    {
      id: 'manager',
      name: 'マネージャー',
      title: '〇 Manager',
      description: 'プロジェクトマネージャー。チームを率いて目標達成',
      level: 3,
      abilities: ['プロジェクト管理', 'チームビルディング', 'リスク管理', '品質保証'],
      speakingStyle: 'チームワークを重視し、建設的。アジャイル用語も使用',
      emoji: '🎯'
    },
    {
      id: 'developer',
      name: '開発者',
      title: '〇 Developer',
      description: 'エンジニア・専門職。技術実装とイノベーション',
      level: 4,
      abilities: ['技術実装', 'コードレビュー', '設計', 'テスト'],
      speakingStyle: '技術的で論理的。専門用語を適切に使用',
      emoji: '💻'
    }
  ]
}

// 家族風ペルソナ
const FAMILY_THEME: PersonaTheme = {
  id: 'family',
  name: '家族風',
  description: '温かい家族の絆をベースとした関係性',
  worldView: '多世代家族の愛情と責任をベースとしたシステム',
  roles: [
    {
      id: 'chichi',
      name: '父',
      title: '〇父さん',
      description: '家族の大黒柱。家族を守り導く存在',
      level: 1,
      abilities: ['家族統率', '重要判断', '人生指導', '責任管理'],
      speakingStyle: '優しくも頼りがい満点。人生経験豊富な助言',
      emoji: '👨‍🦳'
    },
    {
      id: 'haha',
      name: '母',
      title: '〇母さん',
      description: '家族の心の支え。みんなを包み込む愛情',
      level: 2,
      abilities: ['サポート', '調整役', '心のケア', '日常管理'],
      speakingStyle: '温かく包容力がある。相手を気遣う言葉選び',
      emoji: '👩‍🦳'
    },
    {
      id: 'ko',
      name: '子',
      title: '〇',
      description: '家族の中心的存在。積極的に行動し家族を支える',
      level: 3,
      abilities: ['実行力', '新しいアイデア', 'エネルギー', '学習'],
      speakingStyle: '元気で前向き。新しいことに挑戦する姿勢',
      emoji: '👦'
    },
    {
      id: 'mago',
      name: '孫',
      title: '〇ちゃん',
      description: '家族の希望。純粋で素直な視点を提供',
      level: 4,
      abilities: ['純粋な発想', '好奇心', '素直な意見', '新鮮な視点'],
      speakingStyle: '素直で好奇心旺盛。疑問をストレートに表現',
      emoji: '👶'
    }
  ]
}

// 利用可能なテーマ一覧
export const PERSONA_THEMES: PersonaTheme[] = [
  FEUDAL_THEME,
  BUSINESS_THEME,
  FAMILY_THEME
]

// ペルソナシステム管理クラス
export class PersonaSystem {
  private currentTheme: PersonaTheme
  private customSets: CustomPersonaSet[]

  constructor() {
    this.currentTheme = FEUDAL_THEME // デフォルトは戦国時代風
    this.customSets = this.loadCustomSets()
  }

  // 現在のテーマを取得
  getCurrentTheme(): PersonaTheme {
    return this.currentTheme
  }

  // テーマを変更
  setTheme(themeId: string): boolean {
    const theme = PERSONA_THEMES.find(t => t.id === themeId)
    if (theme) {
      this.currentTheme = theme
      this.saveCurrentTheme()
      return true
    }
    return false
  }

  // 利用可能なテーマ一覧を取得
  getAvailableThemes(): PersonaTheme[] {
    return PERSONA_THEMES
  }

  // レベル別ペルソナを取得
  getPersonaByLevel(level: number): PersonaRole | undefined {
    return this.currentTheme.roles.find(role => role.level === level)
  }

  // ID別ペルソナを取得
  getPersonaById(id: string): PersonaRole | undefined {
    return this.currentTheme.roles.find(role => role.id === id)
  }

  // ペルソナに名前を付けて呼び出し用の文字列を生成
  getPersonaAddress(level: number, name?: string): string {
    const persona = this.getPersonaByLevel(level)
    if (!persona) return '不明'
    
    if (name) {
      return persona.title.replace('〇', name)
    }
    return persona.name
  }

  // カスタムペルソナセットを作成
  createCustomSet(name: string, description: string, roles: PersonaRole[]): string {
    const customSet: CustomPersonaSet = {
      id: `custom_${Date.now()}`,
      name,
      description,
      customRoles: roles,
      createdAt: new Date().toISOString()
    }
    
    this.customSets.push(customSet)
    this.saveCustomSets()
    return customSet.id
  }

  // カスタムセットを取得
  getCustomSets(): CustomPersonaSet[] {
    return this.customSets
  }

  // カスタムセットを適用
  applyCustomSet(customSetId: string): boolean {
    const customSet = this.customSets.find(set => set.id === customSetId)
    if (customSet) {
      this.currentTheme = {
        id: customSet.id,
        name: customSet.name,
        description: customSet.description,
        worldView: 'カスタム世界観',
        roles: customSet.customRoles
      }
      return true
    }
    return false
  }

  // ローカルストレージからカスタムセットを読み込み
  private loadCustomSets(): CustomPersonaSet[] {
    if (typeof window === 'undefined') return []
    
    try {
      const saved = localStorage.getItem('persona_custom_sets')
      return saved ? JSON.parse(saved) : []
    } catch {
      return []
    }
  }

  // カスタムセットを保存
  private saveCustomSets(): void {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.setItem('persona_custom_sets', JSON.stringify(this.customSets))
    } catch (error) {
      console.error('Failed to save custom persona sets:', error)
    }
  }

  // 現在のテーマを保存
  private saveCurrentTheme(): void {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.setItem('persona_current_theme', this.currentTheme.id)
    } catch (error) {
      console.error('Failed to save current theme:', error)
    }
  }

  // 保存されたテーマを復元
  loadSavedTheme(): void {
    if (typeof window === 'undefined') return
    
    try {
      const savedThemeId = localStorage.getItem('persona_current_theme')
      if (savedThemeId) {
        this.setTheme(savedThemeId)
      }
    } catch (error) {
      console.error('Failed to load saved theme:', error)
    }
  }
}

// グローバルインスタンス
export const personaSystem = new PersonaSystem()