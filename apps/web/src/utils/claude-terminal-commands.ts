// Claude Code設定管理用ターミナルコマンド

export async function exportClaudeConfig(): Promise<string> {
  try {
    const response = await fetch('/api/claude-config')
    const data = await response.json()
    
    if (data.success) {
      // ダウンロード用のBlobを作成
      const blob = new Blob([JSON.stringify(data.configs, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `claude-config-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      return `✅ Claude設定をエクスポートしました: ${a.download}`
    } else {
      return `❌ エクスポートエラー: ${data.error}`
    }
  } catch (error) {
    return `❌ エクスポート失敗: ${error}`
  }
}

export async function importClaudeConfig(configJson: string): Promise<string> {
  try {
    const config = JSON.parse(configJson)
    
    const response = await fetch('/api/claude-config', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ config: config.metaStudio || config })
    })
    
    const data = await response.json()
    
    if (data.success) {
      return `✅ Claude設定をインポートしました\n更新された設定:\n${JSON.stringify(data.config, null, 2)}`
    } else {
      return `❌ インポートエラー: ${data.error}`
    }
  } catch (error) {
    return `❌ インポート失敗: 有効なJSONを指定してください`
  }
}

export async function setClaudePermission(path: string, autoApprove = true): Promise<string> {
  try {
    // 新しいパーミッションAPIを使用
    const response = await fetch('/api/claude-permissions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        path: path || process.cwd(),
        type: 'directory',
        permissions: ['read', 'write', 'execute'],
        autoApprove
      })
    })
    
    const data = await response.json()
    
    if (data.success) {
      return `✅ パーミッション設定を更新しました
📁 パス: ${data.entry.path}
🔐 権限: ${data.entry.permissions.join(', ')}
✨ 自動承認: ${data.entry.autoApprove ? '有効' : '無効'}
📅 作成日時: ${new Date(data.entry.createdAt).toLocaleString('ja-JP')}`
    } else {
      return `❌ パーミッション設定エラー: ${data.error}`
    }
  } catch (error) {
    return `❌ パーミッション設定失敗: ${error}`
  }
}

export async function getClaudePermissions(): Promise<string> {
  try {
    const response = await fetch('/api/claude-permissions')
    const data = await response.json()
    
    if (data.success) {
      let result = `📋 現在のパーミッション設定:\n`
      result += `グローバル自動承認: ${data.globalAutoApprove ? '✅ 有効' : '❌ 無効'}\n`
      result += `登録済みパス: ${data.totalEntries}件\n\n`
      
      if (data.entries.length > 0) {
        result += `許可されたパス:\n`
        data.entries.forEach((entry: any) => {
          result += `  📁 ${entry.path}\n`
          result += `     - タイプ: ${entry.type}\n`
          result += `     - 権限: ${entry.permissions.join(', ')}\n`
          result += `     - 自動承認: ${entry.autoApprove ? '有効' : '無効'}\n`
          result += `     - 作成日: ${new Date(entry.createdAt).toLocaleString('ja-JP')}\n`
          if (entry.lastUsed) {
            result += `     - 最終使用: ${new Date(entry.lastUsed).toLocaleString('ja-JP')}\n`
          }
          result += '\n'
        })
      } else {
        result += '  （パーミッション未設定）\n'
      }
      
      return result
    } else {
      return `❌ パーミッション取得エラー: ${data.error}`
    }
  } catch (error) {
    return `❌ パーミッション取得失敗: ${error}`
  }
}

// ターミナルコマンドとして登録するための関数
export const claudeConfigCommands = {
  'export-claude-config': exportClaudeConfig,
  'import-claude-config': importClaudeConfig,
  'claude-permission': setClaudePermission,
  'claude-permissions': getClaudePermissions,
  'claude-allow': (path: string) => setClaudePermission(path || process.cwd(), true),
  'claude-config': async () => {
    const response = await fetch('/api/claude-config')
    const data = await response.json()
    return data.success 
      ? `📋 Claude設定:\n${JSON.stringify(data.configs, null, 2)}`
      : `❌ 設定取得エラー: ${data.error}`
  }
}