// Meta Studio トークン節約のための機能・コマンドリスト

export interface ComponentInfo {
  name: string
  path: string
  purpose: string
  keyFeatures: string[]
  dependencies: string[]
}

export interface ApiEndpoint {
  path: string
  method: string
  purpose: string
  parameters: string[]
  responseType: string
}

export interface TerminalCommand {
  command: string
  description: string
  usage: string
  examples: string[]
}

// Meta Studioの主要コンポーネント一覧
export const MAIN_COMPONENTS: ComponentInfo[] = [
  {
    name: "MetaStudioLayout",
    path: "src/components/MetaStudioLayout.tsx",
    purpose: "メインレイアウト・タブ管理・サイドバー統合",
    keyFeatures: ["タブ管理", "リサイズハンドル", "レスポンシブ対応"],
    dependencies: ["TabManager", "Sidebar", "MetaTerminal"]
  },
  {
    name: "TabManager", 
    path: "src/components/TabManager.tsx",
    purpose: "タブシステム・分割ペイン・ピン機能",
    keyFeatures: ["D&D分割", "ピン固定", "18タブまで対応", "アイコン色分け"],
    dependencies: ["DashboardGrid", "ProjectView", "FileViewer"]
  },
  {
    name: "Sidebar",
    path: "src/components/Sidebar.tsx", 
    purpose: "プロジェクト一覧・システムツール・ファイルツリー",
    keyFeatures: ["プロジェクト管理", "ソート機能", "右クリックメニュー", "検索機能"],
    dependencies: ["ContextMenu", "ProjectManager"]
  },
  {
    name: "MetaTerminal",
    path: "src/components/MetaTerminal.tsx",
    purpose: "xterm.js統合ターミナル・Claude Code統合・IME対応",
    keyFeatures: ["ショートカット", "セッション管理", "ストリーミング表示"],
    dependencies: ["ai-agents.ts", "claude-terminal-commands.ts"]
  },
  {
    name: "DashboardGrid",
    path: "src/components/DashboardGrid.tsx",
    purpose: "ダッシュボードウィジェット・レスポンシブグリッド",
    keyFeatures: ["ドラッグリサイズ", "個人目標", "タイムブロック", "統計表示"],
    dependencies: ["VisionWidget", "TaskWidget", "TimeBlockWidget"]
  },
  {
    name: "VRMViewer",
    path: "src/components/VRMViewer.tsx",
    purpose: "VRMモデル表示・Three.js統合・感情表現",
    keyFeatures: ["3Dモデル表示", "感情同期", "表情制御"],
    dependencies: ["CharacterContext", "Three.js"]
  },
  {
    name: "ISSystem", 
    path: "src/components/ISSystem.tsx",
    purpose: "AI配信者システム・チャット統合・音声認識",
    keyFeatures: ["リアルタイムチャット", "音声入力", "セッション永続化"],
    dependencies: ["MotherChat", "VoiceInput", "CharacterContext"]
  },
  {
    name: "FileViewer",
    path: "src/components/FileViewer.tsx",
    purpose: "ファイル表示・編集・プレビュー",
    keyFeatures: ["markdown表示", "YAML編集", "構文ハイライト"],
    dependencies: ["YamlEditor"]
  },
  {
    name: "ProjectManager",
    path: "src/components/ProjectManager.tsx",
    purpose: "プロジェクト作成・テンプレート管理",
    keyFeatures: ["テンプレート選択", "自動生成", "要件定義統合"],
    dependencies: ["ProjectTemplateModal"]
  },
  {
    name: "SettingsPanel",
    path: "src/components/SettingsPanel.tsx",
    purpose: "設定管理・通知設定・テーマ設定",
    keyFeatures: ["音声通知", "Claude設定", "UI設定"],
    dependencies: ["useNotifications"]
  }
]

// APIエンドポイント一覧
export const API_ENDPOINTS: ApiEndpoint[] = [
  {
    path: "/api/claude-code",
    method: "POST", 
    purpose: "Claude Code SDK統合・ストリーミング対応",
    parameters: ["prompt", "maxTurns", "streaming"],
    responseType: "SSE Stream | JSON"
  },
  {
    path: "/api/claude-config",
    method: "GET|POST|PUT",
    purpose: "Claude設定管理・エクスポート/インポート",
    parameters: ["config", "target", "key", "value"],
    responseType: "ConfigData"
  },
  {
    path: "/api/claude-permissions", 
    method: "GET|POST|PUT|DELETE",
    purpose: "パーミッション管理・自動承認設定",
    parameters: ["path", "type", "permissions", "autoApprove"],
    responseType: "PermissionConfig"
  },
  {
    path: "/api/claude-cost",
    method: "GET",
    purpose: "API使用料・コスト追跡",
    parameters: ["period", "details"],
    responseType: "CostMetrics"
  },
  {
    path: "/api/files",
    method: "GET",
    purpose: "ファイルシステム参照・ディレクトリ構造取得",
    parameters: ["path", "recursive"],
    responseType: "FileTree"
  },
  {
    path: "/api/file-content",
    method: "GET|POST",
    purpose: "ファイル内容読み取り・保存",
    parameters: ["filePath", "content"],
    responseType: "FileContent"
  },
  {
    path: "/api/task-md",
    method: "GET|POST",
    purpose: "task.md読み取り・更新",
    parameters: ["content"],
    responseType: "TaskData"
  },
  {
    path: "/api/ai/chat",
    method: "POST",
    purpose: "母AIチャット・コンテキスト管理",
    parameters: ["message", "context", "agent"],
    responseType: "AIResponse"
  }
]

// ターミナルコマンド一覧
export const TERMINAL_COMMANDS: TerminalCommand[] = [
  {
    command: "claude",
    description: "Claude Code統合（API/CLI自動選択）",
    usage: "claude [プロンプト]",
    examples: ["claude \"Reactコンポーネントを作成\"", "claude \"バグを修正して\""]
  },
  {
    command: "claude-api", 
    description: "Claude Code API強制使用",
    usage: "claude-api [プロンプト]",
    examples: ["claude-api \"パフォーマンス最適化\""]
  },
  {
    command: "claude-cli",
    description: "Claude Code CLI強制使用", 
    usage: "claude-cli [プロンプト]",
    examples: ["claude-cli \"コードレビュー\""]
  },
  {
    command: "export-claude-config",
    description: "Claude設定をJSONエクスポート",
    usage: "export-claude-config",
    examples: ["export-claude-config"]
  },
  {
    command: "import-claude-config",
    description: "JSON設定をインポート",
    usage: "import-claude-config [JSON文字列]",
    examples: ["import-claude-config '{\"metaStudio\": {...}}'"]
  },
  {
    command: "claude-permissions",
    description: "パーミッション設定一覧表示",
    usage: "claude-permissions",
    examples: ["claude-permissions"]
  },
  {
    command: "claude-allow",
    description: "パスを自動承認リストに追加",
    usage: "claude-allow [path]",
    examples: ["claude-allow /Users/<USER>", "claude-allow ."]
  },
  {
    command: "cost",
    description: "API使用料・コスト確認", 
    usage: "cost",
    examples: ["cost"]
  },
  {
    command: "px init",
    description: "新規プロジェクト初期化",
    usage: "px init [プロジェクト名]",
    examples: ["px init my-app"]
  },
  {
    command: "px generate",
    description: "AI自動プロジェクト生成",
    usage: "px generate \"[アイデア]\"", 
    examples: ["px generate \"瞑想アプリ\""]
  },
  {
    command: "help",
    description: "コマンドヘルプ表示",
    usage: "help",
    examples: ["help"]
  },
  {
    command: "status",
    description: "システム状態表示",
    usage: "status", 
    examples: ["status"]
  }
]

// 頻出タスク・操作パターン
export const COMMON_TASKS = {
  "サーバー起動": {
    commands: ["./start-dev.sh", "npm run dev:start"],
    description: "バックグラウンドでNext.jsサーバーを起動"
  },
  "サーバー停止": {
    commands: ["./stop-dev.sh", "npm run dev:stop"], 
    description: "開発サーバーを安全に停止"
  },
  "プロジェクト作成": {
    steps: ["サイドバー→新規プロジェクト", "テンプレート選択", "プロジェクト名入力"],
    description: "新しいプロジェクトを作成してサイドバーに追加"
  },
  "ファイル編集": {
    steps: ["ファイルクリック→FileViewer", "編集モード切り替え", "内容編集→保存"],
    description: "ファイルの内容を編集して保存"
  },
  "タブ分割": {
    steps: ["タブをドラッグ", "分割方向選択", "ペイン調整"],
    description: "画面を分割して複数コンテンツを同時表示"
  },
  "VRM設定": {
    steps: ["AIキャラクター→設定展開", "モデル選択", "感情・ポーズ調整"],
    description: "VRMモデルの読み込みと表情設定"
  },
  "設定エクスポート": {
    commands: ["export-claude-config", "claude-permissions"],
    description: "Claude設定とパーミッションをバックアップ"
  }
}

// トークン節約のための簡潔な参照文字列
export function getQuickReference(): string {
  return `
# Meta Studio 簡潔リファレンス

## 主要コンポーネント
- MetaStudioLayout: メインレイアウト・タブ管理
- TabManager: タブ・分割ペイン・ピン機能  
- Sidebar: プロジェクト一覧・システムツール
- MetaTerminal: xterm.js・Claude統合・ショートカット
- DashboardGrid: ウィジェット・レスポンシブ
- VRMViewer: 3Dモデル・感情表現
- ISSystem: AI配信者・チャット・音声
- FileViewer: ファイル表示・編集
- SettingsPanel: 設定管理・通知

## 重要API
- /api/claude-code: Claude統合・ストリーミング
- /api/claude-config: 設定管理
- /api/claude-permissions: パーミッション
- /api/files: ファイルシステム
- /api/ai/chat: 母AIチャット

## 必須コマンド  
- claude [prompt]: Claude統合
- export-claude-config: 設定保存
- claude-allow [path]: パーミッション許可
- cost: 使用料確認
- px init/generate: プロジェクト作成
- help: ヘルプ表示

## 頻出操作
- サーバー: ./start-dev.sh, ./stop-dev.sh
- プロジェクト: サイドバー→新規→テンプレート選択
- ファイル編集: クリック→編集モード→保存
- タブ分割: ドラッグ→方向選択
- VRM: AIキャラクター→設定→モデル選択

## ショートカット
- Cmd+C/X/V: コピー/カット/ペースト
- Cmd+A/E: 行頭/行末移動
- Cmd+K/U: 行削除/全削除
`;
}

// 特定機能の詳細情報を取得
export function getComponentDetails(componentName: string): ComponentInfo | undefined {
  return MAIN_COMPONENTS.find(comp => comp.name === componentName)
}

export function getApiDetails(apiPath: string): ApiEndpoint | undefined {
  return API_ENDPOINTS.find(api => api.path === apiPath)
}

export function getCommandDetails(command: string): TerminalCommand | undefined {
  return TERMINAL_COMMANDS.find(cmd => cmd.command === command)
}