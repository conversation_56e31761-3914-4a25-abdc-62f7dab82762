import { z } from 'zod';
import { createTRPCRouter, publicProcedure } from '../trpc';

export const agentRouter = createTRPCRouter({
  getAll: publicProcedure.query(async () => {
    // TODO: Supabase integration
    return [
      {
        id: 'king-001',
        name: 'K-meditation',
        type: 'king',
        status: 'busy',
        level: 85,
        experience: 12500,
        skills: ['瞑想', 'UX設計', 'プロダクト管理'],
        currentTask: '瞑想タイマー機能設計',
        performance: 92,
        assignedProject: '瞑想アプリ_projext'
      },
      {
        id: 'mother-001',
        name: 'M-CTO',
        type: 'mother',
        status: 'active',
        level: 95,
        experience: 18000,
        skills: ['技術統括', '人材育成', 'アーキテクチャ', '学習効率化'],
        currentTask: '新エージェント生産・教育',
        performance: 95,
      },
    ];
  }),

  updateStatus: publicProcedure
    .input(z.object({
      id: z.string(),
      status: z.enum(['active', 'busy', 'idle', 'learning', 'offline']),
    }))
    .mutation(async ({ input }) => {
      // TODO: Supabase integration
      console.log('Updating agent status:', input);
      return { success: true };
    }),

  assignToProject: publicProcedure
    .input(z.object({
      agentId: z.string(),
      projectId: z.string(),
    }))
    .mutation(async ({ input }) => {
      // TODO: Supabase integration
      console.log('Assigning agent to project:', input);
      return { success: true };
    }),
});