import { z } from 'zod';
import { createTRPCRouter, publicProcedure } from '../trpc';

export const projectRouter = createTRPCRouter({
  getAll: publicProcedure.query(async () => {
    // TODO: Supabase integration
    return [
      {
        id: '1',
        name: '瞑想アプリ_projext',
        description: '5分、10分、15分のプリセット瞑想タイマーアプリ',
        status: 'development',
        progress: 75,
        king: 'K-001',
        general: 'G-meditation',
        soldiers: 5,
        lastUpdated: new Date(),
        technologies: ['React Native', 'Expo', 'Audio API']
      },
      {
        id: '2', 
        name: '投資bot_projext',
        description: '株価予測・自動取引ボットシステム',
        status: 'testing',
        progress: 90,
        king: 'K-002',
        general: 'G-finance',
        soldiers: 8,
        lastUpdated: new Date(),
        technologies: ['Python', 'FastAPI', 'PostgreSQL', 'ML']
      },
    ];
  }),

  create: publicProcedure
    .input(z.object({
      name: z.string(),
      description: z.string(),
      technologies: z.array(z.string()),
    }))
    .mutation(async ({ input }) => {
      // TODO: Supabase integration
      console.log('Creating project:', input);
      return {
        id: Date.now().toString(),
        ...input,
        status: 'planning',
        progress: 0,
        king: 'K-new',
        general: '',
        soldiers: 0,
        lastUpdated: new Date(),
      };
    }),

  updateProgress: publicProcedure
    .input(z.object({
      id: z.string(),
      progress: z.number().min(0).max(100),
    }))
    .mutation(async ({ input }) => {
      // TODO: Supabase integration
      console.log('Updating progress:', input);
      return { success: true };
    }),
});