import { z } from 'zod';
import { createTRPCRouter, publicProcedure } from '../trpc';

export const userRouter = createTRPCRouter({
  getProfile: publicProcedure.query(async () => {
    // TODO: Supabase Auth integration
    return {
      id: 'god-001',
      name: 'あなた（神）',
      email: '<EMAIL>',
      role: 'god',
      settings: {
        theme: 'light',
        notifications: true,
        language: 'ja',
      },
    };
  }),

  updateSettings: publicProcedure
    .input(z.object({
      theme: z.enum(['light', 'dark', 'metastudio']).optional(),
      notifications: z.boolean().optional(),
      language: z.string().optional(),
    }))
    .mutation(async ({ input }) => {
      // TODO: Supabase integration
      console.log('Updating user settings:', input);
      return { success: true };
    }),
});