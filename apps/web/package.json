{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "NODE_OPTIONS='--max-old-space-size=8192 --no-deprecation' next dev -p 8080 --turbo", "dev:start": "./start-dev.sh", "dev:stop": "./stop-dev.sh", "dev:log": "tail -f dev.log", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@anthropic-ai/claude-code": "^1.0.25", "@mediapipe/tasks-vision": "^0.10.22-rc.20250304", "@pixiv/three-vrm": "^3.4.1", "@supabase/supabase-js": "^2.38.0", "@tanstack/react-query": "^5.8.0", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "@types/react-beautiful-dnd": "^13.1.8", "@types/three": "^0.158.0", "autoprefixer": "^10.4.21", "drizzle-orm": "^0.29.0", "framer-motion": "^10.16.0", "lucide-react": "^0.292.0", "next": "15.3.3", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-grid-layout": "^1.5.1", "react-resizable": "^3.0.5", "three": "^0.158.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-webgl": "^0.16.0", "zod": "^3.22.0", "zustand": "^4.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/xterm": "^3.0.0", "daisyui": "^5.0.0-beta.9", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}