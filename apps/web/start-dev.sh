#!/bin/bash
# Meta Studio開発サーバー起動スクリプト

# 既存プロセスを停止
pkill -f "next dev" || true

# プロセスをバックグラウンドで起動
cd /Users/<USER>/Dev/meta-studio/apps/web
nohup npm run dev > dev.log 2>&1 &

# プロセスIDを保存
echo $! > .dev-server.pid

# 起動待機
sleep 3

# 起動確認
if ps -p $(cat .dev-server.pid) > /dev/null; then
    echo "✅ サーバーが起動しました (PID: $(cat .dev-server.pid))"
    echo "📍 URL: http://localhost:8080"
    echo "📋 ログ: tail -f dev.log"
    echo "🛑 停止: npm run stop"
else
    echo "❌ サーバー起動に失敗しました"
    echo "ログを確認: cat dev.log"
fi