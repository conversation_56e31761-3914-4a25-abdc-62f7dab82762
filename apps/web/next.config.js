/** @type {import('next').NextConfig} */
const nextConfig = {
  // Turbopack設定（安定版）
  turbopack: {},
  
  // 開発サーバー設定
  async rewrites() {
    return []
  },
  
  // パフォーマンス最適化
  compress: true,
  poweredByHeader: false,
  
  // エラーハンドリング改善
  onDemandEntries: {
    maxInactiveAge: 25 * 1000,
    pagesBufferLength: 2,
  },
  
  // 安定性向上
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
      }
    }
    return config
  }
}

module.exports = nextConfig