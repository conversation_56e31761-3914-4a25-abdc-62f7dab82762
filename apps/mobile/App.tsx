import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView } from 'react-native';

export default function App() {
  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      {/* ヘッダー */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>メタスタジオ</Text>
        <Text style={styles.headerSubtitle}>モバイル版</Text>
      </View>

      {/* メインコンテンツ */}
      <ScrollView style={styles.content}>
        
        {/* クイックアクション */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>クイックアクション</Text>
          
          <TouchableOpacity style={[styles.actionCard, styles.primaryCard]}>
            <Text style={styles.actionEmoji}>🎤</Text>
            <Text style={styles.actionTitle}>音声でアイデア投入</Text>
            <Text style={styles.actionSubtitle}>思考を即座にキャプチャ</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.actionCard, styles.secondaryCard]}>
            <Text style={styles.actionEmoji}>📱</Text>
            <Text style={styles.actionTitle}>簡易メモ</Text>
            <Text style={styles.actionSubtitle}>素早くテキスト入力</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.actionCard, styles.accentCard]}>
            <Text style={styles.actionEmoji}>📊</Text>
            <Text style={styles.actionTitle}>プロジェクト状況</Text>
            <Text style={styles.actionSubtitle}>進捗をチェック</Text>
          </TouchableOpacity>
        </View>

        {/* 最近のプロジェクト */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>最近のプロジェクト</Text>
          
          <View style={styles.projectCard}>
            <View style={styles.projectHeader}>
              <Text style={styles.projectName}>瞑想アプリ_projext</Text>
              <Text style={styles.projectProgress}>75%</Text>
            </View>
            <Text style={styles.projectDescription}>瞑想タイマーアプリの開発</Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: '75%' }]} />
            </View>
          </View>

          <View style={styles.projectCard}>
            <View style={styles.projectHeader}>
              <Text style={styles.projectName}>投資bot_projext</Text>
              <Text style={styles.projectProgress}>90%</Text>
            </View>
            <Text style={styles.projectDescription}>株価予測ボットシステム</Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: '90%' }]} />
            </View>
          </View>
        </View>

        {/* 統計 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>統計</Text>
          
          <View style={styles.statsContainer}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>127</Text>
              <Text style={styles.statLabel}>完成プロジェクト</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>92%</Text>
              <Text style={styles.statLabel}>実現率</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>15</Text>
              <Text style={styles.statLabel}>アクティブエージェント</Text>
            </View>
          </View>
        </View>

      </ScrollView>

      {/* フローティングアクションボタン */}
      <TouchableOpacity style={styles.fab}>
        <Text style={styles.fabIcon}>+</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
    backgroundColor: '#3b82f6',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 15,
  },
  actionCard: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  primaryCard: {
    backgroundColor: '#3b82f6',
  },
  secondaryCard: {
    backgroundColor: '#8b5cf6',
  },
  accentCard: {
    backgroundColor: '#06b6d4',
  },
  actionEmoji: {
    fontSize: 32,
    marginBottom: 8,
  },
  actionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  actionSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  projectCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  projectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  projectName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
  },
  projectProgress: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  projectDescription: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 12,
  },
  progressBar: {
    height: 6,
    backgroundColor: '#e2e8f0',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#3b82f6',
    borderRadius: 3,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3b82f6',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    bottom: 30,
    right: 30,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  fabIcon: {
    fontSize: 24,
    color: 'white',
    fontWeight: 'bold',
  },
});
