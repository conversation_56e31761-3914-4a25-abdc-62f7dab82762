# メタスタジオ（脳内現実化ツール）統合完全要件定義書 v4.0 FINAL

📋 プロジェクト概要
正式名称: メタスタジオ（脳内現実化ツール進化版）+ Projext統合開発フレームワーク
アイコン: 😎🤖 サングラス + ビジネススーツロボット
コンセプト: アイデアから実装まで、あらゆる創造的願望を自動実現するオールラウンド開発プラットフォーム + LLM駆動プロジェクト管理システム

## ロールモデル統合（Boost.space追加）

### Core系アプリからの機能統合
| アプリ | 採用機能 | メタスタジオでの実装 |
|--------|----------|---------------------|
| **Obsidian** | 拡張性、MD管理、プラグインシステム | 知識ベース管理、プラグインエコシステム |
| **Craft** | WYSIWYG操作性、D&D、シンプルUI | メインエディタの操作性、ブロック管理 |
| **Lark** | ダッシュボード俯瞰、データベースビュー | 統合ダッシュボード、プロジェクト俯瞰 |
| **Notion** | ブロックエディタ、データベース連携 | Projext構造管理、エンティティ管理 |
| **Boost.space** | Make.com統合、MCP-AI連携、Appflows | エージェント連携、外部ツール統合、ワークフロー自動化 |
| **xTiles** |  一覧としてみられる俯瞰ダッシュボード |
| **Airtable（looker studio）** | ダッシュボード統計のデータ元 |
| **Airtable** | リレーショナルDB、Interface Designer | プロジェクト管理、UI構造設計 |

### Advanced系アプリからの高度機能統合
| アプリ | 採用機能 | メタスタジオでの実装 |
|--------|----------|---------------------|
| **Cursor** | LLM統合IDE、コード生成 | Claude Code SDK統合、AI駆動開発 |
| **Termius** | SSH接続、モバイル操作 | px CLI、クロスプラットフォーム開発 |

### 開発ツール・インフラ統合
| ツール/フレームワーク | 採用機能 | メタスタジオでの実装 |
|-------------------|----------|---------------------|
| **Roo boomerang** | 高速開発、テンプレート管理 | プロジェクトテンプレート、迅速な初期化 |
| **Zellij** | ターミナルマルチプレクサ、セッション管理 | 王国ターミナル、複数プロジェクト並行管理 |
| **Git worktrees** | 複数ブランチ並行作業 | Projext構造での並行開発、バージョン管理 |
| **Tyk** | APIゲートウェイ、マイクロサービス管理 | エージェント間通信、API統合管理 |
| **Polar** | 決済システム、創作者収益化 | 創作物収益化、アプリ内課金システム |
| **Better T Stack** | TypeScript最適化スタック | 高速開発基盤、型安全性保証 |

### メタスタジオ独自進化点
- **統合性**: 6つのCoreアプリ + 2つのAdvancedアプリ + 6つの開発ツールの機能を1つに統合
- **AI駆動**: 全体がAIエージェント階層管理（神→王→将→兵 + 母）
- **Projext構造**: LLM可読な統一プロジェクト管理システム
- **音声特化**: モバイル音声入力でのアイデア収集に完全特化
- **Zellij王国**: 複数プロジェクトの並行管理とターミナル統合
- **収益化統合**: Polar決済システムによる創作物の自動収益化

## 核心理念
"ユーザーは何も考えず一つのインボックスにアイデアやメモを残し、それらが自動で振り分けられ、Projextフレームワークによって構造化され、実現まで導かれる"

## 開発方針
- **個人専用ツール**: まず開発者本人が満足するまで使い込む
- **AI駆動開発**: Claude Code SDK + Mastraフレームワーク + Projextシステムで大部分を自動生成、4週間以内完成
- **段階的拡張**: 満足度確認後に他ユーザー展開検討

## 🚀 神→ランチャーまでの基本フロー
```
神（ユーザー）：「瞑想アプリ作って」
↓ 音声入力・チャット（特にモバイル音声入力特化）
インボックス：アイデア自動蓄積
↓ 自動トリアージ
母（CTO）：必要な将・兵を生産・配置
王（COO）：エージェント軍隊を運用・監督
↓ バックグラウンド自動開発
projext自動生成：設計図・計画書完成
↓ 実装フェーズ
ソースコード生成：別フォルダで管理
↓ パッケージ化
ランチャー：完成アプリ配置・即利用可能
```

# 🏗️ 技術アーキテクチャ統合（確定版）

## AI・エージェントフレームワーク（統合設計）
### Claude Code SDK (メインエンジン)
- 神↔王対話システムのコア
- Projextタスク実行エンジン
- リアルタイム開発支援

### Mastraフレームワーク (オーケストレーション)
- マルチエージェント管理システム
- 将・兵エージェントの生成・配置・管理
- ワークフロー自動化

### Projextフレームワーク (プロジェクト構造化)
- SSoT（Single Source of Truth）としてのプロジェクト管理
- LLM可読な統一ファイル構造
- バージョン管理対応

## フロントエンド・基本技術
- **React 19**: フロントエンドフレームワーク
- **Next.js 15**: フルスタックフレームワーク + TurboPack
- **TypeScript 5**: 型システム
- **Rust**: バックエンド開発言語（Tauri用）

## データ・状態管理
- **supabase**: データベース
- **tRPC**: API通信（フル型安全）
- **TanStack Query**: データフェッチング・キャッシング
- **Drizzle**: ORM（軽量、高速）
- **Zustand**: 統一状態管理
- **Zod**: データバリデーション・スキーマ定義

## UI・開発ツール
- **Tailwind CSS v4 + DaisyUI**: スタイリング
- **Three.js**: 3D表現（ネオメタニューモフィズム）
- **Turborepo**: モノレポ管理
- **Bun**: ランタイム + パッケージマネージャー + テストランナー（統合）
- **Biome**: リンター・フォーマッター

## エッジ・デプロイ
- **Hono + Cloudflare**: エッジ関数・デプロイ

# 🎨 デザインシステム（ネオメタニューモフィズム）

## デザイン言語定義
**ネオメタニューモフィズム**：革新的デザイン言語
- **ニューモフィズム**: シンプル + 立体感（影・光演出）
- **スキューモフィズム**: リアル質感（木材・金属・ガラス）
- **メタ要素**: 未来的・テクノロジー感・ホログラム的表現

## 質感テンプレートシステム
- **ガラス系**: フロスト、クリア、カラードガラス、すりガラス
- **メタル系**: アルミニウム、チタン、真鍮、カッパー、プラチナ
- **オーガニック**: ダークウッド、マーブル、レザー、ファブリック
- **未来系**: ホログラム、ネオン、プラズマ、光学グラス

## メタスタジオアイコン
**😎🤖 サングラス + ビジネススーツロボット**
- エージェント・AI開発の象徴
- 未来的でありながら親しみやすいデザイン
- 質感テンプレートに応じて素材変化

# 🖥️ 統合IDEレイアウト設計

## 全体構成（VS Code風IDE + Projext統合）
```
┌─────────────┬──────────────────────────┬──────────────┐
│左:ディレクトリ │   メイン：WYSIWYG Editor    │右:母との会話  │
│📁 templates  │                         │👸 Mother    │
│📁 knowledge  │   # Projext統合編集画面    │💬 対話画面   │
│📁 backups    │   フリック操作対応          │📊 学習統計   │
│              │   ブロック D&D            │⚙️ 人材管理   │
├─────────────┼──────────────────────────┼──────────────┤
│左下:管理      │   下：Zellij王国ターミナル    │右:KPI・進捗  │
│🚀 ランチャー  │┌─王:瞑想─┬─王:投資─┬─王:iS─┐│📈 リアルタイム│
│⚙️ 設定       ││神と対話 │Projext │企画中 ││🎯 統計データ │
│🔧 プラグイン  ││将→兵管理│px run   │      ││             │
│📊 統計       │└────────┴────────┴──────┘│             │
└─────────────┴──────────────────────────┴──────────────┘
```

# 👑 完全階層エージェントシステム統合（王国構造）

## 階層定義
### 🔥 神（CEO）- グランドデザイナー
- **Role**: ユーザー（開発者本人）- 絶対的存在
- **Authority**: お告げ発令・最終承認
- **Interface**: 音声入力・チャット→インボックス

### 👑 王（COO）- オペレーション統括
- **Role**: 軍隊運用管理（神の直下）
- **Technology**: Claude Code SDK メインエンジン
- **Responsibility**: 
  - 神との対話型要件詰め（抽象↔具体反復）
  - Projext構造化（vision→requirements→design自動生成）
  - エージェント軍隊の運用・監督、プロジェクト進行管理
  - A2Aで他法人との連携

### 👸 母（CTO）- 技術・人材統括
- **Role**: 軍隊生産工場（神の直下・独立系統）
- **Technology**: Mastraフレームワーク（マルチエージェント管理）
- **Responsibility**: 
  - 将軍・兵の生成・育成・配属
  - 技術基盤整備
  - Projextテンプレート生成・最適化

### 🏛️ 将（GENERAL）- プロジェクト責任者
- **Role**: 各プロジェクト専属責任者（母が生産）
- **Technology**: Mastra Agent + Projextタスク管理

### ⚔️ 兵（SOLDIER）- 機能実装者
- **Role**: 機能別実装担当（母が生産・育成）
- **Technology**: 各種専門AI + Claude Code SDK

## 王国運営フロー
```
神: 「瞑想アプリ作って」（インボックスに投入）
↓ 自動トリアージ
母: 瞑想アプリ将 + 必要な兵を生産・配属
王: 生産された軍隊を運用・監督
↓ バックグラウンド自動開発
将・兵: projext生成 → 実装 → テスト
↓ 完成
ランチャー: 完成アプリ配置 → 神が確認・利用
```

# 📁 Projextファイル構造（確定版）

## メタスタジオ内でのProjext管理
```
~/Documents/MetaStudio/
├── meditation_app_projext/        # Projext設計図フォルダ
│   ├── projext.yml               # プロジェクトマニフェスト
│   ├── 1_vision/
│   │   ├── GOAL.md
│   │   └── STAKEHOLDERS.md
│   ├── 2_requirements/
│   │   ├── user_stories/
│   │   └── specifications/
│   ├── 3_design/
│   │   ├── architecture.mermaid
│   │   └── sequence/
│   ├── 4_agents/
│   │   ├── system_prompt.md
│   │   └── tasks/
│   └── 5_artifacts/
│       ├── docs/
│       └── tests/
├── meditation_app_src/            # 実際のソースコードフォルダ
├── templates/
│   └── projext_templates/
├── knowledge/
└── backups/
```

## 統合CLIコマンド px（メタスタジオ内蔵）
```bash
# Projext初期化
px init meditation_app

# AIとの対話開始（王との対話）
px chat "瞑想タイマーの要件を詰めたい"

# 母による将・兵生成
px agents generate

# タスク実行
px run T-001_create_timer_ui

# メタスタジオランチャーに配置
px deploy --launcher
```

# 📝 コア機能詳細

## WYSIWYGエディタ（Craft風 + Projext統合）
- **メイン方式**: 完全WYSIWYG - # ヘッダー → 即座変換
- **Projext統合**: projext内のファイル編集時の特別UI
- **操作方式**: フリック/スワイプベースの直感的UI
- **D&D操作**: ブロック単位での直感的操作

## 音声入力システム（カスタマイズ対応）
### デフォルト設定: 常時待機（スマートボイス検出）
- 自然な会話開始で自動録音開始
- 無音検出で自動録音終了
- **Projext統合**: 音声入力内容を自動でProjext構造に分類・配置

### カスタマイズオプション:
- **常時待機型**: 音声検出閾値調整、キーワード起動
- **ボタン型**: マイクボタン・ホットキー
- **ハイブリッド**: 環境に応じて自動切り替え

## 自動分類システム（インボックス→Projext）
```
音声/チャット入力 → テキスト化
↓
プレフィックス判定 → 「タスク：〜」「アイデア：〜」で直接分類
↓
LLM解析 → 自動カテゴリ判定
↓
Projext構造化 → 適切なprojextセクションに自動配置
↓
確認フロー → 学習型/手動モード選択可能
```

## 母との対話UI（ハイブリッドチャット + Projext統合）
- **基本形式**: チャット形式（LINEライクな吹き出し）
- **入力方式**: テキスト入力・音声入力対応
- **Projext統合**: ファイル参照・修正提案のインタラクティブメッセージ
- **検索可能**: 会話ログ

# 🎯 汎用テンプレート対応

## 創造物カテゴリ（拡張版）
- **デジタルコンテンツ**: 文章、画像、音声、動画、漫画、小説
- **インタラクティブ**: ゲーム、アプリケーション、Webサイト
- **エンターテイメント**: iS・AIチューバー・バーチャル配信者
- **マーケティング**: LP、マーケティングファネル、広告クリエイティブ
- **ビジネス**: プレゼン資料、提案書、レポート
- **教育**: eラーニング、チュートリアル、解説動画
- **ソーシャル**: SNSアカウント運用、ポッドキャスト
- **ツール**: ADHD向けアプリシリーズ、生産性ツール
- **次世代**: メタバース空間、NFTコレクション
- **ワークフロー**: 自動化プロセス、ビジネスロジック

## iS（アイエス）テンプレート構造
```yaml
# app_name_projext/projext.yml（iS専用）
name: "みけちゃん_is_streamer"
type: "is_streamer"
character:
  name: "みけちゃん"
  personality: "ギャル系、明るい、猫好き"
  speech_pattern: "ギャル口調、語尾に「にゃん」"
content:
  themes: ["ゲーム実況", "料理", "日常雑談"]
  interaction_style: "視聴者との距離感近め"
```
**独自ブランド名**: iS（アイエス）- AI+Imagination Streamer略

## リッチ化昇華戦略（半自動・段階的）
```
文字生成: 小説・シナリオ・記事
↓
画像生成: イラスト・デザイン
↓
音楽生成: BGM・効果音
↓
動画生成: アニメーション・映像
```

# 🎯 クロスプラットフォーム戦略

## デスクトップ（Tauri）: フル機能開発環境
- WYSIWYG エディタ + 完全階層管理
- Projext統合管理・px CLI統合
- アプリランチャー・詳細ダッシュボード

## モバイル（Expo）: アイデア入力特化
- **音声入力メイン**: インボックスへのアイデア・思考入力に完全特化
- **簡単編集**: 基本的なテキスト修正のみ
- **プレビュー重視**: 入力内容をサッと確認
- **Projext同期**: デスクトップとのリアルタイム同期

## 連携フロー（Projext統合）
```
外出中: モバイル音声入力 → Projext自動分類・保存
↓
帰宅後: デスクトップで詳細調整・本格開発
↓  
移動中: モバイルで進捗確認・軽微な修正
```

# 🚀 デプロイ・配布戦略

## 個人使用: メタスタジオ内蔵型
- ランチャーから各アプリ起動
- エコシステム内で完結・データ共有
- projextによるプロジェクト管理

## 配布用: 完全独立パッケージ
- Tauriで完全パッケージ化
- メタスタジオ不要で単体動作
- projextで生成された成果物のみ抽出

# 🔒 セキュリティ・バックアップ戦略

## エクスポート機能強化
### 完全エクスポートシステム：
- 全projextディレクトリ（SSoT完全保存）
- Wiki知識ベース・エージェント学習データ
- 作成済みアプリ（実行ファイル + ソースコード）

### 段階的バックアップ：
- **リアルタイム**: 編集中の自動保存 + projext同期
- **日次**: 差分バックアップ + Git commit
- **週次**: 完全エクスポート + クラウド同期

# 🧠 学習・進化システム

## 母（CTO）の進化システム
### 過去アプリ開発からの学習：
- 成功したチーム編成パターン（projext/agents/分析）
- プロジェクト難易度別の最適人材配置
- 失敗から学んだ改善点（projext履歴分析）
- Projextテンプレートの自動生成・最適化

## NotebookLM機能（Projext統合）
### 入力データ質問システム:
- 全プロジェクトデータを学習（projext含む）
- 自然言語での質問・検索
- クロスリファレンス分析（プロジェクト間のprojext比較）
- projext構造最適化提案

# ⚠️ エラーハンドリング・問題解決

## 段階的エスカレーション
```
兵：「実装エラー・課題報告」（projext/tasks/実行失敗）
↓
将：別アプローチ提案・他兵への振り分け
↓（解決不可能）
王：技術的解決策指示・追加リソース投入・projext全体見直し
↓（根本的問題）
神：問題分析→改革指示（母に新オートメーション開発指示）
```

# 🚀 開発フェーズ戦略（4週間完成）

## 確定優先順位
**Week 1**: 神→インボックス→自動トリアージの基本フロー
**Week 2**: projext自動生成システム
**Week 3**: ランチャー＋プラグイン管理システム
**Week 4**: 1つの具体アプリ完全生成（瞑想アプリなど）

## 外部ツール連携優先度
1. メタスタジオ単体完成 (最優先)
2. ComfyUI連携 (画像・動画生成)
3. n8n連携 (ワークフロー自動化)

# 🎯 成功指標（KPI）

## ユーザー体験
- **実現率**: アイデア → 完成品の成功率
- **時間短縮**: 従来開発時間との比較
- **Projext活用率**: projext構造の自動生成・活用度

## 技術指標
- **応答速度**: エディタ操作レスポンス
- **正確性**: 自動分類・実装品質
- **AI統合**: Claude Code SDK + Mastra + Projext連携効率

## ビジネス指標
- **創造物完成率**: プロジェクト成功率
- **学習効率**: 母の進化速度
- **Projext普及**: フレームワーク利用拡大

# 📋 重要な技術的決定事項（最終確定）

## AI統合アーキテクチャ
Claude Code SDK（対話）+ Mastra（エージェント管理）+ Projext（構造化）

## 技術スタック統合
- **ランタイム**: Bun（統合）
- **パッケージマネージャー**: Bun
- **テストランナー**: Bun
- **モノレポ管理**: Turborepo
- **フロントエンド**: Next.js 15 + Expo + Tauri
- **状態管理**: Zustand
- **API**: tRPC
- **ORM**: Drizzle
- **デプロイ**: Cloudflare

## 階層明確化
母・王は神の直下独立系統、役割完全分離

## projext管理
設計図フォルダ（`_projext/`）と実ソースフォルダ（`_src/`）は別管理

## 開発優先順位
インボックス→projext→ランチャー→具体アプリ

## 4週間開発
AI駆動で超高速実装

# 📝 次セッションでの開発開始事項

1. **Claude Code起動**: この統合要件定義書を元にメタスタジオ開発開始
2. **初期構築**: Bun + Turborepo + Next.js + Expo + Tauri基盤構築
3. **神→インボックス→トリアージ**: 基本フロー実装
4. **AI統合**: Claude Code SDK + Mastra + Projext統合アーキテクチャ
5. **projext基盤**: ファイル構造自動生成システム

---

**重要**: この統合要件定義は開発過程で継続的に進化させ、実際の使用体験に基づいて最適化していく。まずは4週間で実用版を完成させ、段階的にビジョンを実現する。