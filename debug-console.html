<!DOCTYPE html>
<html>
<head>
    <title>VRM Debug Console</title>
    <style>
        body { font-family: monospace; padding: 20px; }
        #logs { 
            border: 1px solid #ccc; 
            height: 400px; 
            overflow-y: auto; 
            padding: 10px; 
            background: #f9f9f9;
            white-space: pre-wrap;
        }
        button { 
            padding: 10px 20px; 
            margin: 10px 0; 
            font-size: 16px; 
        }
    </style>
</head>
<body>
    <h1>VRM Debug Console</h1>
    <button onclick="startMonitoring()">Start Monitoring localhost:8080</button>
    <button onclick="clearLogs()">Clear Logs</button>
    <div id="logs"></div>

    <script>
        let logsDiv = document.getElementById('logs');
        let originalConsole = {};
        let isMonitoring = false;

        function addLog(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;
            logsDiv.textContent += logMessage;
            logsDiv.scrollTop = logsDiv.scrollHeight;
            
            // VRM関連のログのみ表示
            if (message.includes('📋') || message.includes('🔧') || message.includes('✅') || 
                message.includes('❌') || message.includes('🎯') || message.includes('📦') || 
                message.includes('⏱️') || message.includes('VRM') || message.includes('ArrayBuffer')) {
                console.log(`[VRM LOG] ${message}`);
            }
        }

        function startMonitoring() {
            if (isMonitoring) return;
            isMonitoring = true;
            
            // 新しいウィンドウでlocalhost:8080を開く
            const targetWindow = window.open('http://localhost:8080', '_blank');
            
            addLog('🔍 Monitoring started for localhost:8080');
            addLog('📖 Open the target window and upload a VRM file');
            addLog('🎯 Looking for VRM-related console logs...');
            
            // ポーリングで対象ウィンドウのコンソールを監視
            const interval = setInterval(() => {
                if (targetWindow.closed) {
                    clearInterval(interval);
                    isMonitoring = false;
                    addLog('🔚 Monitoring stopped - target window closed');
                    return;
                }
                
                try {
                    // 対象ウィンドウのコンソールメソッドをフック
                    if (targetWindow.console && !targetWindow.console._hooked) {
                        ['log', 'warn', 'error', 'info'].forEach(method => {
                            const original = targetWindow.console[method];
                            targetWindow.console[method] = function(...args) {
                                const message = args.map(arg => 
                                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                                ).join(' ');
                                
                                addLog(message, method);
                                return original.apply(this, args);
                            };
                        });
                        targetWindow.console._hooked = true;
                        addLog('✅ Console hooks installed');
                    }
                } catch (e) {
                    // Cross-origin制限の場合
                    addLog(`⚠️ Cross-origin restriction: ${e.message}`);
                }
            }, 1000);
        }

        function clearLogs() {
            logsDiv.textContent = '';
        }

        // このページでもコンソールログを監視
        ['log', 'warn', 'error', 'info'].forEach(method => {
            originalConsole[method] = console[method];
            console[method] = function(...args) {
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' ');
                
                if (message.includes('📋') || message.includes('🔧') || message.includes('✅') || 
                    message.includes('❌') || message.includes('🎯') || message.includes('📦') || 
                    message.includes('⏱️') || message.includes('VRM') || message.includes('ArrayBuffer')) {
                    addLog(message, method);
                }
                
                return originalConsole[method].apply(this, args);
            };
        });

        addLog('🚀 VRM Debug Console Ready');
        addLog('📝 This will capture VRM-related console logs from localhost:8080');
    </script>
</body>
</html>