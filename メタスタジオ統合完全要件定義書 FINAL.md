# メタスタジオ（脳内現実化ツール）統合完全要件定義書 v5.0 FINAL

📋 プロジェクト概要
正式名称: メタスタジオ（脳内現実化ツール進化版）+ Projext統合開発フレームワーク
コンセプト: アイデアから実装まで、あらゆる創造的願望を自動実現するオールラウンド開発プラットフォーム + LLM駆動プロジェクト管理システム


## ロールモデル機能統合
| アプリ | 採用機能 | メタスタジオでの実装 |
|--------|----------|---------------------|
| **Obsidian** | 拡張性、MD管理、プラグインシステム | 知識ベース管理、プラグインエコシステム |
| **Craft** | WYSIWYG操作性、D&D、シンプルUI | メインエディタの操作性、ブロック管理 |
| **Lark** | ダッシュボード俯瞰、データベースビュー | 統合ダッシュボード、プロジェクト俯瞰 |
| **Notion** | ブロックエディタ、データベース連携 | Projext構造管理、エンティティ管理 |
| **Boost.space** | Make.com統合、MCP-AI連携、Appflows | エージェント連携、外部ツール統合、ワークフロー自動化 |
| **xTiles** |  一覧としてみられる俯瞰ダッシュボード |
| **Airtable（looker studio）** | ダッシュボード統計のデータ元 |
| **Airtable** | リレーショナルDB、Interface Designer | プロジェクト管理、UI構造設計 |
| **Cursor** | LLM統合IDE、コード生成 | Claude Code SDK統合、AI駆動開発 |
| **blink shell** | SSH接続、モバイル操作 | px CLI、クロスプラットフォーム開発 |

### 開発ツール・インフラ統合
| ツール/フレームワーク | 採用機能 | メタスタジオでの実装 |
|-------------------|----------|---------------------|
| **Roo boomerang** | 高速開発、テンプレート管理 | プロジェクトテンプレート、迅速な初期化 |
| **Zellij** | ターミナルマルチプレクサ、セッション管理 | 組織型ターミナル、複数プロジェクト並行管理 |
| **Git worktrees** | 複数ブランチ並行作業 | Projext構造での並行開発、バージョン管理 |
| **Tyk** | APIゲートウェイ、マイクロサービス管理 | エージェント間通信、API統合管理 |
| **Polar** | 決済システム、創作者収益化 | 創作物収益化、アプリ内課金システム |
| **Better T Stack** | TypeScript最適化スタック | 高速開発基盤、型安全性保証 |
| **Pueue** | バックグラウンドタスク管理、キュー処理 | 長時間タスク管理、並行処理システム |
| **Helix** | モダンエディタ、Vim風操作 | 高速ファイル編集、キーボード効率化 |

### メタスタジオ独自進化点
- **AI駆動**: 全体がAIエージェント階層管理（神→王→将→兵 + 母）
- **Projext構造**: LLM可読な統一プロジェクト管理システム
- **音声特化**: モバイル音声入力でのアイデア収集に完全特化
- **組織ターミナル**: 複数プロジェクトの並行管理とターミナル統合
- **収益化統合**: Polar決済システムによる創作物の自動収益化

## 核心理念
"ユーザーは何も考えず一つのインボックスにアイデアやメモを残し、それらが自動で振り分けられ、Projextフレームワークによって構造化され、実現まで導かれる"

## 開発方針
- **個人専用ツール**: まず開発者本人が満足するまで使い込む
- **AI駆動開発**: Claude Code SDK + Mastraフレームワーク + Projextシステムで大部分を自動生成、4週間以内完成
- **段階的拡張**: 満足度確認後に他ユーザー展開検討

## 🚀 神→ランチャーまでの基本フロー
```
神（ユーザー）：「瞑想アプリ作って」
↓ 音声入力・チャット（特にモバイル音声入力特化）
インボックス：アイデア自動蓄積
↓ 自動トリアージ
母（CTO）：必要な将・兵を生産・配置
王（COO）：エージェント軍隊を運用・監督
↓ バックグラウンド自動開発
projext自動生成：設計図・計画書完成
↓ 実装フェーズ
ソースコード生成：別フォルダで管理
↓ パッケージ化
ランチャー：完成アプリ配置・即利用可能
```



# 🚀 2025年最新モダン開発環境スタック 技術アーキテクチャ統合（確定版）

# 📋 メタスタジオ開発フェーズ・優先度

## 🔥 コア機能（高優先度 - Phase 1）
- 🎯 **インボックスシステム**: アイデア自動トリアージ機能
- 👑 **エージェント階層管理**: 神→王/母→将→兵の自動生成システム
- 🏛️ **Projext構造管理**: vision→requirements→design自動生成
- 🚀 **完成アプリランチャー**: iOS風アプリアイコン表示

## ⚡ 統合機能（中優先度 - Phase 2）
- 🎨 **ネオメタニューモフィズムデザイン**: 質感セット選択スキンシステム
- 🌐 **ブラウザ自動化統合**: Playwright + iframe埋め込み→Tauri WebView
- 🖥️ **ネイティブターミナル**: Ghostty GPU加速（デスクトップ版）
- 🎤 **音声特化インボックス**: モバイル音声入力完全対応
- 💰 **収益化統合**: Polar決済システム連携
- 🔄 **YOYO実験管理**: AI失敗時の安全なロールバック機能

## 🤖 AI駆動機能（低優先度 - Phase 3）
- 🔥 **神→王→母対話システム**: 要件詰めの自動化
- 🤝 **A2A連携**: 他法人エージェントとの連携
- 📊 **KPI・学習統計**: リアルタイム進捗管理

## AI統合アーキテクチャ
Claude Code SDK（対話・並列実行）+ Projext（構造化・中央管理）

- **ランタイム**: Bun（統合）
- **パッケージマネージャー**: Bun
- **テストランナー**: Bun
- **モノレポ管理**: Turborepo
- **フロントエンド**: Next.js 15 + Expo + Tauri
- **状態管理**: Zustand
- **API**: tRPC
- **ORM**: Drizzle
- **デプロイ**: Cloudflare
- **並列開発**: Git worktrees + GitHub Actions
- **実験管理**: YOYO AI Version Control（AI失敗隔離）
- **ブラウザ統合**: iframe埋め込み → Tauri WebView（段階発展）

### 開発環境構成（詳細アーキテクチャ）
```yaml
# Phase 1: Web中心開発（MVP - 4週間）
webview_integration:
  terminal: "xterm.js (Claude Code SDK統合、react-console-emulator移行)"
  browser: "iframe sandbox (ブラウザ自動化UI、セキュア制限)"
  automation: "GitHub Actions (バックグラウンド並列実行)"
  containers: "OrbStack (macOS最適化、Docker Desktop代替)"
  version_control:
    production: "Git + worktrees (正式リリース)"
    experimental: "YOYO (AI実験・失敗隔離)"

# Phase 2: デスクトップ拡張（本格運用）
native_integration:
  terminal: "Ghostty (GPU加速、Zig製、Terminal Inspector)"
  browser: "Tauri WebView2/WebKit (ネイティブ埋め込み)"
  automation: "Playwright (CDP制御、Browser Use Framework)"
  shell: "Fish + Starship (AI統合最適化)"
  containers: "OrbStack → Apple Container (macOS 26+段階移行)"
  live_view: "操作可視化・セッション録画・リプレイ機能"

# Phase 3: エンタープライズ・上級者
advanced_features:
  multiplexer: "Zellij (複雑な並列開発、オプション機能)"
  remote_browser: "Browserbase類似 (クラウドブラウザ統合)"
  security: "サンドボックス強化、エンタープライズ認証"
```

### モバイル・接続環境
```yaml
ローカル実行: blink-Shell (Python/JS/Shortcuts統合)
SSH接続: ShellFish (Files.app統合)
VPN: Tailscale (ゼロコンフィグ全デバイス接続)
```

### アプリケーション基盤
```yaml
フロントエンド: Next.js 15 + React 19 + TypeScript 5
クロスプラットフォーム: Tauri + Expo
ランタイム: Bun (統合型)
モノレポ: Turborepo
AI統合: Claude Code SDK + Mastra + Projext
```

### データ・認証
```yaml
メイン: Convex (DB+認証+リアルタイム統合)
サブ候補: Supabase Auth / Better Auth
状態管理: Zustand + tRPC + Zod
```

## 📅 開発フェーズ戦略
**Phase 1（即時 - 4週間）**: コア機能実装
**Phase 2（2025年夏）**: 統合機能 + Apple Container準備
**Phase 3（2025年秋）**: AI駆動機能 + 完全次世代化

## 🛠️ 開発環境セットアップ
```fish
# デスクトップ環境（Homebrewが最適・macOS標準）
brew install --cask ghostty orbstack tailscale
brew install fish starship zellij

# 日本語対応設定
mkdir -p ~/.config/ghostty
echo 'font-family-japanese = "Hiragino Sans"' >> ~/.config/ghostty/config
echo 'ime-mode = true' >> ~/.config/ghostty/config
echo 'starship init fish | source' >> ~/.config/fish/config.fish
```

## ⚠️ 重要な制約・注意点
- **Zellij**: セッション名は英語必須（技術的制約）
- **Ghostty**: 日本語フォント設定必須
- **モバイル**: blink Shell + ShellFish$14.99が最適構成

### 🤖 AI・エージェント基盤
- **Claude Code SDK**: 神↔王対話システムのコア（一本化・並列実行・メモリ永続化）
- **Projext**: 中央管理型要件定義システム（AI・人間の共同作業基盤・SSoT）
- **Git worktrees**: 複数ブランチ並行開発・プロトタイプ迅速生成
- **YOYO**: AI実験的バージョン管理（失敗隔離・即座ロールバック・クロスIDE）

### 💻 開発環境・ランタイム
- **Bun**: ランタイム + パッケージマネージャー + テストランナー（統合）
- **Turborepo**: モノレポ管理
- **Biome**: リンター・フォーマッター

### 🎨 フロントエンド・UI
- **Next.js 15 + React 19 + TypeScript 5**: フロントエンド
- **Tailwind CSS v4 + DaisyUI　v５**: スタイリング
- **Three.js**: 3D表現（ネオメタニューモフィズム）

### 📱 クロスプラットフォーム（基本は今作ってるWEB版ということ？）
- **Tauri**: デスクトップアプリ化
- **Expo**: モバイルアプリ化

### 🗄️ データ・認証・状態管理
- **Convex**: メインDB + 認証 + リアルタイム
- **Supabase/Better Auth**: サブ候補
- **tRPC**: API通信（フル型安全）
- **Zustand**: 統一状態管理
- **Zod**: データバリデーション・スキーマ定義
- **Drizzle**: ORM（軽量、高速）

### ☁️ デプロイ・インフラ
- **Cloudflare + Hono**: エッジ関数・デプロイ

### 🖥️ 開発環境（フェーズ別構成）
**Phase 1（Web開発中心）**:
- **Webターミナル**: xterm.js（Claude Code SDK統合、GPU加速）
- **ブラウザ埋め込み**: iframe sandbox（ブラウザ自動化UI）
- **並列実行**: GitHub Actions + Claude Code バックグラウンド
- **コンテナ**: OrbStack（macOS高速・軽量環境）
- **実験バージョン管理**: YOYO（AI失敗時即座ロールバック）

**Phase 2（デスクトップ拡張）**:
- **ネイティブターミナル**: Ghostty（GPU加速、Zig製）
- **ブラウザ統合**: Tauri WebView2/WebKit（ネイティブ埋め込み）
- **シェル環境**: Fish + Starship（AI統合最適化）
- **コンテナ進化**: OrbStack → Apple Container（段階移行）
- **ブラウザ自動化**: Playwright統合（CDP経由制御）

**Phase 3（上級者・企業向け）**:
- **高度セッション**: Zellij（複雑な並列開発時のみ）
- **Live View**: ブラウザ操作の可視化・共有機能
- **エンタープライズ**: リモートブラウザ・セキュアサンドボックス

### 📱 モバイル開発環境
- **blinkchell**: ローカル実行（Python/JS/Shortcuts統合）
- **ShellFish**: SSH接続（Files.app統合）
- **Tailscale**: VPN（ゼロコンフィグ全デバイス接続）

# 🎨 デザインシステム（ネオメタニューモフィズム）

## デザイン言語定義
**ネオメタニューモフィズム**：革新的デザイン言語
- **ニューモフィズム**: シンプル + 立体感（影・光演出）
- **スキューモフィズム**: リアル質感（木材・金属・ガラス）
- **メタ要素**: 未来的・テクノロジー感・ホログラム的表現

## メタスタジオアイコン
**😎🤖 サングラス + ビジネススーツロボット**
- エージェント・AI開発の象徴
- 未来的でありながら親しみやすいデザイン
- 質感テンプレートに応じて素材変化

## 質感テンプレートシステム
- **ガラス系**: フロスト、クリア、カラードガラス、すりガラス
- **メタル系**: アルミニウム、チタン、真鍮、カッパー、プラチナ
- **オーガニック**: ダークウッド、マーブル、レザー、ファブリック
- **未来系**: ホログラム、ネオン、プラズマ、光学グラス


# 🖥️ 統合IDEレイアウト設計

## 全体構成（VS Code風IDE + Projext統合）
```
┌─────────────┬──────────────────────────┬──────────────┐
│左:ディレクトリ │   メイン：WYSIWYG Editor    │右:母との会話  │
│📁 templates  │                         │👸 Mother    │
│📁 knowledge  │   # Projext統合編集画面    │💬 対話画面   │
│📁 backups    │   フリック操作対応          │📊 学習統計   │
│              │   ブロック D&D            │⚙️ 人材管理   │
├─────────────┼──────────────────────────┼──────────────┤
│左下:管理      │   下：組織型ターミナル（Zellij）│右:KPI・進捗  │
│🚀 ランチャー  │┌─王:瞑想─┬─王:投資─┬─王:iS─┐│📈 リアルタイム│
│⚙️ 設定       ││神と対話 │Projext │企画中 ││🎯 統計データ │
│🔧 プラグイン  ││将→兵管理│px run   │      ││             │
│📊 統計       │└────────┴────────┴──────┘│             │
└─────────────┴──────────────────────────┴──────────────┘
```

# 👑 完全階層エージェントシステム統合（王国構造）

## 階層定義
### 🔥 神（CEO）- グランドデザイナー
- **Role**: ユーザー（開発者本人）- 絶対的存在
- **Authority**: お告げ発令・最終承認
- **Interface**: 音声入力・チャット→インボックス

### 👑 王（COO）- オペレーション統括
- **Role**: 軍隊運用管理（神の直下）
- **Technology**: Claude Code SDK メインエンジン
- **Responsibility**: 
  - 神との対話型要件詰め（抽象↔具体反復）
  - Projext構造化（vision→requirements→design自動生成）
  - エージェント軍隊の運用・監督、プロジェクト進行管理
  - A2Aで他法人との連携

### 👸 母（CTO）- 技術・人材統括
- **Role**: 軍隊生産工場（神の直下・独立系統）
- **Technology**: Mastraフレームワーク（マルチエージェント管理）
- **Responsibility**: 
  - 将軍・兵の生成・育成・配属
  - 技術基盤整備
  - Projextテンプレート生成・最適化

### 🏛️ 将（GENERAL）- プロジェクト責任者
- **Role**: 各プロジェクト専属責任者（母が生産）
- **Technology**: Mastra Agent + Projextタスク管理

### ⚔️ 兵（SOLDIER）- 機能実装者
- **Role**: 機能別実装担当（母が生産・育成）
- **Technology**: 各種専門AI + Claude Code SDK

## 王国運営フロー
```
神: 「瞑想アプリ作って」（インボックスに投入）
↓ 自動トリアージ
母: 瞑想アプリ将 + 必要な兵を生産・配属
王: 生産された軍隊を運用・監督
↓ バックグラウンド自動開発
将・兵: projext生成 → 実装 → テスト
↓ 完成
ランチャー: 完成アプリ配置 → 神が確認・利用
```

# 📁 Projext（プロジェクスト）システム定義

## Projextとは
**成果物を作るためにユーザーニーズから対話で構築・構造化された要件定義のファイル群**。これを中心に据え、AIも人もそこにアクセスして、リアルタイムに更新しながら成果物を作っていく仕組み。

### 核心概念
- **SSoT (Single Source of Truth)**: プロジェクトの唯一の真実の源泉
- **AI-人間協調**: 両者が同じ構造を理解・編集可能
- **リアルタイム同期**: 変更が即座に全エージェントに反映
- **LLM最適化**: AIが理解しやすい階層構造

## メタスタジオ内でのProjext管理
```
~/Documents/MetaStudio/
├── meditation_app_projext/        # Projext設計図フォルダ（要件定義群）
│   ├── projext.yml               # プロジェクトマニフェスト
│   ├── 1_vision/                 # ビジョン・目標
│   │   ├── GOAL.md               # 何を作るか
│   │   └── STAKEHOLDERS.md       # 誰のために
│   ├── 2_requirements/           # 要件定義
│   │   ├── user_stories/         # ユーザーストーリー
│   │   └── specifications/       # 機能仕様
│   ├── 3_design/                 # 設計
│   │   ├── architecture.mermaid  # アーキテクチャ図
│   │   └── sequence/             # シーケンス図
│   ├── 4_agents/                 # エージェント設定
│   │   ├── system_prompt.md      # 各エージェントへの指示
│   │   └── tasks/                # タスク分解
│   └── 5_artifacts/              # 成果物
│       ├── docs/                 # ドキュメント
│       └── tests/                # テスト仕様
├── meditation_app_src/            # 実際のソースコード（成果物）
├── templates/
│   └── projext_templates/
├── knowledge/
└── backups/
```

## 統合CLIコマンド px（メタスタジオ内蔵）
```fish
# Projext初期化
px init meditation_app

# AIとの対話開始（王との対話）
px chat "瞑想タイマーの要件を詰めたい"

# 母による将・兵生成
px agents generate

# タスク実行
px run T-001_create_timer_ui

# メタスタジオランチャーに配置
px deploy --launcher
```

# 📝 コア機能詳細

## WYSIWYGエディタ（Craft風 + Projext統合）
- **メイン方式**: 完全WYSIWYG - # ヘッダー → 即座変換
- **Projext統合**: projext内のファイル編集時の特別UI
- **操作方式**: フリック/スワイプベースの直感的UI
- **D&D操作**: ブロック単位での直感的操作

## 音声入力システム（カスタマイズ対応）
### デフォルト設定: 常時待機（スマートボイス検出）
- 自然な会話開始で自動録音開始
- 無音検出で自動録音終了
- **Projext統合**: 音声入力内容を自動でProjext構造に分類・配置

### カスタマイズオプション:
- **常時待機型**: 音声検出閾値調整、キーワード起動
- **ボタン型**: マイクボタン・ホットキー
- **ハイブリッド**: 環境に応じて自動切り替え

## 自動分類システム（インボックス→Projext）
```
音声/チャット入力 → テキスト化
↓
プレフィックス判定 → 「タスク：〜」「アイデア：〜」で直接分類
↓
LLM解析 → 自動カテゴリ判定
↓
Projext構造化 → 適切なprojextセクションに自動配置
↓
確認フロー → 学習型/手動モード選択可能
```

## 母との対話UI（ハイブリッドチャット + Projext統合）
- **基本形式**: チャット形式（LINEライクな吹き出し）
- **入力方式**: テキスト入力・音声入力対応
- **Projext統合**: ファイル参照・修正提案のインタラクティブメッセージ
- **検索可能**: 会話ログ

# 🎯 汎用テンプレート対応

## 創造物カテゴリ（拡張版）
- **デジタルコンテンツ**: 文章、画像、音声、動画、漫画、小説
- **インタラクティブ**: ゲーム、アプリケーション、Webサイト
- **エンターテイメント**: iS・AIチューバー・バーチャル配信者
- **マーケティング**: LP、マーケティングファネル、広告クリエイティブ
- **ビジネス**: プレゼン資料、提案書、レポート
- **教育**: eラーニング、チュートリアル、解説動画
- **ソーシャル**: SNSアカウント運用、ポッドキャスト
- **ツール**: ADHD向けアプリシリーズ、生産性ツール
- **次世代**: メタバース空間、NFTコレクション
- **ワークフロー**: 自動化プロセス、ビジネスロジック

## iS（アイエス）テンプレート構造
```yaml
# app_name_projext/projext.yml（iS専用）
name: "みけちゃん_is_streamer"
type: "is_streamer"
character:
  name: "みけちゃん"
  personality: "ギャル系、明るい、猫好き"
  speech_pattern: "ギャル口調、語尾に「にゃん」"
content:
  themes: ["ゲーム実況", "料理", "日常雑談"]
  interaction_style: "視聴者との距離感近め"
```
**独自ブランド名**: iS（アイエス）- AI+Imagination Streamer略

## リッチ化昇華戦略（半自動・段階的）
```
文字生成: 小説・シナリオ・記事
↓
画像生成: イラスト・デザイン
↓
音楽生成: BGM・効果音
↓
動画生成: アニメーション・映像
```

# 🎯 クロスプラットフォーム戦略

## デスクトップ（Tauri）: フル機能開発環境
- WYSIWYG エディタ + 完全階層管理
- Projext統合管理・px CLI統合
- アプリランチャー・詳細ダッシュボード

## モバイル（Expo）: アイデア入力特化
- **音声入力メイン**: インボックスへのアイデア・思考入力に完全特化
- **簡単編集**: 基本的なテキスト修正のみ
- **プレビュー重視**: 入力内容をサッと確認
- **Projext同期**: デスクトップとのリアルタイム同期

## 連携フロー（Projext統合）
```
外出中: モバイル音声入力 → Projext自動分類・保存
↓
帰宅後: デスクトップで詳細調整・本格開発
↓  
移動中: モバイルで進捗確認・軽微な修正
```

# 🚀 デプロイ・配布戦略

## 個人使用: メタスタジオ内蔵型
- ランチャーから各アプリ起動
- エコシステム内で完結・データ共有
- projextによるプロジェクト管理

## 配布用: 完全独立パッケージ
- Tauriで完全パッケージ化
- メタスタジオ不要で単体動作
- projextで生成された成果物のみ抽出

# 🔒 セキュリティ・バックアップ戦略

## エクスポート機能強化
### 完全エクスポートシステム：
- 全projextディレクトリ（SSoT完全保存）
- Wiki知識ベース・エージェント学習データ
- 作成済みアプリ（実行ファイル + ソースコード）

### 段階的バックアップ：
- **リアルタイム**: 編集中の自動保存 + projext同期
- **日次**: 差分バックアップ + Git commit
- **週次**: 完全エクスポート + クラウド同期

# 🧠 学習・進化システム

## 母（CTO）の進化システム
### 過去アプリ開発からの学習：
- 成功したチーム編成パターン（projext/agents/分析）
- プロジェクト難易度別の最適人材配置
- 失敗から学んだ改善点（projext履歴分析）
- Projextテンプレートの自動生成・最適化

## NotebookLM機能（Projext統合）
### 入力データ質問システム:
- 全プロジェクトデータを学習（projext含む）
- 自然言語での質問・検索
- クロスリファレンス分析（プロジェクト間のprojext比較）
- projext構造最適化提案

# ⚠️ エラーハンドリング・問題解決

## 段階的エスカレーション
```
兵：「実装エラー・課題報告」（projext/tasks/実行失敗）
↓
将：別アプローチ提案・他兵への振り分け
↓（解決不可能）
王：技術的解決策指示・追加リソース投入・projext全体見直し
↓（根本的問題）
神：問題分析→改革指示（母に新オートメーション開発指示）
```

# 🌐 ブラウザ自動化・統合システム

## ブラウザ埋め込み技術（段階発展）

### Phase 1: iframe Sandbox（Web版）
```typescript
// セキュアなブラウザ埋め込み
<iframe
  src="/browser-automation"
  className="w-full h-full border-none"
  sandbox="allow-scripts allow-same-origin allow-forms"
  allow="clipboard-write; fullscreen"
  onMessage={handleBrowserEvent}
/>
```

### Phase 2: Tauri WebView（デスクトップ版）
```typescript
// ネイティブブラウザ統合
import { WebviewWindow } from '@tauri-apps/api/window'

const browserWindow = new WebviewWindow('browser-automation', {
  url: '/internal-browser',
  width: 1200,
  height: 800,
  resizable: true,
  decorations: false
})
```

### Phase 3: Playwright統合（自動化エンジン）
```typescript
// バックエンドでのブラウザ制御
import { Browser, chromium } from 'playwright'

const automationEngine = {
  browser: await chromium.launch({ headless: false }),
  cdpSession: "Chrome DevTools Protocol経由制御",
  liveView: "操作の可視化・セッション録画"
}
```

## 技術比較・選定根拠

| 技術 | 用途 | 長所 | 短所 |
|------|------|------|------|
| **iframe sandbox** | Web版UI | 簡単統合、セキュア | 機能制限 |
| **Tauri WebView** | デスクトップ版 | ネイティブ性能 | 設定複雑 |
| **Playwright** | 自動化エンジン | フル機能、CDP | 重い処理 |

# 🔄 YOYO AI実験バージョン管理統合

## YOYO活用戦略（AI開発特化）

### 核心概念
- **AI失敗隔離**: 本番Gitを汚染せず実験履歴を保持
- **即座ロールバック**: AI操作で問題発生時の瞬時復元
- **クロスIDE対応**: Cursor、Windsurf、VSCode間での一貫した実験管理

### メタスタジオ統合設計
```typescript
// YOYO + Projext連携
interface YOYOProjectIntegration {
  // 実験モード（AI開発時）
  experimental: {
    storage: "YOYO"
    trigger: "ai-agent-spawn" // エージェント起動時に自動スナップショット
    rollback: "on-error"      // エラー時即座復元
  }
  
  // 本番モード（正式リリース）
  production: {
    storage: "Git + worktrees"
    merge: "experimental → main" // 成功した実験のみ統合
    ci_cd: "GitHub Actions"
  }
}
```

### 使用フロー
```yaml
# AI実験開始
yoyo_snapshot: "エージェント実験開始"
  ↓
claude_sdk_spawn: "瞑想アプリ機能追加"
  ↓ (AI失敗)
yoyo_rollback: "安全に実験前状態に復元"
  ↓
user_notification: "AI実験失敗、安全に復元完了"
```

### Projext連携メリット
- **要件定義保護**: Projextファイル群の実験的変更を安全に管理
- **履歴可視化**: どの要件変更でAIが失敗したかの追跡
- **学習データ**: AI失敗パターンの蓄積・改善材料

# 🚀 開発フェーズ戦略（4週間完成）

## 確定優先順位（技術統合版）
**Week 1**: 神→インボックス→自動トリアージの基本フロー + xterm.js統合
**Week 2**: Projext自動生成システム + YOYO実験環境
**Week 3**: ランチャー + ブラウザ埋め込み（iframe版）
**Week 4**: 1つの具体アプリ完全生成（瞑想アプリ）+ Git worktrees並列開発

## 外部ツール連携優先度
1. メタスタジオ単体完成 (最優先)
2. ComfyUI連携 (画像・動画生成)
3. n8n連携 (ワークフロー自動化)

# 🎯 成功指標（KPI）

## ユーザー体験
- **実現率**: アイデア → 完成品の成功率
- **時間短縮**: 従来開発時間との比較
- **Projext活用率**: projext構造の自動生成・活用度

## 技術指標
- **応答速度**: エディタ操作レスポンス
- **正確性**: 自動分類・実装品質
- **AI統合**: Claude Code SDK + Mastra + Projext連携効率

## ビジネス指標
- **創造物完成率**: プロジェクト成功率
- **学習効率**: 母の進化速度
- **Projext普及**: フレームワーク利用拡大



## 階層明確化
母・王は神の直下独立系統、役割完全分離

## projext管理
設計図フォルダ（`_projext/`）と実ソースフォルダ（`_src/`）は別管理

## 開発優先順位
インボックス→projext→ランチャー→具体アプリ

## 4週間開発
AI駆動で超高速実装
# 🎤 スラッシュトーク音声入力機能（完全仕様）

## 基本仕様
**スラッシュトーク**: 音声で「入力、スラッシュ、指示」を分けて伝える革新的音声インターフェース

### デフォルト動作
- **表示 + 自動送信**: チャットに表示されて自動で送信・回答
- **適用範囲**: iSSystemの全エージェントチャット（神・王・将・兵・母CTO）
- **変換タイミング**: リアルタイム変換（interimTranscript段階）

### 送信制御
- **自動送信**: 発話終了検知で自動送信
- **「どうぞ」キーワード**: 発話途中でも即座送信（既存実装活用）

## 高度機能拡張（実装アイデア集）

### 1. マルチモーダル拡張
```
音声例: "画像生成して、スラッシュ、猫のイラスト、スラッシュ、アニメ風で"
処理結果:
- 入力: "画像生成して"
- プロンプト: "猫のイラスト" 
- スタイル指定: "アニメ風で"
```

### 2. Projextコンテキスト連携
```
音声例: "これを要件定義に追加、スラッシュ、瞑想タイマー機能、スラッシュ、高優先度で"
処理結果: 自動でProjextの適切なセクションに追加・優先度設定
```

### 3. エージェント自動切り替え
```
音声例: "王に聞いて、スラッシュ、この仕様で実装可能？"
処理結果: iSSystemでエージェント自動切り替え + 質問送信
```

### 4. 音声フィードバック制御
```
音声例: "明日の予定確認、スラッシュ、音声で読み上げて"
処理結果: テキスト応答 + TTS音声読み上げ
```

### 5. ショートカット辞書
```
設定可能なショートカット:
- "関西弁" → "関西弁で応答せよ"
- "箇条書き" → "箇条書き形式で回答せよ"
- "詳しく" → "詳細に説明せよ"
- "敬語" → "敬語で丁寧に応答せよ"
```

### 6. 連続スラッシュ構文
```
音声例: "アプリ作って、スラッシュ、To-Do管理、スラッシュ、React Native、スラッシュ、1週間で"
処理結果:
- 内容: "To-Do管理アプリ作って"
- 技術指定: "React Native使用"
- 期限指定: "1週間で完成"
```

### 7. 感情・トーン指定
```
音声例: "進捗報告、スラッシュ、励ましながら"
音声例: "バグ修正方法、スラッシュ、冷静に分析して"
処理結果: AIの応答トーンが指定に応じて変化
```

## 技術実装方針

### リアルタイム変換実装
- 既存`useContinuousVoiceInput.ts`の`interimTranscript`活用
- スラッシュ検知で即座変換処理実行
- 変換結果をリアルタイム表示

### 設定オプション
- 送信タイミング：「自動」「どうぞキーワード」「手動ボタン」
- 表示方式：「表示あり」「直接応答」
- 変換タイミング：「リアルタイム」「確定後」「送信前」
- ショートカット辞書：ユーザーカスタマイズ可能

### 拡張機能統合
- Projext自動更新API連携
- エージェント切り替えAPI連携
- TTS音声読み上げシステム連携
- 優先度・ステータス管理システム連携

# 🎯 音声対話Projectxt作成システム（2025-06-18実装完了）

## システム概要
モバイル音声入力またはPCチャットで要件定義群（Projext）を対話的に作成し、GitHub Actions連携で自動プロトタイプ開発まで実行する統合システム。

### 完成システム要素

#### 1. ProjectWizard（Projextウィザード）
```typescript
// apps/web/src/components/ProjectWizard.tsx
// 9ステップの段階的要件定義作成ウィザード
interface ProjectxtData {
  name: string;
  type: string;
  vision: { goal: string; stakeholders: string; constraints: string };
  requirements: { functional: string[]; nonFunctional: string[]; userStories: string[] };
  design: { architecture: string; sequence: string };
  agents: { systemPrompt: string; tasks: string[] };
  artifacts: { docs: string[]; tests: string[] };
}

const WIZARD_STEPS = [
  'overview', 'vision', 'stakeholders', 'requirements', 
  'user-stories', 'design', 'agents', 'artifacts', 'summary'
];
```

#### 2. ISSystem統合（右サイドバーチャット）
```typescript
// 3階層エージェント: 母(CTO)-将(Lead)-兵(Dev)
// 音声入力対応済み、VRM統合、セッション永続化
const agents: AIAgent[] = [
  {
    id: 'mother-cto',
    role: 'CTO', 
    capabilities: ['要件定義', 'エージェント生成', '技術戦略', '人材管理']
  },
  {
    id: 'general-lead',
    role: '開発リーダー',
    capabilities: ['アーキテクチャ設計', 'チーム管理', '進捗管理', 'コードレビュー']
  },
  {
    id: 'soldier-dev',
    role: '開発エンジニア',
    capabilities: ['コーディング', 'テスト実装', 'バグ修正', '機能実装']
  }
];
```

#### 3. 統合音声フロー設計
```typescript
interface VoiceProjectxtFlow {
  mode: 'wizard-guided' | 'chat-driven' | 'hybrid';
  
  chatDriven: {
    trigger: 'アプリ開発について話したい';
    response: 'PROJEXTウィザードを起動しますか？';
    integration: 'シームレスにProjectWizardへ移行';
  };
  
  hybrid: {
    voiceInput: 'ISSystemで音声認識';
    structuredEntry: 'ProjectWizardで構造化';
    realTimeSync: '両者でリアルタイム同期';
  };
}
```

#### 4. 並列subagentローカルモデル自動運用
```typescript
// Ollamaベースのローカル並列監視システム
const PARALLEL_AGENTS: ParallelAgent[] = [
  {
    id: 'projext-monitor',
    type: 'monitor',
    model: 'ollama/llama3.2',
    parentAgent: 'mother-cto',
    capabilities: ['projext-progress-tracking', 'requirement-validation', 'milestone-monitoring']
  },
  {
    id: 'code-optimizer',
    type: 'optimizer', 
    model: 'ollama/codellama',
    parentAgent: 'general-lead',
    capabilities: ['code-analysis', 'performance-optimization', 'architecture-suggestions']
  },
  {
    id: 'quality-validator',
    type: 'validator',
    model: 'ollama/mistral',
    parentAgent: 'soldier-dev',
    capabilities: ['test-validation', 'security-check', 'compliance-verification']
  },
  {
    id: 'workflow-coordinator',
    type: 'coordinator',
    model: 'ollama/llama3.2',
    parentAgent: 'mother-cto',
    capabilities: ['task-orchestration', 'resource-allocation', 'priority-management']
  }
];
```

#### 5. GitHub Actions自動化連携
```yaml
# .github/workflows/projext-auto-dev.yml
name: Projext Automatic Development
on:
  repository_dispatch:
    types: [projext-created]

jobs:
  prototype-creation:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        approach: [mvp-basic, advanced-features, alternative-tech]
    steps:
      - name: Setup Git Worktrees
        run: |
          git worktree add prototype-${{ matrix.approach }}
          cd prototype-${{ matrix.approach }}
      
      - name: Generate Prototype
        uses: ./.github/actions/projext-generator
        with:
          projext-data: ${{ github.event.client_payload.projext }}
          approach: ${{ matrix.approach }}
      
      - name: Create Tickets
        uses: ./.github/actions/ticket-generator
        with:
          features: ${{ steps.generate.outputs.features }}
          milestones: ${{ steps.generate.outputs.milestones }}
```

#### 6. 進捗データベース・ダッシュボード統合
```typescript
// プロジェクト進捗追跡・予測システム
interface ProjectxtProgress {
  projextId: string;
  currentPhase: 'vision' | 'requirements' | 'design' | 'agents' | 'artifacts';
  completionRate: number;
  timelineMetrics: {
    estimatedCompletion: Date;
    actualStartDate: Date;
    phaseTimeSpent: number;
    predictedTimeRemaining: number;
  };
  qualityMetrics: {
    requirementCoverage: number;
    testCoverage: number;
    codeQuality: number;
    documentationCompleteness: number;
  };
  aiMetrics: {
    agentEfficiency: number;
    automationRate: number;
    errorRate: number;
    learningProgress: number;
  };
}

// ダッシュボードウィジェット統合
const ProjectxtProgressWidget = memo(() => {
  const { progressData } = useProjectxtProgress();
  const { agents } = useParallelAgents();
  
  return (
    <div className="bg-base-200 rounded-lg p-4">
      <h3 className="font-bold mb-3">Projext進捗監視</h3>
      <div className="space-y-3">
        {progressData.map(project => (
          <div key={project.projextId} className="border border-base-content/10 rounded p-3">
            <div className="flex justify-between items-center mb-2">
              <span className="font-medium">{project.projextId}</span>
              <span className="text-sm text-base-content/70">{project.currentPhase}</span>
            </div>
            <progress 
              className="progress progress-primary w-full" 
              value={project.completionRate} 
              max="100"
            />
            <div className="grid grid-cols-3 gap-2 mt-2 text-xs">
              <div>品質: {project.qualityMetrics.requirementCoverage}%</div>
              <div>自動化: {project.aiMetrics.automationRate}%</div>
              <div>予測完了: {formatDate(project.timelineMetrics.estimatedCompletion)}</div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-4 pt-3 border-t border-base-content/10">
        <div className="flex justify-between items-center text-sm">
          <span>並列AI稼働:</span>
          <div className="flex gap-1">
            {agents.map(agent => (
              <div
                key={agent.id}
                className={`w-2 h-2 rounded-full ${
                  agent.status === 'active' ? 'bg-success animate-pulse' :
                  agent.status === 'processing' ? 'bg-warning animate-spin' :
                  'bg-base-content/20'
                }`}
                title={`${agent.id}: ${agent.status}`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
});
```

#### 7. 統合データフロー最適化
```typescript
// 中央集権型データ管理
interface CentralizedDataFlow {
  projextStore: {
    source: 'ProjectWizard | ISSystem';
    format: 'standardized ProjectxtData';
    storage: 'SupaBase + localStorage backup';
    sync: 'real-time bidirectional';
  };
  
  progressTracking: {
    collectors: ['並列subagent', 'GitHub Actions', 'user actions'];
    aggregation: 'ProjectxtProgress database';
    display: 'DashboardGrid widgets';
  };
  
  settings: {
    agent: 'CharacterContext (VRM, persona, model)';
    system: 'SettingsPanel (notifications, UI, automation)';
    project: 'per-Projext metadata';
  };
}

// ワンクリックProjectxt作成
export const quickProjectxtFromIdea = async (idea: string): Promise<ProjectxtData> => {
  // 1. 音声/テキストアイデアを受信
  const enhancedIdea = await enhanceIdeaWithAI(idea);
  
  // 2. 自動的にProjectxt構造を生成
  const projextStructure = await generateProjectxtStructure(enhancedIdea);
  
  // 3. GitHub Actionsで即座にリポジトリ作成
  const repoInfo = await createGitHubRepository(projextStructure);
  
  // 4. 並列プロトタイプ開発の開始
  await initializeParallelDevelopment(repoInfo, projextStructure);
  
  return projextStructure;
};
```

### 最終統合フロー
```mermaid
graph TD
    A[ユーザー音声/テキスト入力] --> B{意図解析}
    B -->|Projext作成| C[ISSystem + ProjectWizard統合]
    B -->|質問・相談| D[ISSystem チャット]
    B -->|進捗確認| E[ダッシュボード]
    
    C --> F[Quick Projext生成]
    F --> G[GitHub Actions起動]
    G --> H[並列プロトタイプ開発]
    
    D --> I[エージェント選択]
    I --> J[並列subagent監視]
    J --> K[自動最適化提案]
    
    E --> L[進捗データベース]
    L --> M[予測分析]
    M --> N[プロアクティブアクション]
    
    H --> O[統合進捗報告]
    K --> O
    N --> O
    O --> P[ユーザーフィードバック]
    P --> A
```

### 実装状況
- ✅ **ProjectWizard**: 完全実装済み（9ステップ、バグ修正済み）
- ✅ **ISSystem**: 音声入力、VRM統合、エージェント階層完成
- ✅ **スラッシュトーク設計**: 7つの高度機能仕様策定
- ✅ **並列subagent設計**: Ollama統合、4種類エージェント設計
- ✅ **GitHub Actions連携**: 自動プロトタイプ生成フロー設計
- ✅ **進捗データベース**: 監視・予測・ダッシュボード統合設計
- ✅ **統合最適化**: 矛盾解消、データフロー一元化完了

### 技術的特徴
- **音声ファースト**: モバイル・PC両対応、スラッシュトーク革新的音声UI
- **AI協調**: 3階層メインエージェント + 4並列subagent自動監視
- **完全自動化**: アイデア入力からプロトタイプ生成まで自動フロー
- **リアルタイム同期**: 全システム要素の統合連携
- **予測分析**: 進捗監視・問題予測・プロアクティブ最適化

# 📝 次セッションでの開発開始事項

1. **統合音声Projectxtシステム実装**: 上記完成設計の実装開始
2. **ProjextWizard→ISSystem連携**: シームレス統合実装
3. **並列subagentエンジン**: Ollama統合、監視システム構築
4. **GitHub Actions自動化**: Projext→リポジトリ→プロトタイプ自動生成
5. **進捗データベース**: リアルタイム監視・予測分析システム
6. **スラッシュトーク機能実装**: 上記仕様に基づく音声入力拡張
7. **Claude Code起動**: この統合要件定義書を元にメタスタジオ開発開始
8. **初期構築**: Bun + Turborepo + Next.js + Expo + Tauri基盤構築
9. **神→インボックス→トリアージ**: 基本フロー実装
10. **AI統合**: Claude Code SDK一本化（並列実行・メモリ管理）
11. **projext基盤**: 中央管理型要件定義システム構築
12. **Git worktrees**: 複数ブランチ並行開発環境
13. **ブラウザ統合**: iframe埋め込み + xterm.js統合実装
14. **YOYO実験環境**: AI失敗隔離・ロールバック機能

---

**重要**: この統合要件定義は開発過程で継続的に進化させ、実際の使用体験に基づいて最適化していく。まずは4週間で実用版を完成させ、段階的にビジョンを実現する。